# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.o: KNoteDo_autogen/mocs_compilation.cpp \
  /home/<USER>/CLionProjects/KNoteDo/app/MainWindow.h \
  /home/<USER>/CLionProjects/KNoteDo/app/RichTextEditor.h \
  /home/<USER>/CLionProjects/KNoteDo/app/SettingsDialog.h \
  KNoteDo_autogen/VJIZ3MDCXP/moc_MainWindow.cpp \
  KNoteDo_autogen/VJIZ3MDCXP/moc_RichTextEditor.cpp \
  KNoteDo_autogen/VJIZ3MDCXP/moc_SettingsDialog.cpp \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/posix_types_64.h \
  /usr/include/asm/types.h \
  /usr/include/asm/unistd.h \
  /usr/include/asm/unistd_64.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/flt-eval-method.h \
  /usr/include/bits/fp-fast.h \
  /usr/include/bits/fp-logb.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/iscanonical.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/libm-simd-decl-stubs.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/locale.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/math-vector.h \
  /usr/include/bits/mathcalls-helper-functions.h \
  /usr/include/bits/mathcalls-macros.h \
  /usr/include/bits/mathcalls-narrow.h \
  /usr/include/bits/mathcalls.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/syscall.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/mbstate_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/types/wint_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wctype-wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/c++/15/algorithm \
  /usr/include/c++/15/array \
  /usr/include/c++/15/atomic \
  /usr/include/c++/15/backward/auto_ptr.h \
  /usr/include/c++/15/backward/binders.h \
  /usr/include/c++/15/bit \
  /usr/include/c++/15/bits/algorithmfwd.h \
  /usr/include/c++/15/bits/align.h \
  /usr/include/c++/15/bits/alloc_traits.h \
  /usr/include/c++/15/bits/allocated_ptr.h \
  /usr/include/c++/15/bits/allocator.h \
  /usr/include/c++/15/bits/atomic_base.h \
  /usr/include/c++/15/bits/atomic_lockfree_defines.h \
  /usr/include/c++/15/bits/atomic_wait.h \
  /usr/include/c++/15/bits/basic_ios.h \
  /usr/include/c++/15/bits/basic_ios.tcc \
  /usr/include/c++/15/bits/basic_string.h \
  /usr/include/c++/15/bits/basic_string.tcc \
  /usr/include/c++/15/bits/char_traits.h \
  /usr/include/c++/15/bits/charconv.h \
  /usr/include/c++/15/bits/chrono.h \
  /usr/include/c++/15/bits/chrono_io.h \
  /usr/include/c++/15/bits/codecvt.h \
  /usr/include/c++/15/bits/concept_check.h \
  /usr/include/c++/15/bits/cpp_type_traits.h \
  /usr/include/c++/15/bits/cxxabi_forced.h \
  /usr/include/c++/15/bits/cxxabi_init_exception.h \
  /usr/include/c++/15/bits/enable_special_members.h \
  /usr/include/c++/15/bits/erase_if.h \
  /usr/include/c++/15/bits/exception.h \
  /usr/include/c++/15/bits/exception_defines.h \
  /usr/include/c++/15/bits/exception_ptr.h \
  /usr/include/c++/15/bits/formatfwd.h \
  /usr/include/c++/15/bits/functexcept.h \
  /usr/include/c++/15/bits/functional_hash.h \
  /usr/include/c++/15/bits/hash_bytes.h \
  /usr/include/c++/15/bits/hashtable.h \
  /usr/include/c++/15/bits/hashtable_policy.h \
  /usr/include/c++/15/bits/invoke.h \
  /usr/include/c++/15/bits/ios_base.h \
  /usr/include/c++/15/bits/istream.tcc \
  /usr/include/c++/15/bits/iterator_concepts.h \
  /usr/include/c++/15/bits/list.tcc \
  /usr/include/c++/15/bits/locale_classes.h \
  /usr/include/c++/15/bits/locale_classes.tcc \
  /usr/include/c++/15/bits/locale_conv.h \
  /usr/include/c++/15/bits/locale_facets.h \
  /usr/include/c++/15/bits/locale_facets.tcc \
  /usr/include/c++/15/bits/locale_facets_nonio.h \
  /usr/include/c++/15/bits/locale_facets_nonio.tcc \
  /usr/include/c++/15/bits/localefwd.h \
  /usr/include/c++/15/bits/max_size_type.h \
  /usr/include/c++/15/bits/memory_resource.h \
  /usr/include/c++/15/bits/memoryfwd.h \
  /usr/include/c++/15/bits/monostate.h \
  /usr/include/c++/15/bits/move.h \
  /usr/include/c++/15/bits/nested_exception.h \
  /usr/include/c++/15/bits/new_allocator.h \
  /usr/include/c++/15/bits/node_handle.h \
  /usr/include/c++/15/bits/ostream.h \
  /usr/include/c++/15/bits/ostream.tcc \
  /usr/include/c++/15/bits/ostream_insert.h \
  /usr/include/c++/15/bits/parse_numbers.h \
  /usr/include/c++/15/bits/postypes.h \
  /usr/include/c++/15/bits/predefined_ops.h \
  /usr/include/c++/15/bits/ptr_traits.h \
  /usr/include/c++/15/bits/quoted_string.h \
  /usr/include/c++/15/bits/range_access.h \
  /usr/include/c++/15/bits/ranges_algo.h \
  /usr/include/c++/15/bits/ranges_algobase.h \
  /usr/include/c++/15/bits/ranges_base.h \
  /usr/include/c++/15/bits/ranges_cmp.h \
  /usr/include/c++/15/bits/ranges_uninitialized.h \
  /usr/include/c++/15/bits/ranges_util.h \
  /usr/include/c++/15/bits/refwrap.h \
  /usr/include/c++/15/bits/requires_hosted.h \
  /usr/include/c++/15/bits/shared_ptr.h \
  /usr/include/c++/15/bits/shared_ptr_atomic.h \
  /usr/include/c++/15/bits/shared_ptr_base.h \
  /usr/include/c++/15/bits/specfun.h \
  /usr/include/c++/15/bits/sstream.tcc \
  /usr/include/c++/15/bits/std_abs.h \
  /usr/include/c++/15/bits/std_function.h \
  /usr/include/c++/15/bits/std_mutex.h \
  /usr/include/c++/15/bits/stl_algo.h \
  /usr/include/c++/15/bits/stl_algobase.h \
  /usr/include/c++/15/bits/stl_bvector.h \
  /usr/include/c++/15/bits/stl_construct.h \
  /usr/include/c++/15/bits/stl_function.h \
  /usr/include/c++/15/bits/stl_heap.h \
  /usr/include/c++/15/bits/stl_iterator.h \
  /usr/include/c++/15/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/15/bits/stl_iterator_base_types.h \
  /usr/include/c++/15/bits/stl_list.h \
  /usr/include/c++/15/bits/stl_map.h \
  /usr/include/c++/15/bits/stl_multimap.h \
  /usr/include/c++/15/bits/stl_multiset.h \
  /usr/include/c++/15/bits/stl_numeric.h \
  /usr/include/c++/15/bits/stl_pair.h \
  /usr/include/c++/15/bits/stl_raw_storage_iter.h \
  /usr/include/c++/15/bits/stl_relops.h \
  /usr/include/c++/15/bits/stl_set.h \
  /usr/include/c++/15/bits/stl_tempbuf.h \
  /usr/include/c++/15/bits/stl_tree.h \
  /usr/include/c++/15/bits/stl_uninitialized.h \
  /usr/include/c++/15/bits/stl_vector.h \
  /usr/include/c++/15/bits/stream_iterator.h \
  /usr/include/c++/15/bits/streambuf.tcc \
  /usr/include/c++/15/bits/streambuf_iterator.h \
  /usr/include/c++/15/bits/string_view.tcc \
  /usr/include/c++/15/bits/stringfwd.h \
  /usr/include/c++/15/bits/unicode-data.h \
  /usr/include/c++/15/bits/unicode.h \
  /usr/include/c++/15/bits/uniform_int_dist.h \
  /usr/include/c++/15/bits/unique_ptr.h \
  /usr/include/c++/15/bits/unordered_map.h \
  /usr/include/c++/15/bits/unordered_set.h \
  /usr/include/c++/15/bits/uses_allocator.h \
  /usr/include/c++/15/bits/uses_allocator_args.h \
  /usr/include/c++/15/bits/utility.h \
  /usr/include/c++/15/bits/vector.tcc \
  /usr/include/c++/15/bits/version.h \
  /usr/include/c++/15/cassert \
  /usr/include/c++/15/cctype \
  /usr/include/c++/15/cerrno \
  /usr/include/c++/15/charconv \
  /usr/include/c++/15/chrono \
  /usr/include/c++/15/climits \
  /usr/include/c++/15/clocale \
  /usr/include/c++/15/cmath \
  /usr/include/c++/15/compare \
  /usr/include/c++/15/concepts \
  /usr/include/c++/15/cstddef \
  /usr/include/c++/15/cstdint \
  /usr/include/c++/15/cstdio \
  /usr/include/c++/15/cstdlib \
  /usr/include/c++/15/cstring \
  /usr/include/c++/15/ctime \
  /usr/include/c++/15/cwchar \
  /usr/include/c++/15/cwctype \
  /usr/include/c++/15/debug/assertions.h \
  /usr/include/c++/15/debug/debug.h \
  /usr/include/c++/15/exception \
  /usr/include/c++/15/ext/aligned_buffer.h \
  /usr/include/c++/15/ext/alloc_traits.h \
  /usr/include/c++/15/ext/atomicity.h \
  /usr/include/c++/15/ext/concurrence.h \
  /usr/include/c++/15/ext/numeric_traits.h \
  /usr/include/c++/15/ext/string_conversions.h \
  /usr/include/c++/15/ext/type_traits.h \
  /usr/include/c++/15/format \
  /usr/include/c++/15/functional \
  /usr/include/c++/15/initializer_list \
  /usr/include/c++/15/iomanip \
  /usr/include/c++/15/ios \
  /usr/include/c++/15/iosfwd \
  /usr/include/c++/15/istream \
  /usr/include/c++/15/iterator \
  /usr/include/c++/15/limits \
  /usr/include/c++/15/list \
  /usr/include/c++/15/locale \
  /usr/include/c++/15/map \
  /usr/include/c++/15/memory \
  /usr/include/c++/15/new \
  /usr/include/c++/15/numbers \
  /usr/include/c++/15/numeric \
  /usr/include/c++/15/optional \
  /usr/include/c++/15/ostream \
  /usr/include/c++/15/pstl/execution_defs.h \
  /usr/include/c++/15/pstl/glue_algorithm_defs.h \
  /usr/include/c++/15/pstl/glue_memory_defs.h \
  /usr/include/c++/15/pstl/glue_numeric_defs.h \
  /usr/include/c++/15/pstl/pstl_config.h \
  /usr/include/c++/15/ratio \
  /usr/include/c++/15/set \
  /usr/include/c++/15/span \
  /usr/include/c++/15/sstream \
  /usr/include/c++/15/stdexcept \
  /usr/include/c++/15/stdlib.h \
  /usr/include/c++/15/streambuf \
  /usr/include/c++/15/string \
  /usr/include/c++/15/string_view \
  /usr/include/c++/15/system_error \
  /usr/include/c++/15/tr1/bessel_function.tcc \
  /usr/include/c++/15/tr1/beta_function.tcc \
  /usr/include/c++/15/tr1/ell_integral.tcc \
  /usr/include/c++/15/tr1/exp_integral.tcc \
  /usr/include/c++/15/tr1/gamma.tcc \
  /usr/include/c++/15/tr1/hypergeometric.tcc \
  /usr/include/c++/15/tr1/legendre_function.tcc \
  /usr/include/c++/15/tr1/modified_bessel_func.tcc \
  /usr/include/c++/15/tr1/poly_hermite.tcc \
  /usr/include/c++/15/tr1/poly_laguerre.tcc \
  /usr/include/c++/15/tr1/riemann_zeta.tcc \
  /usr/include/c++/15/tr1/special_function_util.h \
  /usr/include/c++/15/tuple \
  /usr/include/c++/15/type_traits \
  /usr/include/c++/15/typeinfo \
  /usr/include/c++/15/unordered_map \
  /usr/include/c++/15/unordered_set \
  /usr/include/c++/15/utility \
  /usr/include/c++/15/variant \
  /usr/include/c++/15/vector \
  /usr/include/c++/15/version \
  /usr/include/c++/15/x86_64-redhat-linux/bits/atomic_word.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/c++allocator.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/c++config.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/c++locale.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/cpu_defines.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/ctype_base.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/ctype_inline.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/error_constants.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/gthr-default.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/gthr.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/messages_members.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/os_defines.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/time_members.h \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/libintl.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/locale.h \
  /usr/include/math.h \
  /usr/include/pthread.h \
  /usr/include/qt6/QtCore/QTimer \
  /usr/include/qt6/QtCore/q17memory.h \
  /usr/include/qt6/QtCore/q20algorithm.h \
  /usr/include/qt6/QtCore/q20functional.h \
  /usr/include/qt6/QtCore/q20iterator.h \
  /usr/include/qt6/QtCore/q20memory.h \
  /usr/include/qt6/QtCore/q20type_traits.h \
  /usr/include/qt6/QtCore/q20utility.h \
  /usr/include/qt6/QtCore/q23type_traits.h \
  /usr/include/qt6/QtCore/q23utility.h \
  /usr/include/qt6/QtCore/qabstracteventdispatcher.h \
  /usr/include/qt6/QtCore/qabstractitemmodel.h \
  /usr/include/qt6/QtCore/qalgorithms.h \
  /usr/include/qt6/QtCore/qanystringview.h \
  /usr/include/qt6/QtCore/qarraydata.h \
  /usr/include/qt6/QtCore/qarraydataops.h \
  /usr/include/qt6/QtCore/qarraydatapointer.h \
  /usr/include/qt6/QtCore/qassert.h \
  /usr/include/qt6/QtCore/qatomic.h \
  /usr/include/qt6/QtCore/qatomic_cxx11.h \
  /usr/include/qt6/QtCore/qbasicatomic.h \
  /usr/include/qt6/QtCore/qbasictimer.h \
  /usr/include/qt6/QtCore/qbindingstorage.h \
  /usr/include/qt6/QtCore/qbytearray.h \
  /usr/include/qt6/QtCore/qbytearrayalgorithms.h \
  /usr/include/qt6/QtCore/qbytearraylist.h \
  /usr/include/qt6/QtCore/qbytearrayview.h \
  /usr/include/qt6/QtCore/qchar.h \
  /usr/include/qt6/QtCore/qcompare.h \
  /usr/include/qt6/QtCore/qcompare_impl.h \
  /usr/include/qt6/QtCore/qcomparehelpers.h \
  /usr/include/qt6/QtCore/qcompilerdetection.h \
  /usr/include/qt6/QtCore/qconfig-64.h \
  /usr/include/qt6/QtCore/qconfig.h \
  /usr/include/qt6/QtCore/qconstructormacros.h \
  /usr/include/qt6/QtCore/qcontainerfwd.h \
  /usr/include/qt6/QtCore/qcontainerinfo.h \
  /usr/include/qt6/QtCore/qcontainertools_impl.h \
  /usr/include/qt6/QtCore/qcontiguouscache.h \
  /usr/include/qt6/QtCore/qdarwinhelpers.h \
  /usr/include/qt6/QtCore/qdatastream.h \
  /usr/include/qt6/QtCore/qdeadlinetimer.h \
  /usr/include/qt6/QtCore/qdebug.h \
  /usr/include/qt6/QtCore/qelapsedtimer.h \
  /usr/include/qt6/QtCore/qendian.h \
  /usr/include/qt6/QtCore/qeventloop.h \
  /usr/include/qt6/QtCore/qexceptionhandling.h \
  /usr/include/qt6/QtCore/qflags.h \
  /usr/include/qt6/QtCore/qfloat16.h \
  /usr/include/qt6/QtCore/qforeach.h \
  /usr/include/qt6/QtCore/qfunctionaltools_impl.h \
  /usr/include/qt6/QtCore/qfunctionpointer.h \
  /usr/include/qt6/QtCore/qgenericatomic.h \
  /usr/include/qt6/QtCore/qglobal.h \
  /usr/include/qt6/QtCore/qglobalstatic.h \
  /usr/include/qt6/QtCore/qhash.h \
  /usr/include/qt6/QtCore/qhashfunctions.h \
  /usr/include/qt6/QtCore/qiodevicebase.h \
  /usr/include/qt6/QtCore/qitemselectionmodel.h \
  /usr/include/qt6/QtCore/qiterable.h \
  /usr/include/qt6/QtCore/qiterator.h \
  /usr/include/qt6/QtCore/qlatin1stringview.h \
  /usr/include/qt6/QtCore/qline.h \
  /usr/include/qt6/QtCore/qlist.h \
  /usr/include/qt6/QtCore/qlocale.h \
  /usr/include/qt6/QtCore/qlogging.h \
  /usr/include/qt6/QtCore/qmalloc.h \
  /usr/include/qt6/QtCore/qmap.h \
  /usr/include/qt6/QtCore/qmargins.h \
  /usr/include/qt6/QtCore/qmath.h \
  /usr/include/qt6/QtCore/qmetacontainer.h \
  /usr/include/qt6/QtCore/qmetatype.h \
  /usr/include/qt6/QtCore/qminmax.h \
  /usr/include/qt6/QtCore/qnamespace.h \
  /usr/include/qt6/QtCore/qnumeric.h \
  /usr/include/qt6/QtCore/qobject.h \
  /usr/include/qt6/QtCore/qobject_impl.h \
  /usr/include/qt6/QtCore/qobjectdefs.h \
  /usr/include/qt6/QtCore/qobjectdefs_impl.h \
  /usr/include/qt6/QtCore/qoverload.h \
  /usr/include/qt6/QtCore/qpair.h \
  /usr/include/qt6/QtCore/qpoint.h \
  /usr/include/qt6/QtCore/qprocessordetection.h \
  /usr/include/qt6/QtCore/qrect.h \
  /usr/include/qt6/QtCore/qrefcount.h \
  /usr/include/qt6/QtCore/qregularexpression.h \
  /usr/include/qt6/QtCore/qscopedpointer.h \
  /usr/include/qt6/QtCore/qscopeguard.h \
  /usr/include/qt6/QtCore/qset.h \
  /usr/include/qt6/QtCore/qshareddata.h \
  /usr/include/qt6/QtCore/qshareddata_impl.h \
  /usr/include/qt6/QtCore/qsharedpointer.h \
  /usr/include/qt6/QtCore/qsharedpointer_impl.h \
  /usr/include/qt6/QtCore/qsize.h \
  /usr/include/qt6/QtCore/qspan.h \
  /usr/include/qt6/QtCore/qstdlibdetection.h \
  /usr/include/qt6/QtCore/qstring.h \
  /usr/include/qt6/QtCore/qstringalgorithms.h \
  /usr/include/qt6/QtCore/qstringbuilder.h \
  /usr/include/qt6/QtCore/qstringconverter.h \
  /usr/include/qt6/QtCore/qstringconverter_base.h \
  /usr/include/qt6/QtCore/qstringfwd.h \
  /usr/include/qt6/QtCore/qstringlist.h \
  /usr/include/qt6/QtCore/qstringliteral.h \
  /usr/include/qt6/QtCore/qstringmatcher.h \
  /usr/include/qt6/QtCore/qstringtokenizer.h \
  /usr/include/qt6/QtCore/qstringview.h \
  /usr/include/qt6/QtCore/qswap.h \
  /usr/include/qt6/QtCore/qsysinfo.h \
  /usr/include/qt6/QtCore/qsystemdetection.h \
  /usr/include/qt6/QtCore/qtaggedpointer.h \
  /usr/include/qt6/QtCore/qtclasshelpermacros.h \
  /usr/include/qt6/QtCore/qtconfiginclude.h \
  /usr/include/qt6/QtCore/qtconfigmacros.h \
  /usr/include/qt6/QtCore/qtcore-config.h \
  /usr/include/qt6/QtCore/qtcoreexports.h \
  /usr/include/qt6/QtCore/qtcoreglobal.h \
  /usr/include/qt6/QtCore/qtdeprecationdefinitions.h \
  /usr/include/qt6/QtCore/qtdeprecationmarkers.h \
  /usr/include/qt6/QtCore/qtenvironmentvariables.h \
  /usr/include/qt6/QtCore/qtextstream.h \
  /usr/include/qt6/QtCore/qtformat_impl.h \
  /usr/include/qt6/QtCore/qtimer.h \
  /usr/include/qt6/QtCore/qtmetamacros.h \
  /usr/include/qt6/QtCore/qtmocconstants.h \
  /usr/include/qt6/QtCore/qtmochelpers.h \
  /usr/include/qt6/QtCore/qtnoop.h \
  /usr/include/qt6/QtCore/qtpreprocessorsupport.h \
  /usr/include/qt6/QtCore/qtresource.h \
  /usr/include/qt6/QtCore/qttranslation.h \
  /usr/include/qt6/QtCore/qttypetraits.h \
  /usr/include/qt6/QtCore/qtversion.h \
  /usr/include/qt6/QtCore/qtversionchecks.h \
  /usr/include/qt6/QtCore/qtypeinfo.h \
  /usr/include/qt6/QtCore/qtypes.h \
  /usr/include/qt6/QtCore/qurl.h \
  /usr/include/qt6/QtCore/qutf8stringview.h \
  /usr/include/qt6/QtCore/qvariant.h \
  /usr/include/qt6/QtCore/qvarlengtharray.h \
  /usr/include/qt6/QtCore/qversiontagging.h \
  /usr/include/qt6/QtCore/qxptype_traits.h \
  /usr/include/qt6/QtCore/qyieldcpu.h \
  /usr/include/qt6/QtGui/qaction.h \
  /usr/include/qt6/QtGui/qbitmap.h \
  /usr/include/qt6/QtGui/qbrush.h \
  /usr/include/qt6/QtGui/qcolor.h \
  /usr/include/qt6/QtGui/qcursor.h \
  /usr/include/qt6/QtGui/qfont.h \
  /usr/include/qt6/QtGui/qfontinfo.h \
  /usr/include/qt6/QtGui/qfontmetrics.h \
  /usr/include/qt6/QtGui/qfontvariableaxis.h \
  /usr/include/qt6/QtGui/qicon.h \
  /usr/include/qt6/QtGui/qimage.h \
  /usr/include/qt6/QtGui/qkeysequence.h \
  /usr/include/qt6/QtGui/qpaintdevice.h \
  /usr/include/qt6/QtGui/qpalette.h \
  /usr/include/qt6/QtGui/qpen.h \
  /usr/include/qt6/QtGui/qpixelformat.h \
  /usr/include/qt6/QtGui/qpixmap.h \
  /usr/include/qt6/QtGui/qpolygon.h \
  /usr/include/qt6/QtGui/qregion.h \
  /usr/include/qt6/QtGui/qrgb.h \
  /usr/include/qt6/QtGui/qrgba64.h \
  /usr/include/qt6/QtGui/qtextcursor.h \
  /usr/include/qt6/QtGui/qtextdocument.h \
  /usr/include/qt6/QtGui/qtextformat.h \
  /usr/include/qt6/QtGui/qtextoption.h \
  /usr/include/qt6/QtGui/qtgui-config.h \
  /usr/include/qt6/QtGui/qtguiexports.h \
  /usr/include/qt6/QtGui/qtguiglobal.h \
  /usr/include/qt6/QtGui/qtransform.h \
  /usr/include/qt6/QtGui/qvalidator.h \
  /usr/include/qt6/QtGui/qwindowdefs.h \
  /usr/include/qt6/QtWidgets/QDialog \
  /usr/include/qt6/QtWidgets/QListWidgetItem \
  /usr/include/qt6/QtWidgets/QMainWindow \
  /usr/include/qt6/QtWidgets/QTextEdit \
  /usr/include/qt6/QtWidgets/QToolBar \
  /usr/include/qt6/QtWidgets/qabstractitemdelegate.h \
  /usr/include/qt6/QtWidgets/qabstractitemview.h \
  /usr/include/qt6/QtWidgets/qabstractscrollarea.h \
  /usr/include/qt6/QtWidgets/qabstractslider.h \
  /usr/include/qt6/QtWidgets/qabstractspinbox.h \
  /usr/include/qt6/QtWidgets/qdialog.h \
  /usr/include/qt6/QtWidgets/qframe.h \
  /usr/include/qt6/QtWidgets/qlistview.h \
  /usr/include/qt6/QtWidgets/qlistwidget.h \
  /usr/include/qt6/QtWidgets/qmainwindow.h \
  /usr/include/qt6/QtWidgets/qrubberband.h \
  /usr/include/qt6/QtWidgets/qsizepolicy.h \
  /usr/include/qt6/QtWidgets/qslider.h \
  /usr/include/qt6/QtWidgets/qstyle.h \
  /usr/include/qt6/QtWidgets/qstyleoption.h \
  /usr/include/qt6/QtWidgets/qtabbar.h \
  /usr/include/qt6/QtWidgets/qtabwidget.h \
  /usr/include/qt6/QtWidgets/qtextedit.h \
  /usr/include/qt6/QtWidgets/qtoolbar.h \
  /usr/include/qt6/QtWidgets/qtwidgets-config.h \
  /usr/include/qt6/QtWidgets/qtwidgetsexports.h \
  /usr/include/qt6/QtWidgets/qtwidgetsglobal.h \
  /usr/include/qt6/QtWidgets/qwidget.h \
  /usr/include/sched.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/select.h \
  /usr/include/sys/single_threaded.h \
  /usr/include/sys/syscall.h \
  /usr/include/sys/types.h \
  /usr/include/syscall.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/include/wchar.h \
  /usr/include/wctype.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/limits.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stdarg.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stdbool.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stddef.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stdint.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/syslimits.h

CMakeFiles/KNoteDo.dir/app/AppleNotesTheme.cpp.o: /home/<USER>/CLionProjects/KNoteDo/app/AppleNotesTheme.cpp \
  /home/<USER>/CLionProjects/KNoteDo/app/AppleNotesTheme.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/posix_types_64.h \
  /usr/include/asm/types.h \
  /usr/include/asm/unistd.h \
  /usr/include/asm/unistd_64.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/flt-eval-method.h \
  /usr/include/bits/fp-fast.h \
  /usr/include/bits/fp-logb.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/iscanonical.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/libm-simd-decl-stubs.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/locale.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/math-vector.h \
  /usr/include/bits/mathcalls-helper-functions.h \
  /usr/include/bits/mathcalls-macros.h \
  /usr/include/bits/mathcalls-narrow.h \
  /usr/include/bits/mathcalls.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/syscall.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/mbstate_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/types/wint_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wctype-wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/c++/15/algorithm \
  /usr/include/c++/15/array \
  /usr/include/c++/15/atomic \
  /usr/include/c++/15/backward/auto_ptr.h \
  /usr/include/c++/15/backward/binders.h \
  /usr/include/c++/15/bit \
  /usr/include/c++/15/bits/algorithmfwd.h \
  /usr/include/c++/15/bits/align.h \
  /usr/include/c++/15/bits/alloc_traits.h \
  /usr/include/c++/15/bits/allocated_ptr.h \
  /usr/include/c++/15/bits/allocator.h \
  /usr/include/c++/15/bits/atomic_base.h \
  /usr/include/c++/15/bits/atomic_lockfree_defines.h \
  /usr/include/c++/15/bits/atomic_wait.h \
  /usr/include/c++/15/bits/basic_ios.h \
  /usr/include/c++/15/bits/basic_ios.tcc \
  /usr/include/c++/15/bits/basic_string.h \
  /usr/include/c++/15/bits/basic_string.tcc \
  /usr/include/c++/15/bits/char_traits.h \
  /usr/include/c++/15/bits/charconv.h \
  /usr/include/c++/15/bits/chrono.h \
  /usr/include/c++/15/bits/chrono_io.h \
  /usr/include/c++/15/bits/codecvt.h \
  /usr/include/c++/15/bits/concept_check.h \
  /usr/include/c++/15/bits/cpp_type_traits.h \
  /usr/include/c++/15/bits/cxxabi_forced.h \
  /usr/include/c++/15/bits/cxxabi_init_exception.h \
  /usr/include/c++/15/bits/enable_special_members.h \
  /usr/include/c++/15/bits/erase_if.h \
  /usr/include/c++/15/bits/exception.h \
  /usr/include/c++/15/bits/exception_defines.h \
  /usr/include/c++/15/bits/exception_ptr.h \
  /usr/include/c++/15/bits/formatfwd.h \
  /usr/include/c++/15/bits/functexcept.h \
  /usr/include/c++/15/bits/functional_hash.h \
  /usr/include/c++/15/bits/hash_bytes.h \
  /usr/include/c++/15/bits/hashtable.h \
  /usr/include/c++/15/bits/hashtable_policy.h \
  /usr/include/c++/15/bits/invoke.h \
  /usr/include/c++/15/bits/ios_base.h \
  /usr/include/c++/15/bits/istream.tcc \
  /usr/include/c++/15/bits/iterator_concepts.h \
  /usr/include/c++/15/bits/list.tcc \
  /usr/include/c++/15/bits/locale_classes.h \
  /usr/include/c++/15/bits/locale_classes.tcc \
  /usr/include/c++/15/bits/locale_conv.h \
  /usr/include/c++/15/bits/locale_facets.h \
  /usr/include/c++/15/bits/locale_facets.tcc \
  /usr/include/c++/15/bits/locale_facets_nonio.h \
  /usr/include/c++/15/bits/locale_facets_nonio.tcc \
  /usr/include/c++/15/bits/localefwd.h \
  /usr/include/c++/15/bits/max_size_type.h \
  /usr/include/c++/15/bits/memory_resource.h \
  /usr/include/c++/15/bits/memoryfwd.h \
  /usr/include/c++/15/bits/monostate.h \
  /usr/include/c++/15/bits/move.h \
  /usr/include/c++/15/bits/nested_exception.h \
  /usr/include/c++/15/bits/new_allocator.h \
  /usr/include/c++/15/bits/node_handle.h \
  /usr/include/c++/15/bits/ostream.h \
  /usr/include/c++/15/bits/ostream.tcc \
  /usr/include/c++/15/bits/ostream_insert.h \
  /usr/include/c++/15/bits/parse_numbers.h \
  /usr/include/c++/15/bits/postypes.h \
  /usr/include/c++/15/bits/predefined_ops.h \
  /usr/include/c++/15/bits/ptr_traits.h \
  /usr/include/c++/15/bits/quoted_string.h \
  /usr/include/c++/15/bits/range_access.h \
  /usr/include/c++/15/bits/ranges_algo.h \
  /usr/include/c++/15/bits/ranges_algobase.h \
  /usr/include/c++/15/bits/ranges_base.h \
  /usr/include/c++/15/bits/ranges_cmp.h \
  /usr/include/c++/15/bits/ranges_uninitialized.h \
  /usr/include/c++/15/bits/ranges_util.h \
  /usr/include/c++/15/bits/refwrap.h \
  /usr/include/c++/15/bits/requires_hosted.h \
  /usr/include/c++/15/bits/shared_ptr.h \
  /usr/include/c++/15/bits/shared_ptr_atomic.h \
  /usr/include/c++/15/bits/shared_ptr_base.h \
  /usr/include/c++/15/bits/specfun.h \
  /usr/include/c++/15/bits/sstream.tcc \
  /usr/include/c++/15/bits/std_abs.h \
  /usr/include/c++/15/bits/std_function.h \
  /usr/include/c++/15/bits/std_mutex.h \
  /usr/include/c++/15/bits/stl_algo.h \
  /usr/include/c++/15/bits/stl_algobase.h \
  /usr/include/c++/15/bits/stl_bvector.h \
  /usr/include/c++/15/bits/stl_construct.h \
  /usr/include/c++/15/bits/stl_function.h \
  /usr/include/c++/15/bits/stl_heap.h \
  /usr/include/c++/15/bits/stl_iterator.h \
  /usr/include/c++/15/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/15/bits/stl_iterator_base_types.h \
  /usr/include/c++/15/bits/stl_list.h \
  /usr/include/c++/15/bits/stl_map.h \
  /usr/include/c++/15/bits/stl_multimap.h \
  /usr/include/c++/15/bits/stl_multiset.h \
  /usr/include/c++/15/bits/stl_numeric.h \
  /usr/include/c++/15/bits/stl_pair.h \
  /usr/include/c++/15/bits/stl_raw_storage_iter.h \
  /usr/include/c++/15/bits/stl_relops.h \
  /usr/include/c++/15/bits/stl_set.h \
  /usr/include/c++/15/bits/stl_tempbuf.h \
  /usr/include/c++/15/bits/stl_tree.h \
  /usr/include/c++/15/bits/stl_uninitialized.h \
  /usr/include/c++/15/bits/stl_vector.h \
  /usr/include/c++/15/bits/stream_iterator.h \
  /usr/include/c++/15/bits/streambuf.tcc \
  /usr/include/c++/15/bits/streambuf_iterator.h \
  /usr/include/c++/15/bits/string_view.tcc \
  /usr/include/c++/15/bits/stringfwd.h \
  /usr/include/c++/15/bits/unicode-data.h \
  /usr/include/c++/15/bits/unicode.h \
  /usr/include/c++/15/bits/uniform_int_dist.h \
  /usr/include/c++/15/bits/unique_ptr.h \
  /usr/include/c++/15/bits/unordered_map.h \
  /usr/include/c++/15/bits/unordered_set.h \
  /usr/include/c++/15/bits/uses_allocator.h \
  /usr/include/c++/15/bits/uses_allocator_args.h \
  /usr/include/c++/15/bits/utility.h \
  /usr/include/c++/15/bits/vector.tcc \
  /usr/include/c++/15/bits/version.h \
  /usr/include/c++/15/cassert \
  /usr/include/c++/15/cctype \
  /usr/include/c++/15/cerrno \
  /usr/include/c++/15/charconv \
  /usr/include/c++/15/chrono \
  /usr/include/c++/15/climits \
  /usr/include/c++/15/clocale \
  /usr/include/c++/15/cmath \
  /usr/include/c++/15/compare \
  /usr/include/c++/15/concepts \
  /usr/include/c++/15/cstddef \
  /usr/include/c++/15/cstdint \
  /usr/include/c++/15/cstdio \
  /usr/include/c++/15/cstdlib \
  /usr/include/c++/15/cstring \
  /usr/include/c++/15/ctime \
  /usr/include/c++/15/cwchar \
  /usr/include/c++/15/cwctype \
  /usr/include/c++/15/debug/assertions.h \
  /usr/include/c++/15/debug/debug.h \
  /usr/include/c++/15/exception \
  /usr/include/c++/15/ext/aligned_buffer.h \
  /usr/include/c++/15/ext/alloc_traits.h \
  /usr/include/c++/15/ext/atomicity.h \
  /usr/include/c++/15/ext/concurrence.h \
  /usr/include/c++/15/ext/numeric_traits.h \
  /usr/include/c++/15/ext/string_conversions.h \
  /usr/include/c++/15/ext/type_traits.h \
  /usr/include/c++/15/format \
  /usr/include/c++/15/functional \
  /usr/include/c++/15/initializer_list \
  /usr/include/c++/15/iomanip \
  /usr/include/c++/15/ios \
  /usr/include/c++/15/iosfwd \
  /usr/include/c++/15/istream \
  /usr/include/c++/15/iterator \
  /usr/include/c++/15/limits \
  /usr/include/c++/15/list \
  /usr/include/c++/15/locale \
  /usr/include/c++/15/map \
  /usr/include/c++/15/memory \
  /usr/include/c++/15/new \
  /usr/include/c++/15/numbers \
  /usr/include/c++/15/numeric \
  /usr/include/c++/15/optional \
  /usr/include/c++/15/ostream \
  /usr/include/c++/15/pstl/execution_defs.h \
  /usr/include/c++/15/pstl/glue_algorithm_defs.h \
  /usr/include/c++/15/pstl/glue_memory_defs.h \
  /usr/include/c++/15/pstl/glue_numeric_defs.h \
  /usr/include/c++/15/pstl/pstl_config.h \
  /usr/include/c++/15/ratio \
  /usr/include/c++/15/set \
  /usr/include/c++/15/span \
  /usr/include/c++/15/sstream \
  /usr/include/c++/15/stdexcept \
  /usr/include/c++/15/stdlib.h \
  /usr/include/c++/15/streambuf \
  /usr/include/c++/15/string \
  /usr/include/c++/15/string_view \
  /usr/include/c++/15/system_error \
  /usr/include/c++/15/tr1/bessel_function.tcc \
  /usr/include/c++/15/tr1/beta_function.tcc \
  /usr/include/c++/15/tr1/ell_integral.tcc \
  /usr/include/c++/15/tr1/exp_integral.tcc \
  /usr/include/c++/15/tr1/gamma.tcc \
  /usr/include/c++/15/tr1/hypergeometric.tcc \
  /usr/include/c++/15/tr1/legendre_function.tcc \
  /usr/include/c++/15/tr1/modified_bessel_func.tcc \
  /usr/include/c++/15/tr1/poly_hermite.tcc \
  /usr/include/c++/15/tr1/poly_laguerre.tcc \
  /usr/include/c++/15/tr1/riemann_zeta.tcc \
  /usr/include/c++/15/tr1/special_function_util.h \
  /usr/include/c++/15/tuple \
  /usr/include/c++/15/type_traits \
  /usr/include/c++/15/typeinfo \
  /usr/include/c++/15/unordered_map \
  /usr/include/c++/15/unordered_set \
  /usr/include/c++/15/utility \
  /usr/include/c++/15/variant \
  /usr/include/c++/15/vector \
  /usr/include/c++/15/version \
  /usr/include/c++/15/x86_64-redhat-linux/bits/atomic_word.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/c++allocator.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/c++config.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/c++locale.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/cpu_defines.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/ctype_base.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/ctype_inline.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/error_constants.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/gthr-default.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/gthr.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/messages_members.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/os_defines.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/time_members.h \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/libintl.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/locale.h \
  /usr/include/math.h \
  /usr/include/pthread.h \
  /usr/include/qt6/QtCore/QString \
  /usr/include/qt6/QtCore/q17memory.h \
  /usr/include/qt6/QtCore/q20functional.h \
  /usr/include/qt6/QtCore/q20iterator.h \
  /usr/include/qt6/QtCore/q20memory.h \
  /usr/include/qt6/QtCore/q20type_traits.h \
  /usr/include/qt6/QtCore/q20utility.h \
  /usr/include/qt6/QtCore/q23utility.h \
  /usr/include/qt6/QtCore/qabstracteventdispatcher.h \
  /usr/include/qt6/QtCore/qalgorithms.h \
  /usr/include/qt6/QtCore/qanystringview.h \
  /usr/include/qt6/QtCore/qarraydata.h \
  /usr/include/qt6/QtCore/qarraydataops.h \
  /usr/include/qt6/QtCore/qarraydatapointer.h \
  /usr/include/qt6/QtCore/qassert.h \
  /usr/include/qt6/QtCore/qatomic.h \
  /usr/include/qt6/QtCore/qatomic_cxx11.h \
  /usr/include/qt6/QtCore/qbasicatomic.h \
  /usr/include/qt6/QtCore/qbasictimer.h \
  /usr/include/qt6/QtCore/qbindingstorage.h \
  /usr/include/qt6/QtCore/qbytearray.h \
  /usr/include/qt6/QtCore/qbytearrayalgorithms.h \
  /usr/include/qt6/QtCore/qbytearraylist.h \
  /usr/include/qt6/QtCore/qbytearrayview.h \
  /usr/include/qt6/QtCore/qchar.h \
  /usr/include/qt6/QtCore/qcompare.h \
  /usr/include/qt6/QtCore/qcompare_impl.h \
  /usr/include/qt6/QtCore/qcomparehelpers.h \
  /usr/include/qt6/QtCore/qcompilerdetection.h \
  /usr/include/qt6/QtCore/qconfig-64.h \
  /usr/include/qt6/QtCore/qconfig.h \
  /usr/include/qt6/QtCore/qconstructormacros.h \
  /usr/include/qt6/QtCore/qcontainerfwd.h \
  /usr/include/qt6/QtCore/qcontainerinfo.h \
  /usr/include/qt6/QtCore/qcontainertools_impl.h \
  /usr/include/qt6/QtCore/qcontiguouscache.h \
  /usr/include/qt6/QtCore/qcoreapplication.h \
  /usr/include/qt6/QtCore/qcoreapplication_platform.h \
  /usr/include/qt6/QtCore/qcoreevent.h \
  /usr/include/qt6/QtCore/qdarwinhelpers.h \
  /usr/include/qt6/QtCore/qdatastream.h \
  /usr/include/qt6/QtCore/qdeadlinetimer.h \
  /usr/include/qt6/QtCore/qdebug.h \
  /usr/include/qt6/QtCore/qelapsedtimer.h \
  /usr/include/qt6/QtCore/qendian.h \
  /usr/include/qt6/QtCore/qeventloop.h \
  /usr/include/qt6/QtCore/qexceptionhandling.h \
  /usr/include/qt6/QtCore/qflags.h \
  /usr/include/qt6/QtCore/qfloat16.h \
  /usr/include/qt6/QtCore/qforeach.h \
  /usr/include/qt6/QtCore/qfunctionaltools_impl.h \
  /usr/include/qt6/QtCore/qfunctionpointer.h \
  /usr/include/qt6/QtCore/qgenericatomic.h \
  /usr/include/qt6/QtCore/qglobal.h \
  /usr/include/qt6/QtCore/qglobalstatic.h \
  /usr/include/qt6/QtCore/qhash.h \
  /usr/include/qt6/QtCore/qhashfunctions.h \
  /usr/include/qt6/QtCore/qiodevicebase.h \
  /usr/include/qt6/QtCore/qiterable.h \
  /usr/include/qt6/QtCore/qiterator.h \
  /usr/include/qt6/QtCore/qlatin1stringview.h \
  /usr/include/qt6/QtCore/qline.h \
  /usr/include/qt6/QtCore/qlist.h \
  /usr/include/qt6/QtCore/qlocale.h \
  /usr/include/qt6/QtCore/qlogging.h \
  /usr/include/qt6/QtCore/qmalloc.h \
  /usr/include/qt6/QtCore/qmap.h \
  /usr/include/qt6/QtCore/qmargins.h \
  /usr/include/qt6/QtCore/qmath.h \
  /usr/include/qt6/QtCore/qmetacontainer.h \
  /usr/include/qt6/QtCore/qmetatype.h \
  /usr/include/qt6/QtCore/qminmax.h \
  /usr/include/qt6/QtCore/qnamespace.h \
  /usr/include/qt6/QtCore/qnativeinterface.h \
  /usr/include/qt6/QtCore/qnumeric.h \
  /usr/include/qt6/QtCore/qobject.h \
  /usr/include/qt6/QtCore/qobject_impl.h \
  /usr/include/qt6/QtCore/qobjectdefs.h \
  /usr/include/qt6/QtCore/qobjectdefs_impl.h \
  /usr/include/qt6/QtCore/qoverload.h \
  /usr/include/qt6/QtCore/qpair.h \
  /usr/include/qt6/QtCore/qpoint.h \
  /usr/include/qt6/QtCore/qprocessordetection.h \
  /usr/include/qt6/QtCore/qrect.h \
  /usr/include/qt6/QtCore/qrefcount.h \
  /usr/include/qt6/QtCore/qscopedpointer.h \
  /usr/include/qt6/QtCore/qscopeguard.h \
  /usr/include/qt6/QtCore/qset.h \
  /usr/include/qt6/QtCore/qshareddata.h \
  /usr/include/qt6/QtCore/qshareddata_impl.h \
  /usr/include/qt6/QtCore/qsharedpointer.h \
  /usr/include/qt6/QtCore/qsharedpointer_impl.h \
  /usr/include/qt6/QtCore/qsize.h \
  /usr/include/qt6/QtCore/qspan.h \
  /usr/include/qt6/QtCore/qstdlibdetection.h \
  /usr/include/qt6/QtCore/qstring.h \
  /usr/include/qt6/QtCore/qstringalgorithms.h \
  /usr/include/qt6/QtCore/qstringbuilder.h \
  /usr/include/qt6/QtCore/qstringconverter.h \
  /usr/include/qt6/QtCore/qstringconverter_base.h \
  /usr/include/qt6/QtCore/qstringfwd.h \
  /usr/include/qt6/QtCore/qstringlist.h \
  /usr/include/qt6/QtCore/qstringliteral.h \
  /usr/include/qt6/QtCore/qstringmatcher.h \
  /usr/include/qt6/QtCore/qstringtokenizer.h \
  /usr/include/qt6/QtCore/qstringview.h \
  /usr/include/qt6/QtCore/qswap.h \
  /usr/include/qt6/QtCore/qsysinfo.h \
  /usr/include/qt6/QtCore/qsystemdetection.h \
  /usr/include/qt6/QtCore/qtaggedpointer.h \
  /usr/include/qt6/QtCore/qtclasshelpermacros.h \
  /usr/include/qt6/QtCore/qtconfiginclude.h \
  /usr/include/qt6/QtCore/qtconfigmacros.h \
  /usr/include/qt6/QtCore/qtcore-config.h \
  /usr/include/qt6/QtCore/qtcoreexports.h \
  /usr/include/qt6/QtCore/qtcoreglobal.h \
  /usr/include/qt6/QtCore/qtdeprecationdefinitions.h \
  /usr/include/qt6/QtCore/qtdeprecationmarkers.h \
  /usr/include/qt6/QtCore/qtenvironmentvariables.h \
  /usr/include/qt6/QtCore/qtextstream.h \
  /usr/include/qt6/QtCore/qtformat_impl.h \
  /usr/include/qt6/QtCore/qtmetamacros.h \
  /usr/include/qt6/QtCore/qtnoop.h \
  /usr/include/qt6/QtCore/qtpreprocessorsupport.h \
  /usr/include/qt6/QtCore/qtresource.h \
  /usr/include/qt6/QtCore/qttranslation.h \
  /usr/include/qt6/QtCore/qttypetraits.h \
  /usr/include/qt6/QtCore/qtversion.h \
  /usr/include/qt6/QtCore/qtversionchecks.h \
  /usr/include/qt6/QtCore/qtypeinfo.h \
  /usr/include/qt6/QtCore/qtypes.h \
  /usr/include/qt6/QtCore/qutf8stringview.h \
  /usr/include/qt6/QtCore/qvariant.h \
  /usr/include/qt6/QtCore/qvarlengtharray.h \
  /usr/include/qt6/QtCore/qversiontagging.h \
  /usr/include/qt6/QtCore/qxptype_traits.h \
  /usr/include/qt6/QtCore/qyieldcpu.h \
  /usr/include/qt6/QtGui/qaction.h \
  /usr/include/qt6/QtGui/qbitmap.h \
  /usr/include/qt6/QtGui/qbrush.h \
  /usr/include/qt6/QtGui/qcolor.h \
  /usr/include/qt6/QtGui/qcursor.h \
  /usr/include/qt6/QtGui/qfont.h \
  /usr/include/qt6/QtGui/qfontinfo.h \
  /usr/include/qt6/QtGui/qfontmetrics.h \
  /usr/include/qt6/QtGui/qfontvariableaxis.h \
  /usr/include/qt6/QtGui/qguiapplication.h \
  /usr/include/qt6/QtGui/qguiapplication_platform.h \
  /usr/include/qt6/QtGui/qicon.h \
  /usr/include/qt6/QtGui/qimage.h \
  /usr/include/qt6/QtGui/qinputmethod.h \
  /usr/include/qt6/QtGui/qkeysequence.h \
  /usr/include/qt6/QtGui/qpaintdevice.h \
  /usr/include/qt6/QtGui/qpalette.h \
  /usr/include/qt6/QtGui/qpixelformat.h \
  /usr/include/qt6/QtGui/qpixmap.h \
  /usr/include/qt6/QtGui/qpolygon.h \
  /usr/include/qt6/QtGui/qregion.h \
  /usr/include/qt6/QtGui/qrgb.h \
  /usr/include/qt6/QtGui/qrgba64.h \
  /usr/include/qt6/QtGui/qtgui-config.h \
  /usr/include/qt6/QtGui/qtguiexports.h \
  /usr/include/qt6/QtGui/qtguiglobal.h \
  /usr/include/qt6/QtGui/qtransform.h \
  /usr/include/qt6/QtGui/qwindowdefs.h \
  /usr/include/qt6/QtWidgets/QApplication \
  /usr/include/qt6/QtWidgets/QWidget \
  /usr/include/qt6/QtWidgets/qapplication.h \
  /usr/include/qt6/QtWidgets/qsizepolicy.h \
  /usr/include/qt6/QtWidgets/qtwidgets-config.h \
  /usr/include/qt6/QtWidgets/qtwidgetsexports.h \
  /usr/include/qt6/QtWidgets/qtwidgetsglobal.h \
  /usr/include/qt6/QtWidgets/qwidget.h \
  /usr/include/sched.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/select.h \
  /usr/include/sys/single_threaded.h \
  /usr/include/sys/syscall.h \
  /usr/include/sys/types.h \
  /usr/include/syscall.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/include/wchar.h \
  /usr/include/wctype.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/limits.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stdarg.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stdbool.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stddef.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stdint.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/syslimits.h

CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.o: /home/<USER>/CLionProjects/KNoteDo/app/MainWindow.cpp \
  /home/<USER>/CLionProjects/KNoteDo/app/AppleNotesTheme.h \
  /home/<USER>/CLionProjects/KNoteDo/app/MainWindow.h \
  /home/<USER>/CLionProjects/KNoteDo/app/RichTextEditor.h \
  /home/<USER>/CLionProjects/KNoteDo/app/SettingsDialog.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/posix_types_64.h \
  /usr/include/asm/types.h \
  /usr/include/asm/unistd.h \
  /usr/include/asm/unistd_64.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/flt-eval-method.h \
  /usr/include/bits/fp-fast.h \
  /usr/include/bits/fp-logb.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/iscanonical.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/libm-simd-decl-stubs.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/locale.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/math-vector.h \
  /usr/include/bits/mathcalls-helper-functions.h \
  /usr/include/bits/mathcalls-macros.h \
  /usr/include/bits/mathcalls-narrow.h \
  /usr/include/bits/mathcalls.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/syscall.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/mbstate_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/types/wint_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wctype-wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/c++/15/algorithm \
  /usr/include/c++/15/array \
  /usr/include/c++/15/atomic \
  /usr/include/c++/15/backward/auto_ptr.h \
  /usr/include/c++/15/backward/binders.h \
  /usr/include/c++/15/bit \
  /usr/include/c++/15/bits/algorithmfwd.h \
  /usr/include/c++/15/bits/align.h \
  /usr/include/c++/15/bits/alloc_traits.h \
  /usr/include/c++/15/bits/allocated_ptr.h \
  /usr/include/c++/15/bits/allocator.h \
  /usr/include/c++/15/bits/atomic_base.h \
  /usr/include/c++/15/bits/atomic_lockfree_defines.h \
  /usr/include/c++/15/bits/atomic_wait.h \
  /usr/include/c++/15/bits/basic_ios.h \
  /usr/include/c++/15/bits/basic_ios.tcc \
  /usr/include/c++/15/bits/basic_string.h \
  /usr/include/c++/15/bits/basic_string.tcc \
  /usr/include/c++/15/bits/char_traits.h \
  /usr/include/c++/15/bits/charconv.h \
  /usr/include/c++/15/bits/chrono.h \
  /usr/include/c++/15/bits/chrono_io.h \
  /usr/include/c++/15/bits/codecvt.h \
  /usr/include/c++/15/bits/concept_check.h \
  /usr/include/c++/15/bits/cpp_type_traits.h \
  /usr/include/c++/15/bits/cxxabi_forced.h \
  /usr/include/c++/15/bits/cxxabi_init_exception.h \
  /usr/include/c++/15/bits/enable_special_members.h \
  /usr/include/c++/15/bits/erase_if.h \
  /usr/include/c++/15/bits/exception.h \
  /usr/include/c++/15/bits/exception_defines.h \
  /usr/include/c++/15/bits/exception_ptr.h \
  /usr/include/c++/15/bits/formatfwd.h \
  /usr/include/c++/15/bits/fs_dir.h \
  /usr/include/c++/15/bits/fs_fwd.h \
  /usr/include/c++/15/bits/fs_ops.h \
  /usr/include/c++/15/bits/fs_path.h \
  /usr/include/c++/15/bits/functexcept.h \
  /usr/include/c++/15/bits/functional_hash.h \
  /usr/include/c++/15/bits/hash_bytes.h \
  /usr/include/c++/15/bits/hashtable.h \
  /usr/include/c++/15/bits/hashtable_policy.h \
  /usr/include/c++/15/bits/invoke.h \
  /usr/include/c++/15/bits/ios_base.h \
  /usr/include/c++/15/bits/istream.tcc \
  /usr/include/c++/15/bits/iterator_concepts.h \
  /usr/include/c++/15/bits/list.tcc \
  /usr/include/c++/15/bits/locale_classes.h \
  /usr/include/c++/15/bits/locale_classes.tcc \
  /usr/include/c++/15/bits/locale_conv.h \
  /usr/include/c++/15/bits/locale_facets.h \
  /usr/include/c++/15/bits/locale_facets.tcc \
  /usr/include/c++/15/bits/locale_facets_nonio.h \
  /usr/include/c++/15/bits/locale_facets_nonio.tcc \
  /usr/include/c++/15/bits/localefwd.h \
  /usr/include/c++/15/bits/max_size_type.h \
  /usr/include/c++/15/bits/memory_resource.h \
  /usr/include/c++/15/bits/memoryfwd.h \
  /usr/include/c++/15/bits/monostate.h \
  /usr/include/c++/15/bits/move.h \
  /usr/include/c++/15/bits/nested_exception.h \
  /usr/include/c++/15/bits/new_allocator.h \
  /usr/include/c++/15/bits/node_handle.h \
  /usr/include/c++/15/bits/ostream.h \
  /usr/include/c++/15/bits/ostream.tcc \
  /usr/include/c++/15/bits/ostream_insert.h \
  /usr/include/c++/15/bits/parse_numbers.h \
  /usr/include/c++/15/bits/postypes.h \
  /usr/include/c++/15/bits/predefined_ops.h \
  /usr/include/c++/15/bits/ptr_traits.h \
  /usr/include/c++/15/bits/quoted_string.h \
  /usr/include/c++/15/bits/range_access.h \
  /usr/include/c++/15/bits/ranges_algo.h \
  /usr/include/c++/15/bits/ranges_algobase.h \
  /usr/include/c++/15/bits/ranges_base.h \
  /usr/include/c++/15/bits/ranges_cmp.h \
  /usr/include/c++/15/bits/ranges_uninitialized.h \
  /usr/include/c++/15/bits/ranges_util.h \
  /usr/include/c++/15/bits/refwrap.h \
  /usr/include/c++/15/bits/requires_hosted.h \
  /usr/include/c++/15/bits/shared_ptr.h \
  /usr/include/c++/15/bits/shared_ptr_atomic.h \
  /usr/include/c++/15/bits/shared_ptr_base.h \
  /usr/include/c++/15/bits/specfun.h \
  /usr/include/c++/15/bits/sstream.tcc \
  /usr/include/c++/15/bits/std_abs.h \
  /usr/include/c++/15/bits/std_function.h \
  /usr/include/c++/15/bits/std_mutex.h \
  /usr/include/c++/15/bits/stl_algo.h \
  /usr/include/c++/15/bits/stl_algobase.h \
  /usr/include/c++/15/bits/stl_bvector.h \
  /usr/include/c++/15/bits/stl_construct.h \
  /usr/include/c++/15/bits/stl_function.h \
  /usr/include/c++/15/bits/stl_heap.h \
  /usr/include/c++/15/bits/stl_iterator.h \
  /usr/include/c++/15/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/15/bits/stl_iterator_base_types.h \
  /usr/include/c++/15/bits/stl_list.h \
  /usr/include/c++/15/bits/stl_map.h \
  /usr/include/c++/15/bits/stl_multimap.h \
  /usr/include/c++/15/bits/stl_multiset.h \
  /usr/include/c++/15/bits/stl_numeric.h \
  /usr/include/c++/15/bits/stl_pair.h \
  /usr/include/c++/15/bits/stl_raw_storage_iter.h \
  /usr/include/c++/15/bits/stl_relops.h \
  /usr/include/c++/15/bits/stl_set.h \
  /usr/include/c++/15/bits/stl_tempbuf.h \
  /usr/include/c++/15/bits/stl_tree.h \
  /usr/include/c++/15/bits/stl_uninitialized.h \
  /usr/include/c++/15/bits/stl_vector.h \
  /usr/include/c++/15/bits/stream_iterator.h \
  /usr/include/c++/15/bits/streambuf.tcc \
  /usr/include/c++/15/bits/streambuf_iterator.h \
  /usr/include/c++/15/bits/string_view.tcc \
  /usr/include/c++/15/bits/stringfwd.h \
  /usr/include/c++/15/bits/unicode-data.h \
  /usr/include/c++/15/bits/unicode.h \
  /usr/include/c++/15/bits/uniform_int_dist.h \
  /usr/include/c++/15/bits/unique_ptr.h \
  /usr/include/c++/15/bits/unordered_map.h \
  /usr/include/c++/15/bits/unordered_set.h \
  /usr/include/c++/15/bits/uses_allocator.h \
  /usr/include/c++/15/bits/uses_allocator_args.h \
  /usr/include/c++/15/bits/utility.h \
  /usr/include/c++/15/bits/vector.tcc \
  /usr/include/c++/15/bits/version.h \
  /usr/include/c++/15/cassert \
  /usr/include/c++/15/cctype \
  /usr/include/c++/15/cerrno \
  /usr/include/c++/15/charconv \
  /usr/include/c++/15/chrono \
  /usr/include/c++/15/climits \
  /usr/include/c++/15/clocale \
  /usr/include/c++/15/cmath \
  /usr/include/c++/15/codecvt \
  /usr/include/c++/15/compare \
  /usr/include/c++/15/concepts \
  /usr/include/c++/15/cstddef \
  /usr/include/c++/15/cstdint \
  /usr/include/c++/15/cstdio \
  /usr/include/c++/15/cstdlib \
  /usr/include/c++/15/cstring \
  /usr/include/c++/15/ctime \
  /usr/include/c++/15/cwchar \
  /usr/include/c++/15/cwctype \
  /usr/include/c++/15/debug/assertions.h \
  /usr/include/c++/15/debug/debug.h \
  /usr/include/c++/15/exception \
  /usr/include/c++/15/ext/aligned_buffer.h \
  /usr/include/c++/15/ext/alloc_traits.h \
  /usr/include/c++/15/ext/atomicity.h \
  /usr/include/c++/15/ext/concurrence.h \
  /usr/include/c++/15/ext/numeric_traits.h \
  /usr/include/c++/15/ext/string_conversions.h \
  /usr/include/c++/15/ext/type_traits.h \
  /usr/include/c++/15/filesystem \
  /usr/include/c++/15/format \
  /usr/include/c++/15/functional \
  /usr/include/c++/15/initializer_list \
  /usr/include/c++/15/iomanip \
  /usr/include/c++/15/ios \
  /usr/include/c++/15/iosfwd \
  /usr/include/c++/15/istream \
  /usr/include/c++/15/iterator \
  /usr/include/c++/15/limits \
  /usr/include/c++/15/list \
  /usr/include/c++/15/locale \
  /usr/include/c++/15/map \
  /usr/include/c++/15/memory \
  /usr/include/c++/15/new \
  /usr/include/c++/15/numbers \
  /usr/include/c++/15/numeric \
  /usr/include/c++/15/optional \
  /usr/include/c++/15/ostream \
  /usr/include/c++/15/pstl/execution_defs.h \
  /usr/include/c++/15/pstl/glue_algorithm_defs.h \
  /usr/include/c++/15/pstl/glue_memory_defs.h \
  /usr/include/c++/15/pstl/glue_numeric_defs.h \
  /usr/include/c++/15/pstl/pstl_config.h \
  /usr/include/c++/15/ratio \
  /usr/include/c++/15/set \
  /usr/include/c++/15/span \
  /usr/include/c++/15/sstream \
  /usr/include/c++/15/stdexcept \
  /usr/include/c++/15/stdlib.h \
  /usr/include/c++/15/streambuf \
  /usr/include/c++/15/string \
  /usr/include/c++/15/string_view \
  /usr/include/c++/15/system_error \
  /usr/include/c++/15/tr1/bessel_function.tcc \
  /usr/include/c++/15/tr1/beta_function.tcc \
  /usr/include/c++/15/tr1/ell_integral.tcc \
  /usr/include/c++/15/tr1/exp_integral.tcc \
  /usr/include/c++/15/tr1/gamma.tcc \
  /usr/include/c++/15/tr1/hypergeometric.tcc \
  /usr/include/c++/15/tr1/legendre_function.tcc \
  /usr/include/c++/15/tr1/modified_bessel_func.tcc \
  /usr/include/c++/15/tr1/poly_hermite.tcc \
  /usr/include/c++/15/tr1/poly_laguerre.tcc \
  /usr/include/c++/15/tr1/riemann_zeta.tcc \
  /usr/include/c++/15/tr1/special_function_util.h \
  /usr/include/c++/15/tuple \
  /usr/include/c++/15/type_traits \
  /usr/include/c++/15/typeinfo \
  /usr/include/c++/15/unordered_map \
  /usr/include/c++/15/unordered_set \
  /usr/include/c++/15/utility \
  /usr/include/c++/15/variant \
  /usr/include/c++/15/vector \
  /usr/include/c++/15/version \
  /usr/include/c++/15/x86_64-redhat-linux/bits/atomic_word.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/c++allocator.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/c++config.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/c++locale.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/cpu_defines.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/ctype_base.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/ctype_inline.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/error_constants.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/gthr-default.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/gthr.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/messages_members.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/os_defines.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/time_members.h \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/libintl.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/locale.h \
  /usr/include/math.h \
  /usr/include/pthread.h \
  /usr/include/qt6/QtCore/QDateTime \
  /usr/include/qt6/QtCore/QDir \
  /usr/include/qt6/QtCore/QFile \
  /usr/include/qt6/QtCore/QJsonArray \
  /usr/include/qt6/QtCore/QJsonDocument \
  /usr/include/qt6/QtCore/QJsonObject \
  /usr/include/qt6/QtCore/QSettings \
  /usr/include/qt6/QtCore/QStandardPaths \
  /usr/include/qt6/QtCore/QString \
  /usr/include/qt6/QtCore/QTextStream \
  /usr/include/qt6/QtCore/QTimer \
  /usr/include/qt6/QtCore/QUrl \
  /usr/include/qt6/QtCore/q17memory.h \
  /usr/include/qt6/QtCore/q20functional.h \
  /usr/include/qt6/QtCore/q20iterator.h \
  /usr/include/qt6/QtCore/q20memory.h \
  /usr/include/qt6/QtCore/q20type_traits.h \
  /usr/include/qt6/QtCore/q20utility.h \
  /usr/include/qt6/QtCore/q23utility.h \
  /usr/include/qt6/QtCore/qabstracteventdispatcher.h \
  /usr/include/qt6/QtCore/qabstractitemmodel.h \
  /usr/include/qt6/QtCore/qalgorithms.h \
  /usr/include/qt6/QtCore/qanystringview.h \
  /usr/include/qt6/QtCore/qarraydata.h \
  /usr/include/qt6/QtCore/qarraydataops.h \
  /usr/include/qt6/QtCore/qarraydatapointer.h \
  /usr/include/qt6/QtCore/qassert.h \
  /usr/include/qt6/QtCore/qatomic.h \
  /usr/include/qt6/QtCore/qatomic_cxx11.h \
  /usr/include/qt6/QtCore/qbasicatomic.h \
  /usr/include/qt6/QtCore/qbasictimer.h \
  /usr/include/qt6/QtCore/qbindingstorage.h \
  /usr/include/qt6/QtCore/qbytearray.h \
  /usr/include/qt6/QtCore/qbytearrayalgorithms.h \
  /usr/include/qt6/QtCore/qbytearraylist.h \
  /usr/include/qt6/QtCore/qbytearrayview.h \
  /usr/include/qt6/QtCore/qcalendar.h \
  /usr/include/qt6/QtCore/qcborcommon.h \
  /usr/include/qt6/QtCore/qcborvalue.h \
  /usr/include/qt6/QtCore/qchar.h \
  /usr/include/qt6/QtCore/qcompare.h \
  /usr/include/qt6/QtCore/qcompare_impl.h \
  /usr/include/qt6/QtCore/qcomparehelpers.h \
  /usr/include/qt6/QtCore/qcompilerdetection.h \
  /usr/include/qt6/QtCore/qconfig-64.h \
  /usr/include/qt6/QtCore/qconfig.h \
  /usr/include/qt6/QtCore/qconstructormacros.h \
  /usr/include/qt6/QtCore/qcontainerfwd.h \
  /usr/include/qt6/QtCore/qcontainerinfo.h \
  /usr/include/qt6/QtCore/qcontainertools_impl.h \
  /usr/include/qt6/QtCore/qcontiguouscache.h \
  /usr/include/qt6/QtCore/qcoreapplication.h \
  /usr/include/qt6/QtCore/qcoreapplication_platform.h \
  /usr/include/qt6/QtCore/qcoreevent.h \
  /usr/include/qt6/QtCore/qdarwinhelpers.h \
  /usr/include/qt6/QtCore/qdatastream.h \
  /usr/include/qt6/QtCore/qdatetime.h \
  /usr/include/qt6/QtCore/qdeadlinetimer.h \
  /usr/include/qt6/QtCore/qdebug.h \
  /usr/include/qt6/QtCore/qdir.h \
  /usr/include/qt6/QtCore/qdirlisting.h \
  /usr/include/qt6/QtCore/qelapsedtimer.h \
  /usr/include/qt6/QtCore/qendian.h \
  /usr/include/qt6/QtCore/qeventloop.h \
  /usr/include/qt6/QtCore/qexceptionhandling.h \
  /usr/include/qt6/QtCore/qfile.h \
  /usr/include/qt6/QtCore/qfiledevice.h \
  /usr/include/qt6/QtCore/qfileinfo.h \
  /usr/include/qt6/QtCore/qflags.h \
  /usr/include/qt6/QtCore/qfloat16.h \
  /usr/include/qt6/QtCore/qforeach.h \
  /usr/include/qt6/QtCore/qfunctionaltools_impl.h \
  /usr/include/qt6/QtCore/qfunctionpointer.h \
  /usr/include/qt6/QtCore/qgenericatomic.h \
  /usr/include/qt6/QtCore/qglobal.h \
  /usr/include/qt6/QtCore/qglobalstatic.h \
  /usr/include/qt6/QtCore/qhash.h \
  /usr/include/qt6/QtCore/qhashfunctions.h \
  /usr/include/qt6/QtCore/qiodevice.h \
  /usr/include/qt6/QtCore/qiodevicebase.h \
  /usr/include/qt6/QtCore/qitemselectionmodel.h \
  /usr/include/qt6/QtCore/qiterable.h \
  /usr/include/qt6/QtCore/qiterator.h \
  /usr/include/qt6/QtCore/qjsonarray.h \
  /usr/include/qt6/QtCore/qjsondocument.h \
  /usr/include/qt6/QtCore/qjsonobject.h \
  /usr/include/qt6/QtCore/qjsonparseerror.h \
  /usr/include/qt6/QtCore/qjsonvalue.h \
  /usr/include/qt6/QtCore/qlatin1stringview.h \
  /usr/include/qt6/QtCore/qline.h \
  /usr/include/qt6/QtCore/qlist.h \
  /usr/include/qt6/QtCore/qlocale.h \
  /usr/include/qt6/QtCore/qlogging.h \
  /usr/include/qt6/QtCore/qmalloc.h \
  /usr/include/qt6/QtCore/qmap.h \
  /usr/include/qt6/QtCore/qmargins.h \
  /usr/include/qt6/QtCore/qmath.h \
  /usr/include/qt6/QtCore/qmetacontainer.h \
  /usr/include/qt6/QtCore/qmetatype.h \
  /usr/include/qt6/QtCore/qminmax.h \
  /usr/include/qt6/QtCore/qnamespace.h \
  /usr/include/qt6/QtCore/qnativeinterface.h \
  /usr/include/qt6/QtCore/qnumeric.h \
  /usr/include/qt6/QtCore/qobject.h \
  /usr/include/qt6/QtCore/qobject_impl.h \
  /usr/include/qt6/QtCore/qobjectdefs.h \
  /usr/include/qt6/QtCore/qobjectdefs_impl.h \
  /usr/include/qt6/QtCore/qoverload.h \
  /usr/include/qt6/QtCore/qpair.h \
  /usr/include/qt6/QtCore/qpoint.h \
  /usr/include/qt6/QtCore/qprocessordetection.h \
  /usr/include/qt6/QtCore/qrect.h \
  /usr/include/qt6/QtCore/qrefcount.h \
  /usr/include/qt6/QtCore/qregularexpression.h \
  /usr/include/qt6/QtCore/qscopedpointer.h \
  /usr/include/qt6/QtCore/qscopeguard.h \
  /usr/include/qt6/QtCore/qset.h \
  /usr/include/qt6/QtCore/qsettings.h \
  /usr/include/qt6/QtCore/qshareddata.h \
  /usr/include/qt6/QtCore/qshareddata_impl.h \
  /usr/include/qt6/QtCore/qsharedpointer.h \
  /usr/include/qt6/QtCore/qsharedpointer_impl.h \
  /usr/include/qt6/QtCore/qsize.h \
  /usr/include/qt6/QtCore/qspan.h \
  /usr/include/qt6/QtCore/qstandardpaths.h \
  /usr/include/qt6/QtCore/qstdlibdetection.h \
  /usr/include/qt6/QtCore/qstring.h \
  /usr/include/qt6/QtCore/qstringalgorithms.h \
  /usr/include/qt6/QtCore/qstringbuilder.h \
  /usr/include/qt6/QtCore/qstringconverter.h \
  /usr/include/qt6/QtCore/qstringconverter_base.h \
  /usr/include/qt6/QtCore/qstringfwd.h \
  /usr/include/qt6/QtCore/qstringlist.h \
  /usr/include/qt6/QtCore/qstringliteral.h \
  /usr/include/qt6/QtCore/qstringmatcher.h \
  /usr/include/qt6/QtCore/qstringtokenizer.h \
  /usr/include/qt6/QtCore/qstringview.h \
  /usr/include/qt6/QtCore/qswap.h \
  /usr/include/qt6/QtCore/qsysinfo.h \
  /usr/include/qt6/QtCore/qsystemdetection.h \
  /usr/include/qt6/QtCore/qtaggedpointer.h \
  /usr/include/qt6/QtCore/qtclasshelpermacros.h \
  /usr/include/qt6/QtCore/qtconfiginclude.h \
  /usr/include/qt6/QtCore/qtconfigmacros.h \
  /usr/include/qt6/QtCore/qtcore-config.h \
  /usr/include/qt6/QtCore/qtcoreexports.h \
  /usr/include/qt6/QtCore/qtcoreglobal.h \
  /usr/include/qt6/QtCore/qtdeprecationdefinitions.h \
  /usr/include/qt6/QtCore/qtdeprecationmarkers.h \
  /usr/include/qt6/QtCore/qtenvironmentvariables.h \
  /usr/include/qt6/QtCore/qtextstream.h \
  /usr/include/qt6/QtCore/qtformat_impl.h \
  /usr/include/qt6/QtCore/qtimer.h \
  /usr/include/qt6/QtCore/qtimezone.h \
  /usr/include/qt6/QtCore/qtmetamacros.h \
  /usr/include/qt6/QtCore/qtnoop.h \
  /usr/include/qt6/QtCore/qtpreprocessorsupport.h \
  /usr/include/qt6/QtCore/qtresource.h \
  /usr/include/qt6/QtCore/qttranslation.h \
  /usr/include/qt6/QtCore/qttypetraits.h \
  /usr/include/qt6/QtCore/qtversion.h \
  /usr/include/qt6/QtCore/qtversionchecks.h \
  /usr/include/qt6/QtCore/qtypeinfo.h \
  /usr/include/qt6/QtCore/qtypes.h \
  /usr/include/qt6/QtCore/qurl.h \
  /usr/include/qt6/QtCore/qutf8stringview.h \
  /usr/include/qt6/QtCore/quuid.h \
  /usr/include/qt6/QtCore/qvariant.h \
  /usr/include/qt6/QtCore/qvarlengtharray.h \
  /usr/include/qt6/QtCore/qversiontagging.h \
  /usr/include/qt6/QtCore/qxptype_traits.h \
  /usr/include/qt6/QtCore/qyieldcpu.h \
  /usr/include/qt6/QtGui/QAction \
  /usr/include/qt6/QtGui/QDesktopServices \
  /usr/include/qt6/QtGui/QFont \
  /usr/include/qt6/QtGui/QFontMetrics \
  /usr/include/qt6/QtGui/QIcon \
  /usr/include/qt6/QtGui/QKeySequence \
  /usr/include/qt6/QtGui/QPalette \
  /usr/include/qt6/QtGui/QShortcut \
  /usr/include/qt6/QtGui/QStyleHints \
  /usr/include/qt6/QtGui/QTextCharFormat \
  /usr/include/qt6/QtGui/QTextCursor \
  /usr/include/qt6/QtGui/qaction.h \
  /usr/include/qt6/QtGui/qbitmap.h \
  /usr/include/qt6/QtGui/qbrush.h \
  /usr/include/qt6/QtGui/qcolor.h \
  /usr/include/qt6/QtGui/qcursor.h \
  /usr/include/qt6/QtGui/qdesktopservices.h \
  /usr/include/qt6/QtGui/qfont.h \
  /usr/include/qt6/QtGui/qfontinfo.h \
  /usr/include/qt6/QtGui/qfontmetrics.h \
  /usr/include/qt6/QtGui/qfontvariableaxis.h \
  /usr/include/qt6/QtGui/qguiapplication.h \
  /usr/include/qt6/QtGui/qguiapplication_platform.h \
  /usr/include/qt6/QtGui/qicon.h \
  /usr/include/qt6/QtGui/qimage.h \
  /usr/include/qt6/QtGui/qinputmethod.h \
  /usr/include/qt6/QtGui/qkeysequence.h \
  /usr/include/qt6/QtGui/qpaintdevice.h \
  /usr/include/qt6/QtGui/qpalette.h \
  /usr/include/qt6/QtGui/qpen.h \
  /usr/include/qt6/QtGui/qpicture.h \
  /usr/include/qt6/QtGui/qpixelformat.h \
  /usr/include/qt6/QtGui/qpixmap.h \
  /usr/include/qt6/QtGui/qpolygon.h \
  /usr/include/qt6/QtGui/qregion.h \
  /usr/include/qt6/QtGui/qrgb.h \
  /usr/include/qt6/QtGui/qrgba64.h \
  /usr/include/qt6/QtGui/qshortcut.h \
  /usr/include/qt6/QtGui/qstylehints.h \
  /usr/include/qt6/QtGui/qtextcursor.h \
  /usr/include/qt6/QtGui/qtextdocument.h \
  /usr/include/qt6/QtGui/qtextformat.h \
  /usr/include/qt6/QtGui/qtextoption.h \
  /usr/include/qt6/QtGui/qtgui-config.h \
  /usr/include/qt6/QtGui/qtguiexports.h \
  /usr/include/qt6/QtGui/qtguiglobal.h \
  /usr/include/qt6/QtGui/qtransform.h \
  /usr/include/qt6/QtGui/qvalidator.h \
  /usr/include/qt6/QtGui/qwindowdefs.h \
  /usr/include/qt6/QtWidgets/QApplication \
  /usr/include/qt6/QtWidgets/QDialog \
  /usr/include/qt6/QtWidgets/QFileDialog \
  /usr/include/qt6/QtWidgets/QFrame \
  /usr/include/qt6/QtWidgets/QHBoxLayout \
  /usr/include/qt6/QtWidgets/QHeaderView \
  /usr/include/qt6/QtWidgets/QInputDialog \
  /usr/include/qt6/QtWidgets/QLabel \
  /usr/include/qt6/QtWidgets/QLineEdit \
  /usr/include/qt6/QtWidgets/QListWidget \
  /usr/include/qt6/QtWidgets/QListWidgetItem \
  /usr/include/qt6/QtWidgets/QMainWindow \
  /usr/include/qt6/QtWidgets/QMenu \
  /usr/include/qt6/QtWidgets/QMenuBar \
  /usr/include/qt6/QtWidgets/QMessageBox \
  /usr/include/qt6/QtWidgets/QPushButton \
  /usr/include/qt6/QtWidgets/QSplitter \
  /usr/include/qt6/QtWidgets/QStackedWidget \
  /usr/include/qt6/QtWidgets/QStyle \
  /usr/include/qt6/QtWidgets/QStyleFactory \
  /usr/include/qt6/QtWidgets/QTextEdit \
  /usr/include/qt6/QtWidgets/QToolBar \
  /usr/include/qt6/QtWidgets/QTreeWidget \
  /usr/include/qt6/QtWidgets/QTreeWidgetItem \
  /usr/include/qt6/QtWidgets/QVBoxLayout \
  /usr/include/qt6/QtWidgets/QWidget \
  /usr/include/qt6/QtWidgets/qabstractbutton.h \
  /usr/include/qt6/QtWidgets/qabstractitemdelegate.h \
  /usr/include/qt6/QtWidgets/qabstractitemview.h \
  /usr/include/qt6/QtWidgets/qabstractscrollarea.h \
  /usr/include/qt6/QtWidgets/qabstractslider.h \
  /usr/include/qt6/QtWidgets/qabstractspinbox.h \
  /usr/include/qt6/QtWidgets/qapplication.h \
  /usr/include/qt6/QtWidgets/qboxlayout.h \
  /usr/include/qt6/QtWidgets/qdialog.h \
  /usr/include/qt6/QtWidgets/qdialogbuttonbox.h \
  /usr/include/qt6/QtWidgets/qfiledialog.h \
  /usr/include/qt6/QtWidgets/qframe.h \
  /usr/include/qt6/QtWidgets/qgridlayout.h \
  /usr/include/qt6/QtWidgets/qheaderview.h \
  /usr/include/qt6/QtWidgets/qinputdialog.h \
  /usr/include/qt6/QtWidgets/qlabel.h \
  /usr/include/qt6/QtWidgets/qlayout.h \
  /usr/include/qt6/QtWidgets/qlayoutitem.h \
  /usr/include/qt6/QtWidgets/qlineedit.h \
  /usr/include/qt6/QtWidgets/qlistview.h \
  /usr/include/qt6/QtWidgets/qlistwidget.h \
  /usr/include/qt6/QtWidgets/qmainwindow.h \
  /usr/include/qt6/QtWidgets/qmenu.h \
  /usr/include/qt6/QtWidgets/qmenubar.h \
  /usr/include/qt6/QtWidgets/qmessagebox.h \
  /usr/include/qt6/QtWidgets/qpushbutton.h \
  /usr/include/qt6/QtWidgets/qrubberband.h \
  /usr/include/qt6/QtWidgets/qsizepolicy.h \
  /usr/include/qt6/QtWidgets/qslider.h \
  /usr/include/qt6/QtWidgets/qsplitter.h \
  /usr/include/qt6/QtWidgets/qstackedwidget.h \
  /usr/include/qt6/QtWidgets/qstyle.h \
  /usr/include/qt6/QtWidgets/qstylefactory.h \
  /usr/include/qt6/QtWidgets/qstyleoption.h \
  /usr/include/qt6/QtWidgets/qtabbar.h \
  /usr/include/qt6/QtWidgets/qtabwidget.h \
  /usr/include/qt6/QtWidgets/qtextedit.h \
  /usr/include/qt6/QtWidgets/qtoolbar.h \
  /usr/include/qt6/QtWidgets/qtreeview.h \
  /usr/include/qt6/QtWidgets/qtreewidget.h \
  /usr/include/qt6/QtWidgets/qtreewidgetitemiterator.h \
  /usr/include/qt6/QtWidgets/qtwidgets-config.h \
  /usr/include/qt6/QtWidgets/qtwidgetsexports.h \
  /usr/include/qt6/QtWidgets/qtwidgetsglobal.h \
  /usr/include/qt6/QtWidgets/qwidget.h \
  /usr/include/sched.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/select.h \
  /usr/include/sys/single_threaded.h \
  /usr/include/sys/syscall.h \
  /usr/include/sys/types.h \
  /usr/include/syscall.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/include/wchar.h \
  /usr/include/wctype.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/limits.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stdarg.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stdbool.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stddef.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stdint.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/syslimits.h

CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.o: /home/<USER>/CLionProjects/KNoteDo/app/RichTextEditor.cpp \
  /home/<USER>/CLionProjects/KNoteDo/app/RichTextEditor.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/posix_types_64.h \
  /usr/include/asm/types.h \
  /usr/include/asm/unistd.h \
  /usr/include/asm/unistd_64.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/flt-eval-method.h \
  /usr/include/bits/fp-fast.h \
  /usr/include/bits/fp-logb.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/iscanonical.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/libm-simd-decl-stubs.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/locale.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/math-vector.h \
  /usr/include/bits/mathcalls-helper-functions.h \
  /usr/include/bits/mathcalls-macros.h \
  /usr/include/bits/mathcalls-narrow.h \
  /usr/include/bits/mathcalls.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/syscall.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/mbstate_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/types/wint_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wctype-wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/c++/15/algorithm \
  /usr/include/c++/15/array \
  /usr/include/c++/15/atomic \
  /usr/include/c++/15/backward/auto_ptr.h \
  /usr/include/c++/15/backward/binders.h \
  /usr/include/c++/15/bit \
  /usr/include/c++/15/bits/algorithmfwd.h \
  /usr/include/c++/15/bits/align.h \
  /usr/include/c++/15/bits/alloc_traits.h \
  /usr/include/c++/15/bits/allocated_ptr.h \
  /usr/include/c++/15/bits/allocator.h \
  /usr/include/c++/15/bits/atomic_base.h \
  /usr/include/c++/15/bits/atomic_lockfree_defines.h \
  /usr/include/c++/15/bits/atomic_wait.h \
  /usr/include/c++/15/bits/basic_ios.h \
  /usr/include/c++/15/bits/basic_ios.tcc \
  /usr/include/c++/15/bits/basic_string.h \
  /usr/include/c++/15/bits/basic_string.tcc \
  /usr/include/c++/15/bits/char_traits.h \
  /usr/include/c++/15/bits/charconv.h \
  /usr/include/c++/15/bits/chrono.h \
  /usr/include/c++/15/bits/chrono_io.h \
  /usr/include/c++/15/bits/codecvt.h \
  /usr/include/c++/15/bits/concept_check.h \
  /usr/include/c++/15/bits/cpp_type_traits.h \
  /usr/include/c++/15/bits/cxxabi_forced.h \
  /usr/include/c++/15/bits/cxxabi_init_exception.h \
  /usr/include/c++/15/bits/enable_special_members.h \
  /usr/include/c++/15/bits/erase_if.h \
  /usr/include/c++/15/bits/exception.h \
  /usr/include/c++/15/bits/exception_defines.h \
  /usr/include/c++/15/bits/exception_ptr.h \
  /usr/include/c++/15/bits/formatfwd.h \
  /usr/include/c++/15/bits/fs_dir.h \
  /usr/include/c++/15/bits/fs_fwd.h \
  /usr/include/c++/15/bits/fs_ops.h \
  /usr/include/c++/15/bits/fs_path.h \
  /usr/include/c++/15/bits/functexcept.h \
  /usr/include/c++/15/bits/functional_hash.h \
  /usr/include/c++/15/bits/hash_bytes.h \
  /usr/include/c++/15/bits/hashtable.h \
  /usr/include/c++/15/bits/hashtable_policy.h \
  /usr/include/c++/15/bits/invoke.h \
  /usr/include/c++/15/bits/ios_base.h \
  /usr/include/c++/15/bits/istream.tcc \
  /usr/include/c++/15/bits/iterator_concepts.h \
  /usr/include/c++/15/bits/list.tcc \
  /usr/include/c++/15/bits/locale_classes.h \
  /usr/include/c++/15/bits/locale_classes.tcc \
  /usr/include/c++/15/bits/locale_conv.h \
  /usr/include/c++/15/bits/locale_facets.h \
  /usr/include/c++/15/bits/locale_facets.tcc \
  /usr/include/c++/15/bits/locale_facets_nonio.h \
  /usr/include/c++/15/bits/locale_facets_nonio.tcc \
  /usr/include/c++/15/bits/localefwd.h \
  /usr/include/c++/15/bits/max_size_type.h \
  /usr/include/c++/15/bits/memory_resource.h \
  /usr/include/c++/15/bits/memoryfwd.h \
  /usr/include/c++/15/bits/monostate.h \
  /usr/include/c++/15/bits/move.h \
  /usr/include/c++/15/bits/nested_exception.h \
  /usr/include/c++/15/bits/new_allocator.h \
  /usr/include/c++/15/bits/node_handle.h \
  /usr/include/c++/15/bits/ostream.h \
  /usr/include/c++/15/bits/ostream.tcc \
  /usr/include/c++/15/bits/ostream_insert.h \
  /usr/include/c++/15/bits/parse_numbers.h \
  /usr/include/c++/15/bits/postypes.h \
  /usr/include/c++/15/bits/predefined_ops.h \
  /usr/include/c++/15/bits/ptr_traits.h \
  /usr/include/c++/15/bits/quoted_string.h \
  /usr/include/c++/15/bits/range_access.h \
  /usr/include/c++/15/bits/ranges_algo.h \
  /usr/include/c++/15/bits/ranges_algobase.h \
  /usr/include/c++/15/bits/ranges_base.h \
  /usr/include/c++/15/bits/ranges_cmp.h \
  /usr/include/c++/15/bits/ranges_uninitialized.h \
  /usr/include/c++/15/bits/ranges_util.h \
  /usr/include/c++/15/bits/refwrap.h \
  /usr/include/c++/15/bits/requires_hosted.h \
  /usr/include/c++/15/bits/shared_ptr.h \
  /usr/include/c++/15/bits/shared_ptr_atomic.h \
  /usr/include/c++/15/bits/shared_ptr_base.h \
  /usr/include/c++/15/bits/specfun.h \
  /usr/include/c++/15/bits/sstream.tcc \
  /usr/include/c++/15/bits/std_abs.h \
  /usr/include/c++/15/bits/std_function.h \
  /usr/include/c++/15/bits/std_mutex.h \
  /usr/include/c++/15/bits/stl_algo.h \
  /usr/include/c++/15/bits/stl_algobase.h \
  /usr/include/c++/15/bits/stl_bvector.h \
  /usr/include/c++/15/bits/stl_construct.h \
  /usr/include/c++/15/bits/stl_function.h \
  /usr/include/c++/15/bits/stl_heap.h \
  /usr/include/c++/15/bits/stl_iterator.h \
  /usr/include/c++/15/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/15/bits/stl_iterator_base_types.h \
  /usr/include/c++/15/bits/stl_list.h \
  /usr/include/c++/15/bits/stl_map.h \
  /usr/include/c++/15/bits/stl_multimap.h \
  /usr/include/c++/15/bits/stl_multiset.h \
  /usr/include/c++/15/bits/stl_numeric.h \
  /usr/include/c++/15/bits/stl_pair.h \
  /usr/include/c++/15/bits/stl_raw_storage_iter.h \
  /usr/include/c++/15/bits/stl_relops.h \
  /usr/include/c++/15/bits/stl_set.h \
  /usr/include/c++/15/bits/stl_tempbuf.h \
  /usr/include/c++/15/bits/stl_tree.h \
  /usr/include/c++/15/bits/stl_uninitialized.h \
  /usr/include/c++/15/bits/stl_vector.h \
  /usr/include/c++/15/bits/stream_iterator.h \
  /usr/include/c++/15/bits/streambuf.tcc \
  /usr/include/c++/15/bits/streambuf_iterator.h \
  /usr/include/c++/15/bits/string_view.tcc \
  /usr/include/c++/15/bits/stringfwd.h \
  /usr/include/c++/15/bits/unicode-data.h \
  /usr/include/c++/15/bits/unicode.h \
  /usr/include/c++/15/bits/uniform_int_dist.h \
  /usr/include/c++/15/bits/unique_ptr.h \
  /usr/include/c++/15/bits/unordered_map.h \
  /usr/include/c++/15/bits/unordered_set.h \
  /usr/include/c++/15/bits/uses_allocator.h \
  /usr/include/c++/15/bits/uses_allocator_args.h \
  /usr/include/c++/15/bits/utility.h \
  /usr/include/c++/15/bits/vector.tcc \
  /usr/include/c++/15/bits/version.h \
  /usr/include/c++/15/cassert \
  /usr/include/c++/15/cctype \
  /usr/include/c++/15/cerrno \
  /usr/include/c++/15/charconv \
  /usr/include/c++/15/chrono \
  /usr/include/c++/15/climits \
  /usr/include/c++/15/clocale \
  /usr/include/c++/15/cmath \
  /usr/include/c++/15/codecvt \
  /usr/include/c++/15/compare \
  /usr/include/c++/15/concepts \
  /usr/include/c++/15/cstddef \
  /usr/include/c++/15/cstdint \
  /usr/include/c++/15/cstdio \
  /usr/include/c++/15/cstdlib \
  /usr/include/c++/15/cstring \
  /usr/include/c++/15/ctime \
  /usr/include/c++/15/cwchar \
  /usr/include/c++/15/cwctype \
  /usr/include/c++/15/debug/assertions.h \
  /usr/include/c++/15/debug/debug.h \
  /usr/include/c++/15/exception \
  /usr/include/c++/15/ext/aligned_buffer.h \
  /usr/include/c++/15/ext/alloc_traits.h \
  /usr/include/c++/15/ext/atomicity.h \
  /usr/include/c++/15/ext/concurrence.h \
  /usr/include/c++/15/ext/numeric_traits.h \
  /usr/include/c++/15/ext/string_conversions.h \
  /usr/include/c++/15/ext/type_traits.h \
  /usr/include/c++/15/filesystem \
  /usr/include/c++/15/format \
  /usr/include/c++/15/functional \
  /usr/include/c++/15/initializer_list \
  /usr/include/c++/15/iomanip \
  /usr/include/c++/15/ios \
  /usr/include/c++/15/iosfwd \
  /usr/include/c++/15/istream \
  /usr/include/c++/15/iterator \
  /usr/include/c++/15/limits \
  /usr/include/c++/15/list \
  /usr/include/c++/15/locale \
  /usr/include/c++/15/map \
  /usr/include/c++/15/memory \
  /usr/include/c++/15/new \
  /usr/include/c++/15/numbers \
  /usr/include/c++/15/numeric \
  /usr/include/c++/15/optional \
  /usr/include/c++/15/ostream \
  /usr/include/c++/15/pstl/execution_defs.h \
  /usr/include/c++/15/pstl/glue_algorithm_defs.h \
  /usr/include/c++/15/pstl/glue_memory_defs.h \
  /usr/include/c++/15/pstl/glue_numeric_defs.h \
  /usr/include/c++/15/pstl/pstl_config.h \
  /usr/include/c++/15/ratio \
  /usr/include/c++/15/set \
  /usr/include/c++/15/span \
  /usr/include/c++/15/sstream \
  /usr/include/c++/15/stdexcept \
  /usr/include/c++/15/stdlib.h \
  /usr/include/c++/15/streambuf \
  /usr/include/c++/15/string \
  /usr/include/c++/15/string_view \
  /usr/include/c++/15/system_error \
  /usr/include/c++/15/tr1/bessel_function.tcc \
  /usr/include/c++/15/tr1/beta_function.tcc \
  /usr/include/c++/15/tr1/ell_integral.tcc \
  /usr/include/c++/15/tr1/exp_integral.tcc \
  /usr/include/c++/15/tr1/gamma.tcc \
  /usr/include/c++/15/tr1/hypergeometric.tcc \
  /usr/include/c++/15/tr1/legendre_function.tcc \
  /usr/include/c++/15/tr1/modified_bessel_func.tcc \
  /usr/include/c++/15/tr1/poly_hermite.tcc \
  /usr/include/c++/15/tr1/poly_laguerre.tcc \
  /usr/include/c++/15/tr1/riemann_zeta.tcc \
  /usr/include/c++/15/tr1/special_function_util.h \
  /usr/include/c++/15/tuple \
  /usr/include/c++/15/type_traits \
  /usr/include/c++/15/typeinfo \
  /usr/include/c++/15/unordered_map \
  /usr/include/c++/15/unordered_set \
  /usr/include/c++/15/utility \
  /usr/include/c++/15/variant \
  /usr/include/c++/15/vector \
  /usr/include/c++/15/version \
  /usr/include/c++/15/x86_64-redhat-linux/bits/atomic_word.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/c++allocator.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/c++config.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/c++locale.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/cpu_defines.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/ctype_base.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/ctype_inline.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/error_constants.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/gthr-default.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/gthr.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/messages_members.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/os_defines.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/time_members.h \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/libintl.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/locale.h \
  /usr/include/math.h \
  /usr/include/pthread.h \
  /usr/include/qt6/QtCore/QDir \
  /usr/include/qt6/QtCore/QList \
  /usr/include/qt6/QtCore/QMimeData \
  /usr/include/qt6/QtCore/QObject \
  /usr/include/qt6/QtCore/QRect \
  /usr/include/qt6/QtCore/QSize \
  /usr/include/qt6/QtCore/QSizeF \
  /usr/include/qt6/QtCore/QTemporaryDir \
  /usr/include/qt6/QtCore/q17memory.h \
  /usr/include/qt6/QtCore/q20functional.h \
  /usr/include/qt6/QtCore/q20iterator.h \
  /usr/include/qt6/QtCore/q20memory.h \
  /usr/include/qt6/QtCore/q20type_traits.h \
  /usr/include/qt6/QtCore/q20utility.h \
  /usr/include/qt6/QtCore/q23utility.h \
  /usr/include/qt6/QtCore/qabstracteventdispatcher.h \
  /usr/include/qt6/QtCore/qabstractitemmodel.h \
  /usr/include/qt6/QtCore/qalgorithms.h \
  /usr/include/qt6/QtCore/qanystringview.h \
  /usr/include/qt6/QtCore/qarraydata.h \
  /usr/include/qt6/QtCore/qarraydataops.h \
  /usr/include/qt6/QtCore/qarraydatapointer.h \
  /usr/include/qt6/QtCore/qassert.h \
  /usr/include/qt6/QtCore/qatomic.h \
  /usr/include/qt6/QtCore/qatomic_cxx11.h \
  /usr/include/qt6/QtCore/qbasicatomic.h \
  /usr/include/qt6/QtCore/qbasictimer.h \
  /usr/include/qt6/QtCore/qbindingstorage.h \
  /usr/include/qt6/QtCore/qbytearray.h \
  /usr/include/qt6/QtCore/qbytearrayalgorithms.h \
  /usr/include/qt6/QtCore/qbytearraylist.h \
  /usr/include/qt6/QtCore/qbytearrayview.h \
  /usr/include/qt6/QtCore/qcalendar.h \
  /usr/include/qt6/QtCore/qchar.h \
  /usr/include/qt6/QtCore/qcompare.h \
  /usr/include/qt6/QtCore/qcompare_impl.h \
  /usr/include/qt6/QtCore/qcomparehelpers.h \
  /usr/include/qt6/QtCore/qcompilerdetection.h \
  /usr/include/qt6/QtCore/qconfig-64.h \
  /usr/include/qt6/QtCore/qconfig.h \
  /usr/include/qt6/QtCore/qconstructormacros.h \
  /usr/include/qt6/QtCore/qcontainerfwd.h \
  /usr/include/qt6/QtCore/qcontainerinfo.h \
  /usr/include/qt6/QtCore/qcontainertools_impl.h \
  /usr/include/qt6/QtCore/qcontiguouscache.h \
  /usr/include/qt6/QtCore/qcoreapplication.h \
  /usr/include/qt6/QtCore/qcoreapplication_platform.h \
  /usr/include/qt6/QtCore/qcoreevent.h \
  /usr/include/qt6/QtCore/qdarwinhelpers.h \
  /usr/include/qt6/QtCore/qdatastream.h \
  /usr/include/qt6/QtCore/qdatetime.h \
  /usr/include/qt6/QtCore/qdeadlinetimer.h \
  /usr/include/qt6/QtCore/qdebug.h \
  /usr/include/qt6/QtCore/qdir.h \
  /usr/include/qt6/QtCore/qdirlisting.h \
  /usr/include/qt6/QtCore/qelapsedtimer.h \
  /usr/include/qt6/QtCore/qendian.h \
  /usr/include/qt6/QtCore/qeventloop.h \
  /usr/include/qt6/QtCore/qexceptionhandling.h \
  /usr/include/qt6/QtCore/qfile.h \
  /usr/include/qt6/QtCore/qfiledevice.h \
  /usr/include/qt6/QtCore/qfileinfo.h \
  /usr/include/qt6/QtCore/qflags.h \
  /usr/include/qt6/QtCore/qfloat16.h \
  /usr/include/qt6/QtCore/qforeach.h \
  /usr/include/qt6/QtCore/qfunctionaltools_impl.h \
  /usr/include/qt6/QtCore/qfunctionpointer.h \
  /usr/include/qt6/QtCore/qgenericatomic.h \
  /usr/include/qt6/QtCore/qglobal.h \
  /usr/include/qt6/QtCore/qglobalstatic.h \
  /usr/include/qt6/QtCore/qhash.h \
  /usr/include/qt6/QtCore/qhashfunctions.h \
  /usr/include/qt6/QtCore/qiodevice.h \
  /usr/include/qt6/QtCore/qiodevicebase.h \
  /usr/include/qt6/QtCore/qiterable.h \
  /usr/include/qt6/QtCore/qiterator.h \
  /usr/include/qt6/QtCore/qlatin1stringview.h \
  /usr/include/qt6/QtCore/qline.h \
  /usr/include/qt6/QtCore/qlist.h \
  /usr/include/qt6/QtCore/qlocale.h \
  /usr/include/qt6/QtCore/qlogging.h \
  /usr/include/qt6/QtCore/qmalloc.h \
  /usr/include/qt6/QtCore/qmap.h \
  /usr/include/qt6/QtCore/qmargins.h \
  /usr/include/qt6/QtCore/qmath.h \
  /usr/include/qt6/QtCore/qmetacontainer.h \
  /usr/include/qt6/QtCore/qmetatype.h \
  /usr/include/qt6/QtCore/qmimedata.h \
  /usr/include/qt6/QtCore/qminmax.h \
  /usr/include/qt6/QtCore/qnamespace.h \
  /usr/include/qt6/QtCore/qnativeinterface.h \
  /usr/include/qt6/QtCore/qnumeric.h \
  /usr/include/qt6/QtCore/qobject.h \
  /usr/include/qt6/QtCore/qobject_impl.h \
  /usr/include/qt6/QtCore/qobjectdefs.h \
  /usr/include/qt6/QtCore/qobjectdefs_impl.h \
  /usr/include/qt6/QtCore/qoverload.h \
  /usr/include/qt6/QtCore/qpair.h \
  /usr/include/qt6/QtCore/qpoint.h \
  /usr/include/qt6/QtCore/qprocessordetection.h \
  /usr/include/qt6/QtCore/qrect.h \
  /usr/include/qt6/QtCore/qrefcount.h \
  /usr/include/qt6/QtCore/qregularexpression.h \
  /usr/include/qt6/QtCore/qscopedpointer.h \
  /usr/include/qt6/QtCore/qscopeguard.h \
  /usr/include/qt6/QtCore/qset.h \
  /usr/include/qt6/QtCore/qshareddata.h \
  /usr/include/qt6/QtCore/qshareddata_impl.h \
  /usr/include/qt6/QtCore/qsharedpointer.h \
  /usr/include/qt6/QtCore/qsharedpointer_impl.h \
  /usr/include/qt6/QtCore/qsize.h \
  /usr/include/qt6/QtCore/qspan.h \
  /usr/include/qt6/QtCore/qstdlibdetection.h \
  /usr/include/qt6/QtCore/qstring.h \
  /usr/include/qt6/QtCore/qstringalgorithms.h \
  /usr/include/qt6/QtCore/qstringbuilder.h \
  /usr/include/qt6/QtCore/qstringconverter.h \
  /usr/include/qt6/QtCore/qstringconverter_base.h \
  /usr/include/qt6/QtCore/qstringfwd.h \
  /usr/include/qt6/QtCore/qstringlist.h \
  /usr/include/qt6/QtCore/qstringliteral.h \
  /usr/include/qt6/QtCore/qstringmatcher.h \
  /usr/include/qt6/QtCore/qstringtokenizer.h \
  /usr/include/qt6/QtCore/qstringview.h \
  /usr/include/qt6/QtCore/qswap.h \
  /usr/include/qt6/QtCore/qsysinfo.h \
  /usr/include/qt6/QtCore/qsystemdetection.h \
  /usr/include/qt6/QtCore/qtaggedpointer.h \
  /usr/include/qt6/QtCore/qtclasshelpermacros.h \
  /usr/include/qt6/QtCore/qtconfiginclude.h \
  /usr/include/qt6/QtCore/qtconfigmacros.h \
  /usr/include/qt6/QtCore/qtcore-config.h \
  /usr/include/qt6/QtCore/qtcoreexports.h \
  /usr/include/qt6/QtCore/qtcoreglobal.h \
  /usr/include/qt6/QtCore/qtdeprecationdefinitions.h \
  /usr/include/qt6/QtCore/qtdeprecationmarkers.h \
  /usr/include/qt6/QtCore/qtemporarydir.h \
  /usr/include/qt6/QtCore/qtenvironmentvariables.h \
  /usr/include/qt6/QtCore/qtextstream.h \
  /usr/include/qt6/QtCore/qtformat_impl.h \
  /usr/include/qt6/QtCore/qtimezone.h \
  /usr/include/qt6/QtCore/qtmetamacros.h \
  /usr/include/qt6/QtCore/qtnoop.h \
  /usr/include/qt6/QtCore/qtpreprocessorsupport.h \
  /usr/include/qt6/QtCore/qtresource.h \
  /usr/include/qt6/QtCore/qttranslation.h \
  /usr/include/qt6/QtCore/qttypetraits.h \
  /usr/include/qt6/QtCore/qtversion.h \
  /usr/include/qt6/QtCore/qtversionchecks.h \
  /usr/include/qt6/QtCore/qtypeinfo.h \
  /usr/include/qt6/QtCore/qtypes.h \
  /usr/include/qt6/QtCore/qurl.h \
  /usr/include/qt6/QtCore/qutf8stringview.h \
  /usr/include/qt6/QtCore/qvariant.h \
  /usr/include/qt6/QtCore/qvarlengtharray.h \
  /usr/include/qt6/QtCore/qversiontagging.h \
  /usr/include/qt6/QtCore/qxptype_traits.h \
  /usr/include/qt6/QtCore/qyieldcpu.h \
  /usr/include/qt6/QtGui/QAction \
  /usr/include/qt6/QtGui/QIcon \
  /usr/include/qt6/QtGui/QKeyEvent \
  /usr/include/qt6/QtGui/QTextBlockFormat \
  /usr/include/qt6/QtGui/QTextCharFormat \
  /usr/include/qt6/QtGui/QTextCursor \
  /usr/include/qt6/QtGui/QTextImageFormat \
  /usr/include/qt6/QtGui/QTextList \
  /usr/include/qt6/QtGui/QTextTable \
  /usr/include/qt6/QtGui/QTextTableFormat \
  /usr/include/qt6/QtGui/QTransform \
  /usr/include/qt6/QtGui/qaction.h \
  /usr/include/qt6/QtGui/qbitmap.h \
  /usr/include/qt6/QtGui/qbrush.h \
  /usr/include/qt6/QtGui/qcolor.h \
  /usr/include/qt6/QtGui/qcursor.h \
  /usr/include/qt6/QtGui/qevent.h \
  /usr/include/qt6/QtGui/qeventpoint.h \
  /usr/include/qt6/QtGui/qfont.h \
  /usr/include/qt6/QtGui/qfontdatabase.h \
  /usr/include/qt6/QtGui/qfontinfo.h \
  /usr/include/qt6/QtGui/qfontmetrics.h \
  /usr/include/qt6/QtGui/qfontvariableaxis.h \
  /usr/include/qt6/QtGui/qglyphrun.h \
  /usr/include/qt6/QtGui/qguiapplication.h \
  /usr/include/qt6/QtGui/qguiapplication_platform.h \
  /usr/include/qt6/QtGui/qicon.h \
  /usr/include/qt6/QtGui/qimage.h \
  /usr/include/qt6/QtGui/qinputdevice.h \
  /usr/include/qt6/QtGui/qinputmethod.h \
  /usr/include/qt6/QtGui/qkeysequence.h \
  /usr/include/qt6/QtGui/qpaintdevice.h \
  /usr/include/qt6/QtGui/qpalette.h \
  /usr/include/qt6/QtGui/qpen.h \
  /usr/include/qt6/QtGui/qpixelformat.h \
  /usr/include/qt6/QtGui/qpixmap.h \
  /usr/include/qt6/QtGui/qpointingdevice.h \
  /usr/include/qt6/QtGui/qpolygon.h \
  /usr/include/qt6/QtGui/qrawfont.h \
  /usr/include/qt6/QtGui/qregion.h \
  /usr/include/qt6/QtGui/qrgb.h \
  /usr/include/qt6/QtGui/qrgba64.h \
  /usr/include/qt6/QtGui/qscreen.h \
  /usr/include/qt6/QtGui/qscreen_platform.h \
  /usr/include/qt6/QtGui/qtextcursor.h \
  /usr/include/qt6/QtGui/qtextdocument.h \
  /usr/include/qt6/QtGui/qtextformat.h \
  /usr/include/qt6/QtGui/qtextlayout.h \
  /usr/include/qt6/QtGui/qtextlist.h \
  /usr/include/qt6/QtGui/qtextobject.h \
  /usr/include/qt6/QtGui/qtextoption.h \
  /usr/include/qt6/QtGui/qtexttable.h \
  /usr/include/qt6/QtGui/qtgui-config.h \
  /usr/include/qt6/QtGui/qtguiexports.h \
  /usr/include/qt6/QtGui/qtguiglobal.h \
  /usr/include/qt6/QtGui/qtransform.h \
  /usr/include/qt6/QtGui/qvalidator.h \
  /usr/include/qt6/QtGui/qvector2d.h \
  /usr/include/qt6/QtGui/qvectornd.h \
  /usr/include/qt6/QtGui/qwindowdefs.h \
  /usr/include/qt6/QtWidgets/QApplication \
  /usr/include/qt6/QtWidgets/QColorDialog \
  /usr/include/qt6/QtWidgets/QFileDialog \
  /usr/include/qt6/QtWidgets/QFontComboBox \
  /usr/include/qt6/QtWidgets/QInputDialog \
  /usr/include/qt6/QtWidgets/QSpinBox \
  /usr/include/qt6/QtWidgets/QTextEdit \
  /usr/include/qt6/QtWidgets/QToolBar \
  /usr/include/qt6/QtWidgets/qabstractitemdelegate.h \
  /usr/include/qt6/QtWidgets/qabstractscrollarea.h \
  /usr/include/qt6/QtWidgets/qabstractslider.h \
  /usr/include/qt6/QtWidgets/qabstractspinbox.h \
  /usr/include/qt6/QtWidgets/qapplication.h \
  /usr/include/qt6/QtWidgets/qcolordialog.h \
  /usr/include/qt6/QtWidgets/qcombobox.h \
  /usr/include/qt6/QtWidgets/qdialog.h \
  /usr/include/qt6/QtWidgets/qfiledialog.h \
  /usr/include/qt6/QtWidgets/qfontcombobox.h \
  /usr/include/qt6/QtWidgets/qframe.h \
  /usr/include/qt6/QtWidgets/qinputdialog.h \
  /usr/include/qt6/QtWidgets/qlineedit.h \
  /usr/include/qt6/QtWidgets/qrubberband.h \
  /usr/include/qt6/QtWidgets/qsizepolicy.h \
  /usr/include/qt6/QtWidgets/qslider.h \
  /usr/include/qt6/QtWidgets/qspinbox.h \
  /usr/include/qt6/QtWidgets/qstyle.h \
  /usr/include/qt6/QtWidgets/qstyleoption.h \
  /usr/include/qt6/QtWidgets/qtabbar.h \
  /usr/include/qt6/QtWidgets/qtabwidget.h \
  /usr/include/qt6/QtWidgets/qtextedit.h \
  /usr/include/qt6/QtWidgets/qtoolbar.h \
  /usr/include/qt6/QtWidgets/qtwidgets-config.h \
  /usr/include/qt6/QtWidgets/qtwidgetsexports.h \
  /usr/include/qt6/QtWidgets/qtwidgetsglobal.h \
  /usr/include/qt6/QtWidgets/qwidget.h \
  /usr/include/sched.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/select.h \
  /usr/include/sys/single_threaded.h \
  /usr/include/sys/syscall.h \
  /usr/include/sys/types.h \
  /usr/include/syscall.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/include/wchar.h \
  /usr/include/wctype.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/limits.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stdarg.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stdbool.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stddef.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stdint.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/syslimits.h

CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.o: /home/<USER>/CLionProjects/KNoteDo/app/SettingsDialog.cpp \
  /home/<USER>/CLionProjects/KNoteDo/app/SettingsDialog.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/posix_types_64.h \
  /usr/include/asm/types.h \
  /usr/include/asm/unistd.h \
  /usr/include/asm/unistd_64.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/flt-eval-method.h \
  /usr/include/bits/fp-fast.h \
  /usr/include/bits/fp-logb.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/iscanonical.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/libm-simd-decl-stubs.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/locale.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/math-vector.h \
  /usr/include/bits/mathcalls-helper-functions.h \
  /usr/include/bits/mathcalls-macros.h \
  /usr/include/bits/mathcalls-narrow.h \
  /usr/include/bits/mathcalls.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/syscall.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/mbstate_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/types/wint_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wctype-wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/c++/15/algorithm \
  /usr/include/c++/15/array \
  /usr/include/c++/15/atomic \
  /usr/include/c++/15/backward/auto_ptr.h \
  /usr/include/c++/15/backward/binders.h \
  /usr/include/c++/15/bit \
  /usr/include/c++/15/bits/algorithmfwd.h \
  /usr/include/c++/15/bits/align.h \
  /usr/include/c++/15/bits/alloc_traits.h \
  /usr/include/c++/15/bits/allocated_ptr.h \
  /usr/include/c++/15/bits/allocator.h \
  /usr/include/c++/15/bits/atomic_base.h \
  /usr/include/c++/15/bits/atomic_lockfree_defines.h \
  /usr/include/c++/15/bits/atomic_wait.h \
  /usr/include/c++/15/bits/basic_ios.h \
  /usr/include/c++/15/bits/basic_ios.tcc \
  /usr/include/c++/15/bits/basic_string.h \
  /usr/include/c++/15/bits/basic_string.tcc \
  /usr/include/c++/15/bits/char_traits.h \
  /usr/include/c++/15/bits/charconv.h \
  /usr/include/c++/15/bits/chrono.h \
  /usr/include/c++/15/bits/chrono_io.h \
  /usr/include/c++/15/bits/codecvt.h \
  /usr/include/c++/15/bits/concept_check.h \
  /usr/include/c++/15/bits/cpp_type_traits.h \
  /usr/include/c++/15/bits/cxxabi_forced.h \
  /usr/include/c++/15/bits/cxxabi_init_exception.h \
  /usr/include/c++/15/bits/enable_special_members.h \
  /usr/include/c++/15/bits/erase_if.h \
  /usr/include/c++/15/bits/exception.h \
  /usr/include/c++/15/bits/exception_defines.h \
  /usr/include/c++/15/bits/exception_ptr.h \
  /usr/include/c++/15/bits/formatfwd.h \
  /usr/include/c++/15/bits/fs_dir.h \
  /usr/include/c++/15/bits/fs_fwd.h \
  /usr/include/c++/15/bits/fs_ops.h \
  /usr/include/c++/15/bits/fs_path.h \
  /usr/include/c++/15/bits/functexcept.h \
  /usr/include/c++/15/bits/functional_hash.h \
  /usr/include/c++/15/bits/hash_bytes.h \
  /usr/include/c++/15/bits/hashtable.h \
  /usr/include/c++/15/bits/hashtable_policy.h \
  /usr/include/c++/15/bits/invoke.h \
  /usr/include/c++/15/bits/ios_base.h \
  /usr/include/c++/15/bits/istream.tcc \
  /usr/include/c++/15/bits/iterator_concepts.h \
  /usr/include/c++/15/bits/list.tcc \
  /usr/include/c++/15/bits/locale_classes.h \
  /usr/include/c++/15/bits/locale_classes.tcc \
  /usr/include/c++/15/bits/locale_conv.h \
  /usr/include/c++/15/bits/locale_facets.h \
  /usr/include/c++/15/bits/locale_facets.tcc \
  /usr/include/c++/15/bits/locale_facets_nonio.h \
  /usr/include/c++/15/bits/locale_facets_nonio.tcc \
  /usr/include/c++/15/bits/localefwd.h \
  /usr/include/c++/15/bits/max_size_type.h \
  /usr/include/c++/15/bits/memory_resource.h \
  /usr/include/c++/15/bits/memoryfwd.h \
  /usr/include/c++/15/bits/monostate.h \
  /usr/include/c++/15/bits/move.h \
  /usr/include/c++/15/bits/nested_exception.h \
  /usr/include/c++/15/bits/new_allocator.h \
  /usr/include/c++/15/bits/node_handle.h \
  /usr/include/c++/15/bits/ostream.h \
  /usr/include/c++/15/bits/ostream.tcc \
  /usr/include/c++/15/bits/ostream_insert.h \
  /usr/include/c++/15/bits/parse_numbers.h \
  /usr/include/c++/15/bits/postypes.h \
  /usr/include/c++/15/bits/predefined_ops.h \
  /usr/include/c++/15/bits/ptr_traits.h \
  /usr/include/c++/15/bits/quoted_string.h \
  /usr/include/c++/15/bits/range_access.h \
  /usr/include/c++/15/bits/ranges_algo.h \
  /usr/include/c++/15/bits/ranges_algobase.h \
  /usr/include/c++/15/bits/ranges_base.h \
  /usr/include/c++/15/bits/ranges_cmp.h \
  /usr/include/c++/15/bits/ranges_uninitialized.h \
  /usr/include/c++/15/bits/ranges_util.h \
  /usr/include/c++/15/bits/refwrap.h \
  /usr/include/c++/15/bits/requires_hosted.h \
  /usr/include/c++/15/bits/shared_ptr.h \
  /usr/include/c++/15/bits/shared_ptr_atomic.h \
  /usr/include/c++/15/bits/shared_ptr_base.h \
  /usr/include/c++/15/bits/specfun.h \
  /usr/include/c++/15/bits/sstream.tcc \
  /usr/include/c++/15/bits/std_abs.h \
  /usr/include/c++/15/bits/std_function.h \
  /usr/include/c++/15/bits/std_mutex.h \
  /usr/include/c++/15/bits/stl_algo.h \
  /usr/include/c++/15/bits/stl_algobase.h \
  /usr/include/c++/15/bits/stl_bvector.h \
  /usr/include/c++/15/bits/stl_construct.h \
  /usr/include/c++/15/bits/stl_function.h \
  /usr/include/c++/15/bits/stl_heap.h \
  /usr/include/c++/15/bits/stl_iterator.h \
  /usr/include/c++/15/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/15/bits/stl_iterator_base_types.h \
  /usr/include/c++/15/bits/stl_list.h \
  /usr/include/c++/15/bits/stl_map.h \
  /usr/include/c++/15/bits/stl_multimap.h \
  /usr/include/c++/15/bits/stl_multiset.h \
  /usr/include/c++/15/bits/stl_numeric.h \
  /usr/include/c++/15/bits/stl_pair.h \
  /usr/include/c++/15/bits/stl_raw_storage_iter.h \
  /usr/include/c++/15/bits/stl_relops.h \
  /usr/include/c++/15/bits/stl_set.h \
  /usr/include/c++/15/bits/stl_tempbuf.h \
  /usr/include/c++/15/bits/stl_tree.h \
  /usr/include/c++/15/bits/stl_uninitialized.h \
  /usr/include/c++/15/bits/stl_vector.h \
  /usr/include/c++/15/bits/stream_iterator.h \
  /usr/include/c++/15/bits/streambuf.tcc \
  /usr/include/c++/15/bits/streambuf_iterator.h \
  /usr/include/c++/15/bits/string_view.tcc \
  /usr/include/c++/15/bits/stringfwd.h \
  /usr/include/c++/15/bits/unicode-data.h \
  /usr/include/c++/15/bits/unicode.h \
  /usr/include/c++/15/bits/uniform_int_dist.h \
  /usr/include/c++/15/bits/unique_ptr.h \
  /usr/include/c++/15/bits/unordered_map.h \
  /usr/include/c++/15/bits/unordered_set.h \
  /usr/include/c++/15/bits/uses_allocator.h \
  /usr/include/c++/15/bits/uses_allocator_args.h \
  /usr/include/c++/15/bits/utility.h \
  /usr/include/c++/15/bits/vector.tcc \
  /usr/include/c++/15/bits/version.h \
  /usr/include/c++/15/cassert \
  /usr/include/c++/15/cctype \
  /usr/include/c++/15/cerrno \
  /usr/include/c++/15/charconv \
  /usr/include/c++/15/chrono \
  /usr/include/c++/15/climits \
  /usr/include/c++/15/clocale \
  /usr/include/c++/15/cmath \
  /usr/include/c++/15/codecvt \
  /usr/include/c++/15/compare \
  /usr/include/c++/15/concepts \
  /usr/include/c++/15/cstddef \
  /usr/include/c++/15/cstdint \
  /usr/include/c++/15/cstdio \
  /usr/include/c++/15/cstdlib \
  /usr/include/c++/15/cstring \
  /usr/include/c++/15/ctime \
  /usr/include/c++/15/cwchar \
  /usr/include/c++/15/cwctype \
  /usr/include/c++/15/debug/assertions.h \
  /usr/include/c++/15/debug/debug.h \
  /usr/include/c++/15/exception \
  /usr/include/c++/15/ext/aligned_buffer.h \
  /usr/include/c++/15/ext/alloc_traits.h \
  /usr/include/c++/15/ext/atomicity.h \
  /usr/include/c++/15/ext/concurrence.h \
  /usr/include/c++/15/ext/numeric_traits.h \
  /usr/include/c++/15/ext/string_conversions.h \
  /usr/include/c++/15/ext/type_traits.h \
  /usr/include/c++/15/filesystem \
  /usr/include/c++/15/format \
  /usr/include/c++/15/functional \
  /usr/include/c++/15/initializer_list \
  /usr/include/c++/15/iomanip \
  /usr/include/c++/15/ios \
  /usr/include/c++/15/iosfwd \
  /usr/include/c++/15/istream \
  /usr/include/c++/15/iterator \
  /usr/include/c++/15/limits \
  /usr/include/c++/15/list \
  /usr/include/c++/15/locale \
  /usr/include/c++/15/map \
  /usr/include/c++/15/memory \
  /usr/include/c++/15/new \
  /usr/include/c++/15/numbers \
  /usr/include/c++/15/numeric \
  /usr/include/c++/15/optional \
  /usr/include/c++/15/ostream \
  /usr/include/c++/15/pstl/execution_defs.h \
  /usr/include/c++/15/pstl/glue_algorithm_defs.h \
  /usr/include/c++/15/pstl/glue_memory_defs.h \
  /usr/include/c++/15/pstl/glue_numeric_defs.h \
  /usr/include/c++/15/pstl/pstl_config.h \
  /usr/include/c++/15/ratio \
  /usr/include/c++/15/set \
  /usr/include/c++/15/span \
  /usr/include/c++/15/sstream \
  /usr/include/c++/15/stdexcept \
  /usr/include/c++/15/stdlib.h \
  /usr/include/c++/15/streambuf \
  /usr/include/c++/15/string \
  /usr/include/c++/15/string_view \
  /usr/include/c++/15/system_error \
  /usr/include/c++/15/tr1/bessel_function.tcc \
  /usr/include/c++/15/tr1/beta_function.tcc \
  /usr/include/c++/15/tr1/ell_integral.tcc \
  /usr/include/c++/15/tr1/exp_integral.tcc \
  /usr/include/c++/15/tr1/gamma.tcc \
  /usr/include/c++/15/tr1/hypergeometric.tcc \
  /usr/include/c++/15/tr1/legendre_function.tcc \
  /usr/include/c++/15/tr1/modified_bessel_func.tcc \
  /usr/include/c++/15/tr1/poly_hermite.tcc \
  /usr/include/c++/15/tr1/poly_laguerre.tcc \
  /usr/include/c++/15/tr1/riemann_zeta.tcc \
  /usr/include/c++/15/tr1/special_function_util.h \
  /usr/include/c++/15/tuple \
  /usr/include/c++/15/type_traits \
  /usr/include/c++/15/typeinfo \
  /usr/include/c++/15/unordered_map \
  /usr/include/c++/15/unordered_set \
  /usr/include/c++/15/utility \
  /usr/include/c++/15/variant \
  /usr/include/c++/15/vector \
  /usr/include/c++/15/version \
  /usr/include/c++/15/x86_64-redhat-linux/bits/atomic_word.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/c++allocator.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/c++config.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/c++locale.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/cpu_defines.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/ctype_base.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/ctype_inline.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/error_constants.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/gthr-default.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/gthr.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/messages_members.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/os_defines.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/time_members.h \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/libintl.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/locale.h \
  /usr/include/math.h \
  /usr/include/pthread.h \
  /usr/include/qt6/QtCore/QStandardPaths \
  /usr/include/qt6/QtCore/q17memory.h \
  /usr/include/qt6/QtCore/q20functional.h \
  /usr/include/qt6/QtCore/q20iterator.h \
  /usr/include/qt6/QtCore/q20memory.h \
  /usr/include/qt6/QtCore/q20type_traits.h \
  /usr/include/qt6/QtCore/q20utility.h \
  /usr/include/qt6/QtCore/q23utility.h \
  /usr/include/qt6/QtCore/qabstractitemmodel.h \
  /usr/include/qt6/QtCore/qalgorithms.h \
  /usr/include/qt6/QtCore/qanystringview.h \
  /usr/include/qt6/QtCore/qarraydata.h \
  /usr/include/qt6/QtCore/qarraydataops.h \
  /usr/include/qt6/QtCore/qarraydatapointer.h \
  /usr/include/qt6/QtCore/qassert.h \
  /usr/include/qt6/QtCore/qatomic.h \
  /usr/include/qt6/QtCore/qatomic_cxx11.h \
  /usr/include/qt6/QtCore/qbasicatomic.h \
  /usr/include/qt6/QtCore/qbindingstorage.h \
  /usr/include/qt6/QtCore/qbytearray.h \
  /usr/include/qt6/QtCore/qbytearrayalgorithms.h \
  /usr/include/qt6/QtCore/qbytearraylist.h \
  /usr/include/qt6/QtCore/qbytearrayview.h \
  /usr/include/qt6/QtCore/qcalendar.h \
  /usr/include/qt6/QtCore/qchar.h \
  /usr/include/qt6/QtCore/qcompare.h \
  /usr/include/qt6/QtCore/qcompare_impl.h \
  /usr/include/qt6/QtCore/qcomparehelpers.h \
  /usr/include/qt6/QtCore/qcompilerdetection.h \
  /usr/include/qt6/QtCore/qconfig-64.h \
  /usr/include/qt6/QtCore/qconfig.h \
  /usr/include/qt6/QtCore/qconstructormacros.h \
  /usr/include/qt6/QtCore/qcontainerfwd.h \
  /usr/include/qt6/QtCore/qcontainerinfo.h \
  /usr/include/qt6/QtCore/qcontainertools_impl.h \
  /usr/include/qt6/QtCore/qcontiguouscache.h \
  /usr/include/qt6/QtCore/qdarwinhelpers.h \
  /usr/include/qt6/QtCore/qdatastream.h \
  /usr/include/qt6/QtCore/qdatetime.h \
  /usr/include/qt6/QtCore/qdebug.h \
  /usr/include/qt6/QtCore/qdir.h \
  /usr/include/qt6/QtCore/qdirlisting.h \
  /usr/include/qt6/QtCore/qendian.h \
  /usr/include/qt6/QtCore/qexceptionhandling.h \
  /usr/include/qt6/QtCore/qfile.h \
  /usr/include/qt6/QtCore/qfiledevice.h \
  /usr/include/qt6/QtCore/qfileinfo.h \
  /usr/include/qt6/QtCore/qflags.h \
  /usr/include/qt6/QtCore/qfloat16.h \
  /usr/include/qt6/QtCore/qforeach.h \
  /usr/include/qt6/QtCore/qfunctionaltools_impl.h \
  /usr/include/qt6/QtCore/qfunctionpointer.h \
  /usr/include/qt6/QtCore/qgenericatomic.h \
  /usr/include/qt6/QtCore/qglobal.h \
  /usr/include/qt6/QtCore/qglobalstatic.h \
  /usr/include/qt6/QtCore/qhash.h \
  /usr/include/qt6/QtCore/qhashfunctions.h \
  /usr/include/qt6/QtCore/qiodevice.h \
  /usr/include/qt6/QtCore/qiodevicebase.h \
  /usr/include/qt6/QtCore/qiterable.h \
  /usr/include/qt6/QtCore/qiterator.h \
  /usr/include/qt6/QtCore/qlatin1stringview.h \
  /usr/include/qt6/QtCore/qline.h \
  /usr/include/qt6/QtCore/qlist.h \
  /usr/include/qt6/QtCore/qlocale.h \
  /usr/include/qt6/QtCore/qlogging.h \
  /usr/include/qt6/QtCore/qmalloc.h \
  /usr/include/qt6/QtCore/qmap.h \
  /usr/include/qt6/QtCore/qmargins.h \
  /usr/include/qt6/QtCore/qmath.h \
  /usr/include/qt6/QtCore/qmetacontainer.h \
  /usr/include/qt6/QtCore/qmetatype.h \
  /usr/include/qt6/QtCore/qminmax.h \
  /usr/include/qt6/QtCore/qnamespace.h \
  /usr/include/qt6/QtCore/qnumeric.h \
  /usr/include/qt6/QtCore/qobject.h \
  /usr/include/qt6/QtCore/qobject_impl.h \
  /usr/include/qt6/QtCore/qobjectdefs.h \
  /usr/include/qt6/QtCore/qobjectdefs_impl.h \
  /usr/include/qt6/QtCore/qoverload.h \
  /usr/include/qt6/QtCore/qpair.h \
  /usr/include/qt6/QtCore/qpoint.h \
  /usr/include/qt6/QtCore/qprocessordetection.h \
  /usr/include/qt6/QtCore/qrect.h \
  /usr/include/qt6/QtCore/qrefcount.h \
  /usr/include/qt6/QtCore/qregularexpression.h \
  /usr/include/qt6/QtCore/qscopedpointer.h \
  /usr/include/qt6/QtCore/qscopeguard.h \
  /usr/include/qt6/QtCore/qset.h \
  /usr/include/qt6/QtCore/qshareddata.h \
  /usr/include/qt6/QtCore/qshareddata_impl.h \
  /usr/include/qt6/QtCore/qsharedpointer.h \
  /usr/include/qt6/QtCore/qsharedpointer_impl.h \
  /usr/include/qt6/QtCore/qsize.h \
  /usr/include/qt6/QtCore/qspan.h \
  /usr/include/qt6/QtCore/qstandardpaths.h \
  /usr/include/qt6/QtCore/qstdlibdetection.h \
  /usr/include/qt6/QtCore/qstring.h \
  /usr/include/qt6/QtCore/qstringalgorithms.h \
  /usr/include/qt6/QtCore/qstringbuilder.h \
  /usr/include/qt6/QtCore/qstringconverter.h \
  /usr/include/qt6/QtCore/qstringconverter_base.h \
  /usr/include/qt6/QtCore/qstringfwd.h \
  /usr/include/qt6/QtCore/qstringlist.h \
  /usr/include/qt6/QtCore/qstringliteral.h \
  /usr/include/qt6/QtCore/qstringmatcher.h \
  /usr/include/qt6/QtCore/qstringtokenizer.h \
  /usr/include/qt6/QtCore/qstringview.h \
  /usr/include/qt6/QtCore/qswap.h \
  /usr/include/qt6/QtCore/qsysinfo.h \
  /usr/include/qt6/QtCore/qsystemdetection.h \
  /usr/include/qt6/QtCore/qtaggedpointer.h \
  /usr/include/qt6/QtCore/qtclasshelpermacros.h \
  /usr/include/qt6/QtCore/qtconfiginclude.h \
  /usr/include/qt6/QtCore/qtconfigmacros.h \
  /usr/include/qt6/QtCore/qtcore-config.h \
  /usr/include/qt6/QtCore/qtcoreexports.h \
  /usr/include/qt6/QtCore/qtcoreglobal.h \
  /usr/include/qt6/QtCore/qtdeprecationdefinitions.h \
  /usr/include/qt6/QtCore/qtdeprecationmarkers.h \
  /usr/include/qt6/QtCore/qtenvironmentvariables.h \
  /usr/include/qt6/QtCore/qtextstream.h \
  /usr/include/qt6/QtCore/qtformat_impl.h \
  /usr/include/qt6/QtCore/qtimezone.h \
  /usr/include/qt6/QtCore/qtmetamacros.h \
  /usr/include/qt6/QtCore/qtnoop.h \
  /usr/include/qt6/QtCore/qtpreprocessorsupport.h \
  /usr/include/qt6/QtCore/qtresource.h \
  /usr/include/qt6/QtCore/qttranslation.h \
  /usr/include/qt6/QtCore/qttypetraits.h \
  /usr/include/qt6/QtCore/qtversion.h \
  /usr/include/qt6/QtCore/qtversionchecks.h \
  /usr/include/qt6/QtCore/qtypeinfo.h \
  /usr/include/qt6/QtCore/qtypes.h \
  /usr/include/qt6/QtCore/qurl.h \
  /usr/include/qt6/QtCore/qutf8stringview.h \
  /usr/include/qt6/QtCore/qvariant.h \
  /usr/include/qt6/QtCore/qvarlengtharray.h \
  /usr/include/qt6/QtCore/qversiontagging.h \
  /usr/include/qt6/QtCore/qxptype_traits.h \
  /usr/include/qt6/QtCore/qyieldcpu.h \
  /usr/include/qt6/QtGui/qaction.h \
  /usr/include/qt6/QtGui/qbitmap.h \
  /usr/include/qt6/QtGui/qbrush.h \
  /usr/include/qt6/QtGui/qcolor.h \
  /usr/include/qt6/QtGui/qcursor.h \
  /usr/include/qt6/QtGui/qfont.h \
  /usr/include/qt6/QtGui/qfontdatabase.h \
  /usr/include/qt6/QtGui/qfontinfo.h \
  /usr/include/qt6/QtGui/qfontmetrics.h \
  /usr/include/qt6/QtGui/qfontvariableaxis.h \
  /usr/include/qt6/QtGui/qicon.h \
  /usr/include/qt6/QtGui/qimage.h \
  /usr/include/qt6/QtGui/qkeysequence.h \
  /usr/include/qt6/QtGui/qpaintdevice.h \
  /usr/include/qt6/QtGui/qpalette.h \
  /usr/include/qt6/QtGui/qpen.h \
  /usr/include/qt6/QtGui/qpicture.h \
  /usr/include/qt6/QtGui/qpixelformat.h \
  /usr/include/qt6/QtGui/qpixmap.h \
  /usr/include/qt6/QtGui/qpolygon.h \
  /usr/include/qt6/QtGui/qregion.h \
  /usr/include/qt6/QtGui/qrgb.h \
  /usr/include/qt6/QtGui/qrgba64.h \
  /usr/include/qt6/QtGui/qtextcursor.h \
  /usr/include/qt6/QtGui/qtextdocument.h \
  /usr/include/qt6/QtGui/qtextformat.h \
  /usr/include/qt6/QtGui/qtextoption.h \
  /usr/include/qt6/QtGui/qtgui-config.h \
  /usr/include/qt6/QtGui/qtguiexports.h \
  /usr/include/qt6/QtGui/qtguiglobal.h \
  /usr/include/qt6/QtGui/qtransform.h \
  /usr/include/qt6/QtGui/qvalidator.h \
  /usr/include/qt6/QtGui/qwindowdefs.h \
  /usr/include/qt6/QtWidgets/QCheckBox \
  /usr/include/qt6/QtWidgets/QComboBox \
  /usr/include/qt6/QtWidgets/QDialog \
  /usr/include/qt6/QtWidgets/QDialogButtonBox \
  /usr/include/qt6/QtWidgets/QFileDialog \
  /usr/include/qt6/QtWidgets/QFontComboBox \
  /usr/include/qt6/QtWidgets/QGroupBox \
  /usr/include/qt6/QtWidgets/QHBoxLayout \
  /usr/include/qt6/QtWidgets/QLabel \
  /usr/include/qt6/QtWidgets/QLineEdit \
  /usr/include/qt6/QtWidgets/QPushButton \
  /usr/include/qt6/QtWidgets/QSpinBox \
  /usr/include/qt6/QtWidgets/QTabWidget \
  /usr/include/qt6/QtWidgets/QVBoxLayout \
  /usr/include/qt6/QtWidgets/qabstractbutton.h \
  /usr/include/qt6/QtWidgets/qabstractitemdelegate.h \
  /usr/include/qt6/QtWidgets/qabstractslider.h \
  /usr/include/qt6/QtWidgets/qabstractspinbox.h \
  /usr/include/qt6/QtWidgets/qboxlayout.h \
  /usr/include/qt6/QtWidgets/qcheckbox.h \
  /usr/include/qt6/QtWidgets/qcombobox.h \
  /usr/include/qt6/QtWidgets/qdialog.h \
  /usr/include/qt6/QtWidgets/qdialogbuttonbox.h \
  /usr/include/qt6/QtWidgets/qfiledialog.h \
  /usr/include/qt6/QtWidgets/qfontcombobox.h \
  /usr/include/qt6/QtWidgets/qframe.h \
  /usr/include/qt6/QtWidgets/qgridlayout.h \
  /usr/include/qt6/QtWidgets/qgroupbox.h \
  /usr/include/qt6/QtWidgets/qlabel.h \
  /usr/include/qt6/QtWidgets/qlayout.h \
  /usr/include/qt6/QtWidgets/qlayoutitem.h \
  /usr/include/qt6/QtWidgets/qlineedit.h \
  /usr/include/qt6/QtWidgets/qpushbutton.h \
  /usr/include/qt6/QtWidgets/qrubberband.h \
  /usr/include/qt6/QtWidgets/qsizepolicy.h \
  /usr/include/qt6/QtWidgets/qslider.h \
  /usr/include/qt6/QtWidgets/qspinbox.h \
  /usr/include/qt6/QtWidgets/qstyle.h \
  /usr/include/qt6/QtWidgets/qstyleoption.h \
  /usr/include/qt6/QtWidgets/qtabbar.h \
  /usr/include/qt6/QtWidgets/qtabwidget.h \
  /usr/include/qt6/QtWidgets/qtwidgets-config.h \
  /usr/include/qt6/QtWidgets/qtwidgetsexports.h \
  /usr/include/qt6/QtWidgets/qtwidgetsglobal.h \
  /usr/include/qt6/QtWidgets/qwidget.h \
  /usr/include/sched.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/select.h \
  /usr/include/sys/single_threaded.h \
  /usr/include/sys/syscall.h \
  /usr/include/sys/types.h \
  /usr/include/syscall.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/include/wchar.h \
  /usr/include/wctype.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/limits.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stdarg.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stdbool.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stddef.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stdint.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/syslimits.h

CMakeFiles/KNoteDo.dir/main.cpp.o: /home/<USER>/CLionProjects/KNoteDo/main.cpp \
  /home/<USER>/CLionProjects/KNoteDo/app/MainWindow.h \
  /usr/include/alloca.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/int-ll64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/asm-generic/types.h \
  /usr/include/asm/bitsperlong.h \
  /usr/include/asm/errno.h \
  /usr/include/asm/posix_types.h \
  /usr/include/asm/posix_types_64.h \
  /usr/include/asm/types.h \
  /usr/include/asm/unistd.h \
  /usr/include/asm/unistd_64.h \
  /usr/include/assert.h \
  /usr/include/bits/atomic_wide_counter.h \
  /usr/include/bits/byteswap.h \
  /usr/include/bits/confname.h \
  /usr/include/bits/cpu-set.h \
  /usr/include/bits/endian.h \
  /usr/include/bits/endianness.h \
  /usr/include/bits/environments.h \
  /usr/include/bits/errno.h \
  /usr/include/bits/floatn-common.h \
  /usr/include/bits/floatn.h \
  /usr/include/bits/flt-eval-method.h \
  /usr/include/bits/fp-fast.h \
  /usr/include/bits/fp-logb.h \
  /usr/include/bits/getopt_core.h \
  /usr/include/bits/getopt_posix.h \
  /usr/include/bits/iscanonical.h \
  /usr/include/bits/libc-header-start.h \
  /usr/include/bits/libm-simd-decl-stubs.h \
  /usr/include/bits/local_lim.h \
  /usr/include/bits/locale.h \
  /usr/include/bits/long-double.h \
  /usr/include/bits/math-vector.h \
  /usr/include/bits/mathcalls-helper-functions.h \
  /usr/include/bits/mathcalls-macros.h \
  /usr/include/bits/mathcalls-narrow.h \
  /usr/include/bits/mathcalls.h \
  /usr/include/bits/posix1_lim.h \
  /usr/include/bits/posix2_lim.h \
  /usr/include/bits/posix_opt.h \
  /usr/include/bits/pthread_stack_min-dynamic.h \
  /usr/include/bits/pthreadtypes-arch.h \
  /usr/include/bits/pthreadtypes.h \
  /usr/include/bits/sched.h \
  /usr/include/bits/select.h \
  /usr/include/bits/setjmp.h \
  /usr/include/bits/stdint-intn.h \
  /usr/include/bits/stdint-least.h \
  /usr/include/bits/stdint-uintn.h \
  /usr/include/bits/stdio_lim.h \
  /usr/include/bits/stdlib-float.h \
  /usr/include/bits/struct_mutex.h \
  /usr/include/bits/struct_rwlock.h \
  /usr/include/bits/syscall.h \
  /usr/include/bits/thread-shared-types.h \
  /usr/include/bits/time.h \
  /usr/include/bits/time64.h \
  /usr/include/bits/timesize.h \
  /usr/include/bits/timex.h \
  /usr/include/bits/types.h \
  /usr/include/bits/types/FILE.h \
  /usr/include/bits/types/__FILE.h \
  /usr/include/bits/types/__fpos64_t.h \
  /usr/include/bits/types/__fpos_t.h \
  /usr/include/bits/types/__locale_t.h \
  /usr/include/bits/types/__mbstate_t.h \
  /usr/include/bits/types/__sigset_t.h \
  /usr/include/bits/types/clock_t.h \
  /usr/include/bits/types/clockid_t.h \
  /usr/include/bits/types/cookie_io_functions_t.h \
  /usr/include/bits/types/error_t.h \
  /usr/include/bits/types/locale_t.h \
  /usr/include/bits/types/mbstate_t.h \
  /usr/include/bits/types/sigset_t.h \
  /usr/include/bits/types/struct_FILE.h \
  /usr/include/bits/types/struct___jmp_buf_tag.h \
  /usr/include/bits/types/struct_itimerspec.h \
  /usr/include/bits/types/struct_sched_param.h \
  /usr/include/bits/types/struct_timespec.h \
  /usr/include/bits/types/struct_timeval.h \
  /usr/include/bits/types/struct_tm.h \
  /usr/include/bits/types/time_t.h \
  /usr/include/bits/types/timer_t.h \
  /usr/include/bits/types/wint_t.h \
  /usr/include/bits/typesizes.h \
  /usr/include/bits/uintn-identity.h \
  /usr/include/bits/uio_lim.h \
  /usr/include/bits/unistd_ext.h \
  /usr/include/bits/waitflags.h \
  /usr/include/bits/waitstatus.h \
  /usr/include/bits/wchar.h \
  /usr/include/bits/wctype-wchar.h \
  /usr/include/bits/wordsize.h \
  /usr/include/bits/xopen_lim.h \
  /usr/include/c++/15/algorithm \
  /usr/include/c++/15/array \
  /usr/include/c++/15/atomic \
  /usr/include/c++/15/backward/auto_ptr.h \
  /usr/include/c++/15/backward/binders.h \
  /usr/include/c++/15/bit \
  /usr/include/c++/15/bits/algorithmfwd.h \
  /usr/include/c++/15/bits/align.h \
  /usr/include/c++/15/bits/alloc_traits.h \
  /usr/include/c++/15/bits/allocated_ptr.h \
  /usr/include/c++/15/bits/allocator.h \
  /usr/include/c++/15/bits/atomic_base.h \
  /usr/include/c++/15/bits/atomic_lockfree_defines.h \
  /usr/include/c++/15/bits/atomic_wait.h \
  /usr/include/c++/15/bits/basic_ios.h \
  /usr/include/c++/15/bits/basic_ios.tcc \
  /usr/include/c++/15/bits/basic_string.h \
  /usr/include/c++/15/bits/basic_string.tcc \
  /usr/include/c++/15/bits/char_traits.h \
  /usr/include/c++/15/bits/charconv.h \
  /usr/include/c++/15/bits/chrono.h \
  /usr/include/c++/15/bits/chrono_io.h \
  /usr/include/c++/15/bits/codecvt.h \
  /usr/include/c++/15/bits/concept_check.h \
  /usr/include/c++/15/bits/cpp_type_traits.h \
  /usr/include/c++/15/bits/cxxabi_forced.h \
  /usr/include/c++/15/bits/cxxabi_init_exception.h \
  /usr/include/c++/15/bits/enable_special_members.h \
  /usr/include/c++/15/bits/erase_if.h \
  /usr/include/c++/15/bits/exception.h \
  /usr/include/c++/15/bits/exception_defines.h \
  /usr/include/c++/15/bits/exception_ptr.h \
  /usr/include/c++/15/bits/formatfwd.h \
  /usr/include/c++/15/bits/functexcept.h \
  /usr/include/c++/15/bits/functional_hash.h \
  /usr/include/c++/15/bits/hash_bytes.h \
  /usr/include/c++/15/bits/hashtable.h \
  /usr/include/c++/15/bits/hashtable_policy.h \
  /usr/include/c++/15/bits/invoke.h \
  /usr/include/c++/15/bits/ios_base.h \
  /usr/include/c++/15/bits/istream.tcc \
  /usr/include/c++/15/bits/iterator_concepts.h \
  /usr/include/c++/15/bits/list.tcc \
  /usr/include/c++/15/bits/locale_classes.h \
  /usr/include/c++/15/bits/locale_classes.tcc \
  /usr/include/c++/15/bits/locale_conv.h \
  /usr/include/c++/15/bits/locale_facets.h \
  /usr/include/c++/15/bits/locale_facets.tcc \
  /usr/include/c++/15/bits/locale_facets_nonio.h \
  /usr/include/c++/15/bits/locale_facets_nonio.tcc \
  /usr/include/c++/15/bits/localefwd.h \
  /usr/include/c++/15/bits/max_size_type.h \
  /usr/include/c++/15/bits/memory_resource.h \
  /usr/include/c++/15/bits/memoryfwd.h \
  /usr/include/c++/15/bits/monostate.h \
  /usr/include/c++/15/bits/move.h \
  /usr/include/c++/15/bits/nested_exception.h \
  /usr/include/c++/15/bits/new_allocator.h \
  /usr/include/c++/15/bits/node_handle.h \
  /usr/include/c++/15/bits/ostream.h \
  /usr/include/c++/15/bits/ostream.tcc \
  /usr/include/c++/15/bits/ostream_insert.h \
  /usr/include/c++/15/bits/parse_numbers.h \
  /usr/include/c++/15/bits/postypes.h \
  /usr/include/c++/15/bits/predefined_ops.h \
  /usr/include/c++/15/bits/ptr_traits.h \
  /usr/include/c++/15/bits/quoted_string.h \
  /usr/include/c++/15/bits/range_access.h \
  /usr/include/c++/15/bits/ranges_algo.h \
  /usr/include/c++/15/bits/ranges_algobase.h \
  /usr/include/c++/15/bits/ranges_base.h \
  /usr/include/c++/15/bits/ranges_cmp.h \
  /usr/include/c++/15/bits/ranges_uninitialized.h \
  /usr/include/c++/15/bits/ranges_util.h \
  /usr/include/c++/15/bits/refwrap.h \
  /usr/include/c++/15/bits/requires_hosted.h \
  /usr/include/c++/15/bits/shared_ptr.h \
  /usr/include/c++/15/bits/shared_ptr_atomic.h \
  /usr/include/c++/15/bits/shared_ptr_base.h \
  /usr/include/c++/15/bits/specfun.h \
  /usr/include/c++/15/bits/sstream.tcc \
  /usr/include/c++/15/bits/std_abs.h \
  /usr/include/c++/15/bits/std_function.h \
  /usr/include/c++/15/bits/std_mutex.h \
  /usr/include/c++/15/bits/stl_algo.h \
  /usr/include/c++/15/bits/stl_algobase.h \
  /usr/include/c++/15/bits/stl_bvector.h \
  /usr/include/c++/15/bits/stl_construct.h \
  /usr/include/c++/15/bits/stl_function.h \
  /usr/include/c++/15/bits/stl_heap.h \
  /usr/include/c++/15/bits/stl_iterator.h \
  /usr/include/c++/15/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/15/bits/stl_iterator_base_types.h \
  /usr/include/c++/15/bits/stl_list.h \
  /usr/include/c++/15/bits/stl_map.h \
  /usr/include/c++/15/bits/stl_multimap.h \
  /usr/include/c++/15/bits/stl_multiset.h \
  /usr/include/c++/15/bits/stl_numeric.h \
  /usr/include/c++/15/bits/stl_pair.h \
  /usr/include/c++/15/bits/stl_raw_storage_iter.h \
  /usr/include/c++/15/bits/stl_relops.h \
  /usr/include/c++/15/bits/stl_set.h \
  /usr/include/c++/15/bits/stl_tempbuf.h \
  /usr/include/c++/15/bits/stl_tree.h \
  /usr/include/c++/15/bits/stl_uninitialized.h \
  /usr/include/c++/15/bits/stl_vector.h \
  /usr/include/c++/15/bits/stream_iterator.h \
  /usr/include/c++/15/bits/streambuf.tcc \
  /usr/include/c++/15/bits/streambuf_iterator.h \
  /usr/include/c++/15/bits/string_view.tcc \
  /usr/include/c++/15/bits/stringfwd.h \
  /usr/include/c++/15/bits/unicode-data.h \
  /usr/include/c++/15/bits/unicode.h \
  /usr/include/c++/15/bits/uniform_int_dist.h \
  /usr/include/c++/15/bits/unique_ptr.h \
  /usr/include/c++/15/bits/unordered_map.h \
  /usr/include/c++/15/bits/unordered_set.h \
  /usr/include/c++/15/bits/uses_allocator.h \
  /usr/include/c++/15/bits/uses_allocator_args.h \
  /usr/include/c++/15/bits/utility.h \
  /usr/include/c++/15/bits/vector.tcc \
  /usr/include/c++/15/bits/version.h \
  /usr/include/c++/15/cassert \
  /usr/include/c++/15/cctype \
  /usr/include/c++/15/cerrno \
  /usr/include/c++/15/charconv \
  /usr/include/c++/15/chrono \
  /usr/include/c++/15/climits \
  /usr/include/c++/15/clocale \
  /usr/include/c++/15/cmath \
  /usr/include/c++/15/compare \
  /usr/include/c++/15/concepts \
  /usr/include/c++/15/cstddef \
  /usr/include/c++/15/cstdint \
  /usr/include/c++/15/cstdio \
  /usr/include/c++/15/cstdlib \
  /usr/include/c++/15/cstring \
  /usr/include/c++/15/ctime \
  /usr/include/c++/15/cwchar \
  /usr/include/c++/15/cwctype \
  /usr/include/c++/15/debug/assertions.h \
  /usr/include/c++/15/debug/debug.h \
  /usr/include/c++/15/exception \
  /usr/include/c++/15/ext/aligned_buffer.h \
  /usr/include/c++/15/ext/alloc_traits.h \
  /usr/include/c++/15/ext/atomicity.h \
  /usr/include/c++/15/ext/concurrence.h \
  /usr/include/c++/15/ext/numeric_traits.h \
  /usr/include/c++/15/ext/string_conversions.h \
  /usr/include/c++/15/ext/type_traits.h \
  /usr/include/c++/15/format \
  /usr/include/c++/15/functional \
  /usr/include/c++/15/initializer_list \
  /usr/include/c++/15/iomanip \
  /usr/include/c++/15/ios \
  /usr/include/c++/15/iosfwd \
  /usr/include/c++/15/istream \
  /usr/include/c++/15/iterator \
  /usr/include/c++/15/limits \
  /usr/include/c++/15/list \
  /usr/include/c++/15/locale \
  /usr/include/c++/15/map \
  /usr/include/c++/15/memory \
  /usr/include/c++/15/new \
  /usr/include/c++/15/numbers \
  /usr/include/c++/15/numeric \
  /usr/include/c++/15/optional \
  /usr/include/c++/15/ostream \
  /usr/include/c++/15/pstl/execution_defs.h \
  /usr/include/c++/15/pstl/glue_algorithm_defs.h \
  /usr/include/c++/15/pstl/glue_memory_defs.h \
  /usr/include/c++/15/pstl/glue_numeric_defs.h \
  /usr/include/c++/15/pstl/pstl_config.h \
  /usr/include/c++/15/ratio \
  /usr/include/c++/15/set \
  /usr/include/c++/15/span \
  /usr/include/c++/15/sstream \
  /usr/include/c++/15/stdexcept \
  /usr/include/c++/15/stdlib.h \
  /usr/include/c++/15/streambuf \
  /usr/include/c++/15/string \
  /usr/include/c++/15/string_view \
  /usr/include/c++/15/system_error \
  /usr/include/c++/15/tr1/bessel_function.tcc \
  /usr/include/c++/15/tr1/beta_function.tcc \
  /usr/include/c++/15/tr1/ell_integral.tcc \
  /usr/include/c++/15/tr1/exp_integral.tcc \
  /usr/include/c++/15/tr1/gamma.tcc \
  /usr/include/c++/15/tr1/hypergeometric.tcc \
  /usr/include/c++/15/tr1/legendre_function.tcc \
  /usr/include/c++/15/tr1/modified_bessel_func.tcc \
  /usr/include/c++/15/tr1/poly_hermite.tcc \
  /usr/include/c++/15/tr1/poly_laguerre.tcc \
  /usr/include/c++/15/tr1/riemann_zeta.tcc \
  /usr/include/c++/15/tr1/special_function_util.h \
  /usr/include/c++/15/tuple \
  /usr/include/c++/15/type_traits \
  /usr/include/c++/15/typeinfo \
  /usr/include/c++/15/unordered_map \
  /usr/include/c++/15/unordered_set \
  /usr/include/c++/15/utility \
  /usr/include/c++/15/variant \
  /usr/include/c++/15/vector \
  /usr/include/c++/15/version \
  /usr/include/c++/15/x86_64-redhat-linux/bits/atomic_word.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/c++allocator.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/c++config.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/c++locale.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/cpu_defines.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/ctype_base.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/ctype_inline.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/error_constants.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/gthr-default.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/gthr.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/messages_members.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/os_defines.h \
  /usr/include/c++/15/x86_64-redhat-linux/bits/time_members.h \
  /usr/include/ctype.h \
  /usr/include/endian.h \
  /usr/include/errno.h \
  /usr/include/features-time64.h \
  /usr/include/features.h \
  /usr/include/gnu/stubs-64.h \
  /usr/include/gnu/stubs.h \
  /usr/include/libintl.h \
  /usr/include/limits.h \
  /usr/include/linux/close_range.h \
  /usr/include/linux/errno.h \
  /usr/include/linux/limits.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/sched/types.h \
  /usr/include/linux/stddef.h \
  /usr/include/linux/types.h \
  /usr/include/locale.h \
  /usr/include/math.h \
  /usr/include/pthread.h \
  /usr/include/qt6/QtCore/QTimer \
  /usr/include/qt6/QtCore/q17memory.h \
  /usr/include/qt6/QtCore/q20functional.h \
  /usr/include/qt6/QtCore/q20iterator.h \
  /usr/include/qt6/QtCore/q20memory.h \
  /usr/include/qt6/QtCore/q20type_traits.h \
  /usr/include/qt6/QtCore/q20utility.h \
  /usr/include/qt6/QtCore/q23utility.h \
  /usr/include/qt6/QtCore/qabstracteventdispatcher.h \
  /usr/include/qt6/QtCore/qabstractitemmodel.h \
  /usr/include/qt6/QtCore/qalgorithms.h \
  /usr/include/qt6/QtCore/qanystringview.h \
  /usr/include/qt6/QtCore/qarraydata.h \
  /usr/include/qt6/QtCore/qarraydataops.h \
  /usr/include/qt6/QtCore/qarraydatapointer.h \
  /usr/include/qt6/QtCore/qassert.h \
  /usr/include/qt6/QtCore/qatomic.h \
  /usr/include/qt6/QtCore/qatomic_cxx11.h \
  /usr/include/qt6/QtCore/qbasicatomic.h \
  /usr/include/qt6/QtCore/qbasictimer.h \
  /usr/include/qt6/QtCore/qbindingstorage.h \
  /usr/include/qt6/QtCore/qbytearray.h \
  /usr/include/qt6/QtCore/qbytearrayalgorithms.h \
  /usr/include/qt6/QtCore/qbytearraylist.h \
  /usr/include/qt6/QtCore/qbytearrayview.h \
  /usr/include/qt6/QtCore/qchar.h \
  /usr/include/qt6/QtCore/qcompare.h \
  /usr/include/qt6/QtCore/qcompare_impl.h \
  /usr/include/qt6/QtCore/qcomparehelpers.h \
  /usr/include/qt6/QtCore/qcompilerdetection.h \
  /usr/include/qt6/QtCore/qconfig-64.h \
  /usr/include/qt6/QtCore/qconfig.h \
  /usr/include/qt6/QtCore/qconstructormacros.h \
  /usr/include/qt6/QtCore/qcontainerfwd.h \
  /usr/include/qt6/QtCore/qcontainerinfo.h \
  /usr/include/qt6/QtCore/qcontainertools_impl.h \
  /usr/include/qt6/QtCore/qcontiguouscache.h \
  /usr/include/qt6/QtCore/qcoreapplication.h \
  /usr/include/qt6/QtCore/qcoreapplication_platform.h \
  /usr/include/qt6/QtCore/qcoreevent.h \
  /usr/include/qt6/QtCore/qdarwinhelpers.h \
  /usr/include/qt6/QtCore/qdatastream.h \
  /usr/include/qt6/QtCore/qdeadlinetimer.h \
  /usr/include/qt6/QtCore/qdebug.h \
  /usr/include/qt6/QtCore/qelapsedtimer.h \
  /usr/include/qt6/QtCore/qendian.h \
  /usr/include/qt6/QtCore/qeventloop.h \
  /usr/include/qt6/QtCore/qexceptionhandling.h \
  /usr/include/qt6/QtCore/qflags.h \
  /usr/include/qt6/QtCore/qfloat16.h \
  /usr/include/qt6/QtCore/qforeach.h \
  /usr/include/qt6/QtCore/qfunctionaltools_impl.h \
  /usr/include/qt6/QtCore/qfunctionpointer.h \
  /usr/include/qt6/QtCore/qgenericatomic.h \
  /usr/include/qt6/QtCore/qglobal.h \
  /usr/include/qt6/QtCore/qglobalstatic.h \
  /usr/include/qt6/QtCore/qhash.h \
  /usr/include/qt6/QtCore/qhashfunctions.h \
  /usr/include/qt6/QtCore/qiodevicebase.h \
  /usr/include/qt6/QtCore/qitemselectionmodel.h \
  /usr/include/qt6/QtCore/qiterable.h \
  /usr/include/qt6/QtCore/qiterator.h \
  /usr/include/qt6/QtCore/qlatin1stringview.h \
  /usr/include/qt6/QtCore/qline.h \
  /usr/include/qt6/QtCore/qlist.h \
  /usr/include/qt6/QtCore/qlocale.h \
  /usr/include/qt6/QtCore/qlogging.h \
  /usr/include/qt6/QtCore/qmalloc.h \
  /usr/include/qt6/QtCore/qmap.h \
  /usr/include/qt6/QtCore/qmargins.h \
  /usr/include/qt6/QtCore/qmath.h \
  /usr/include/qt6/QtCore/qmetacontainer.h \
  /usr/include/qt6/QtCore/qmetatype.h \
  /usr/include/qt6/QtCore/qminmax.h \
  /usr/include/qt6/QtCore/qnamespace.h \
  /usr/include/qt6/QtCore/qnativeinterface.h \
  /usr/include/qt6/QtCore/qnumeric.h \
  /usr/include/qt6/QtCore/qobject.h \
  /usr/include/qt6/QtCore/qobject_impl.h \
  /usr/include/qt6/QtCore/qobjectdefs.h \
  /usr/include/qt6/QtCore/qobjectdefs_impl.h \
  /usr/include/qt6/QtCore/qoverload.h \
  /usr/include/qt6/QtCore/qpair.h \
  /usr/include/qt6/QtCore/qpoint.h \
  /usr/include/qt6/QtCore/qprocessordetection.h \
  /usr/include/qt6/QtCore/qrect.h \
  /usr/include/qt6/QtCore/qrefcount.h \
  /usr/include/qt6/QtCore/qregularexpression.h \
  /usr/include/qt6/QtCore/qscopedpointer.h \
  /usr/include/qt6/QtCore/qscopeguard.h \
  /usr/include/qt6/QtCore/qset.h \
  /usr/include/qt6/QtCore/qshareddata.h \
  /usr/include/qt6/QtCore/qshareddata_impl.h \
  /usr/include/qt6/QtCore/qsharedpointer.h \
  /usr/include/qt6/QtCore/qsharedpointer_impl.h \
  /usr/include/qt6/QtCore/qsize.h \
  /usr/include/qt6/QtCore/qspan.h \
  /usr/include/qt6/QtCore/qstdlibdetection.h \
  /usr/include/qt6/QtCore/qstring.h \
  /usr/include/qt6/QtCore/qstringalgorithms.h \
  /usr/include/qt6/QtCore/qstringbuilder.h \
  /usr/include/qt6/QtCore/qstringconverter.h \
  /usr/include/qt6/QtCore/qstringconverter_base.h \
  /usr/include/qt6/QtCore/qstringfwd.h \
  /usr/include/qt6/QtCore/qstringlist.h \
  /usr/include/qt6/QtCore/qstringliteral.h \
  /usr/include/qt6/QtCore/qstringmatcher.h \
  /usr/include/qt6/QtCore/qstringtokenizer.h \
  /usr/include/qt6/QtCore/qstringview.h \
  /usr/include/qt6/QtCore/qswap.h \
  /usr/include/qt6/QtCore/qsysinfo.h \
  /usr/include/qt6/QtCore/qsystemdetection.h \
  /usr/include/qt6/QtCore/qtaggedpointer.h \
  /usr/include/qt6/QtCore/qtclasshelpermacros.h \
  /usr/include/qt6/QtCore/qtconfiginclude.h \
  /usr/include/qt6/QtCore/qtconfigmacros.h \
  /usr/include/qt6/QtCore/qtcore-config.h \
  /usr/include/qt6/QtCore/qtcoreexports.h \
  /usr/include/qt6/QtCore/qtcoreglobal.h \
  /usr/include/qt6/QtCore/qtdeprecationdefinitions.h \
  /usr/include/qt6/QtCore/qtdeprecationmarkers.h \
  /usr/include/qt6/QtCore/qtenvironmentvariables.h \
  /usr/include/qt6/QtCore/qtextstream.h \
  /usr/include/qt6/QtCore/qtformat_impl.h \
  /usr/include/qt6/QtCore/qtimer.h \
  /usr/include/qt6/QtCore/qtmetamacros.h \
  /usr/include/qt6/QtCore/qtnoop.h \
  /usr/include/qt6/QtCore/qtpreprocessorsupport.h \
  /usr/include/qt6/QtCore/qtresource.h \
  /usr/include/qt6/QtCore/qttranslation.h \
  /usr/include/qt6/QtCore/qttypetraits.h \
  /usr/include/qt6/QtCore/qtversion.h \
  /usr/include/qt6/QtCore/qtversionchecks.h \
  /usr/include/qt6/QtCore/qtypeinfo.h \
  /usr/include/qt6/QtCore/qtypes.h \
  /usr/include/qt6/QtCore/qutf8stringview.h \
  /usr/include/qt6/QtCore/qvariant.h \
  /usr/include/qt6/QtCore/qvarlengtharray.h \
  /usr/include/qt6/QtCore/qversiontagging.h \
  /usr/include/qt6/QtCore/qxptype_traits.h \
  /usr/include/qt6/QtCore/qyieldcpu.h \
  /usr/include/qt6/QtGui/qaction.h \
  /usr/include/qt6/QtGui/qbitmap.h \
  /usr/include/qt6/QtGui/qbrush.h \
  /usr/include/qt6/QtGui/qcolor.h \
  /usr/include/qt6/QtGui/qcursor.h \
  /usr/include/qt6/QtGui/qfont.h \
  /usr/include/qt6/QtGui/qfontinfo.h \
  /usr/include/qt6/QtGui/qfontmetrics.h \
  /usr/include/qt6/QtGui/qfontvariableaxis.h \
  /usr/include/qt6/QtGui/qguiapplication.h \
  /usr/include/qt6/QtGui/qguiapplication_platform.h \
  /usr/include/qt6/QtGui/qicon.h \
  /usr/include/qt6/QtGui/qimage.h \
  /usr/include/qt6/QtGui/qinputmethod.h \
  /usr/include/qt6/QtGui/qkeysequence.h \
  /usr/include/qt6/QtGui/qpaintdevice.h \
  /usr/include/qt6/QtGui/qpalette.h \
  /usr/include/qt6/QtGui/qpixelformat.h \
  /usr/include/qt6/QtGui/qpixmap.h \
  /usr/include/qt6/QtGui/qpolygon.h \
  /usr/include/qt6/QtGui/qregion.h \
  /usr/include/qt6/QtGui/qrgb.h \
  /usr/include/qt6/QtGui/qrgba64.h \
  /usr/include/qt6/QtGui/qtgui-config.h \
  /usr/include/qt6/QtGui/qtguiexports.h \
  /usr/include/qt6/QtGui/qtguiglobal.h \
  /usr/include/qt6/QtGui/qtransform.h \
  /usr/include/qt6/QtGui/qvalidator.h \
  /usr/include/qt6/QtGui/qwindowdefs.h \
  /usr/include/qt6/QtWidgets/QApplication \
  /usr/include/qt6/QtWidgets/QListWidgetItem \
  /usr/include/qt6/QtWidgets/QMainWindow \
  /usr/include/qt6/QtWidgets/qabstractitemdelegate.h \
  /usr/include/qt6/QtWidgets/qabstractitemview.h \
  /usr/include/qt6/QtWidgets/qabstractscrollarea.h \
  /usr/include/qt6/QtWidgets/qabstractslider.h \
  /usr/include/qt6/QtWidgets/qabstractspinbox.h \
  /usr/include/qt6/QtWidgets/qapplication.h \
  /usr/include/qt6/QtWidgets/qframe.h \
  /usr/include/qt6/QtWidgets/qlistview.h \
  /usr/include/qt6/QtWidgets/qlistwidget.h \
  /usr/include/qt6/QtWidgets/qmainwindow.h \
  /usr/include/qt6/QtWidgets/qrubberband.h \
  /usr/include/qt6/QtWidgets/qsizepolicy.h \
  /usr/include/qt6/QtWidgets/qslider.h \
  /usr/include/qt6/QtWidgets/qstyle.h \
  /usr/include/qt6/QtWidgets/qstyleoption.h \
  /usr/include/qt6/QtWidgets/qtabbar.h \
  /usr/include/qt6/QtWidgets/qtabwidget.h \
  /usr/include/qt6/QtWidgets/qtwidgets-config.h \
  /usr/include/qt6/QtWidgets/qtwidgetsexports.h \
  /usr/include/qt6/QtWidgets/qtwidgetsglobal.h \
  /usr/include/qt6/QtWidgets/qwidget.h \
  /usr/include/sched.h \
  /usr/include/stdc-predef.h \
  /usr/include/stdint.h \
  /usr/include/stdio.h \
  /usr/include/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/sys/cdefs.h \
  /usr/include/sys/select.h \
  /usr/include/sys/single_threaded.h \
  /usr/include/sys/syscall.h \
  /usr/include/sys/types.h \
  /usr/include/syscall.h \
  /usr/include/time.h \
  /usr/include/unistd.h \
  /usr/include/wchar.h \
  /usr/include/wctype.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/limits.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stdarg.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stdbool.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stddef.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/stdint.h \
  /usr/lib/gcc/x86_64-redhat-linux/15/include/syslimits.h

KNoteDo: /lib64/ld-linux-x86-64.so.2 \
  /lib64/libc.so.6 \
  /lib64/libgcc_s.so.1 \
  /lib64/libm.so.6 \
  /lib64/libmvec.so.1 \
  /usr/lib64/crt1.o \
  /usr/lib64/crti.o \
  /usr/lib64/crtn.o \
  /usr/lib64/libc.so \
  /usr/lib64/libm.so \
  /usr/lib/gcc/x86_64-redhat-linux/15/crtbegin.o \
  /usr/lib/gcc/x86_64-redhat-linux/15/crtend.o \
  /usr/lib/gcc/x86_64-redhat-linux/15/libgcc.a \
  /usr/lib/gcc/x86_64-redhat-linux/15/libgcc_s.so \
  /usr/lib/gcc/x86_64-redhat-linux/15/libstdc++.so \
  /usr/lib64/libEGL.so.1 \
  /usr/lib64/libGLX.so \
  /usr/lib64/libGLdispatch.so.0 \
  /usr/lib64/libOpenGL.so \
  /usr/lib64/libQt6Core.so.6.9.1 \
  /usr/lib64/libQt6DBus.so.6 \
  /usr/lib64/libQt6Gui.so.6.9.1 \
  /usr/lib64/libQt6PrintSupport.so.6.9.1 \
  /usr/lib64/libQt6Widgets.so.6.9.1 \
  /usr/lib64/libX11.so.6 \
  /usr/lib64/libXau.so.6 \
  /usr/lib64/libXext.so.6 \
  /usr/lib64/libb2.so.1 \
  /usr/lib64/libbrotlicommon.so.1 \
  /usr/lib64/libbrotlidec.so.1 \
  /usr/lib64/libbz2.so.1 \
  /usr/lib64/libc_nonshared.a \
  /usr/lib64/libcap.so.2 \
  /usr/lib64/libcrypto.so.3 \
  /usr/lib64/libdbus-1.so.3 \
  /usr/lib64/libdouble-conversion.so.3 \
  /usr/lib64/libfontconfig.so.1 \
  /usr/lib64/libfreetype.so.6 \
  /usr/lib64/libglib-2.0.so.0 \
  /usr/lib64/libgomp.so.1 \
  /usr/lib64/libgraphite2.so.3 \
  /usr/lib64/libharfbuzz.so.0 \
  /usr/lib64/libicudata.so.76 \
  /usr/lib64/libicui18n.so.76 \
  /usr/lib64/libicuuc.so.76 \
  /usr/lib64/liblzma.so.5 \
  /usr/lib64/libpcre2-16.so.0 \
  /usr/lib64/libpcre2-8.so.0 \
  /usr/lib64/libpng16.so.16 \
  /usr/lib64/libsystemd.so.0 \
  /usr/lib64/libxcb.so.1 \
  /usr/lib64/libxkbcommon.so.0 \
  /usr/lib64/libxml2.so.2 \
  /usr/lib64/libz.so.1 \
  /usr/lib64/libzstd.so.1 \
  CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.o \
  CMakeFiles/KNoteDo.dir/app/AppleNotesTheme.cpp.o \
  CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.o \
  CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.o \
  CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.o \
  CMakeFiles/KNoteDo.dir/main.cpp.o


CMakeFiles/KNoteDo.dir/main.cpp.o:

CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.o:

/usr/lib64/libzstd.so.1:

/usr/lib64/libxml2.so.2:

/usr/lib64/libpng16.so.16:

/usr/lib64/libpcre2-8.so.0:

/usr/lib64/liblzma.so.5:

/usr/lib64/libicuuc.so.76:

/usr/lib64/libicui18n.so.76:

/usr/lib64/libgraphite2.so.3:

/usr/lib64/libglib-2.0.so.0:

/usr/lib64/libdbus-1.so.3:

/usr/lib64/libcap.so.2:

/usr/lib64/libbz2.so.1:

/usr/lib64/libbrotlidec.so.1:

/usr/lib64/libxcb.so.1:

/usr/lib64/libb2.so.1:

/usr/lib64/libX11.so.6:

/usr/lib64/libQt6Widgets.so.6.9.1:

/usr/lib64/libQt6DBus.so.6:

/usr/lib64/libGLdispatch.so.0:

/usr/lib64/libGLX.so:

/usr/lib64/libEGL.so.1:

/usr/lib/gcc/x86_64-redhat-linux/15/libstdc++.so:

/usr/lib/gcc/x86_64-redhat-linux/15/libgcc.a:

/usr/lib64/libm.so:

/usr/lib64/crtn.o:

/home/<USER>/CLionProjects/KNoteDo/main.cpp:

/usr/include/qt6/QtWidgets/qgroupbox.h:

/usr/include/qt6/QtWidgets/qcheckbox.h:

/usr/include/qt6/QtWidgets/QTabWidget:

/usr/include/qt6/QtWidgets/QGroupBox:

/usr/include/qt6/QtWidgets/QComboBox:

/usr/lib64/libQt6PrintSupport.so.6.9.1:

/usr/include/qt6/QtWidgets/QCheckBox:

/home/<USER>/CLionProjects/KNoteDo/app/SettingsDialog.cpp:

/usr/include/qt6/QtWidgets/qspinbox.h:

/usr/include/qt6/QtWidgets/qfontcombobox.h:

/usr/include/qt6/QtWidgets/qcombobox.h:

/usr/include/qt6/QtWidgets/QSpinBox:

/usr/include/qt6/QtWidgets/qcolordialog.h:

/usr/include/qt6/QtGui/qtextlist.h:

/usr/include/qt6/QtGui/qscreen.h:

/usr/include/qt6/QtGui/qrawfont.h:

/usr/include/qt6/QtGui/qpointingdevice.h:

/usr/include/qt6/QtGui/qglyphrun.h:

/usr/include/qt6/QtGui/qfontdatabase.h:

/usr/include/qt6/QtGui/QTextTable:

/usr/lib64/libz.so.1:

/usr/include/qt6/QtGui/QTextImageFormat:

/usr/include/qt6/QtGui/QKeyEvent:

/usr/include/qt6/QtCore/qtemporarydir.h:

/usr/lib64/libXext.so.6:

/usr/include/qt6/QtCore/QSizeF:

/usr/include/qt6/QtCore/QRect:

/usr/include/qt6/QtCore/QObject:

/usr/include/qt6/QtCore/QMimeData:

/usr/include/qt6/QtCore/QList:

/home/<USER>/CLionProjects/KNoteDo/app/RichTextEditor.cpp:

/usr/include/qt6/QtWidgets/qtreewidgetitemiterator.h:

/usr/include/qt6/QtWidgets/qtreewidget.h:

/usr/lib64/libxkbcommon.so.0:

/usr/include/qt6/QtWidgets/qstylefactory.h:

/usr/lib/gcc/x86_64-redhat-linux/15/libgcc_s.so:

/usr/include/qt6/QtWidgets/qstackedwidget.h:

/usr/include/qt6/QtWidgets/qsplitter.h:

/usr/include/qt6/QtWidgets/qpushbutton.h:

/usr/include/qt6/QtWidgets/qmenu.h:

/usr/include/qt6/QtGui/qtextobject.h:

/usr/include/qt6/QtWidgets/qlayoutitem.h:

/usr/include/qt6/QtWidgets/qlayout.h:

/usr/include/qt6/QtWidgets/qlabel.h:

/usr/include/qt6/QtWidgets/qheaderview.h:

CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.o:

/usr/include/qt6/QtWidgets/qgridlayout.h:

/usr/include/qt6/QtWidgets/qfiledialog.h:

/usr/include/qt6/QtWidgets/qdialogbuttonbox.h:

/usr/include/qt6/QtWidgets/qboxlayout.h:

/usr/include/qt6/QtWidgets/QTreeWidgetItem:

/usr/include/qt6/QtWidgets/QStyle:

/usr/include/qt6/QtWidgets/QMessageBox:

/usr/include/qt6/QtWidgets/QMenuBar:

/usr/include/qt6/QtWidgets/QMenu:

/usr/include/qt6/QtWidgets/QListWidget:

/usr/include/qt6/QtWidgets/QInputDialog:

/usr/include/qt6/QtWidgets/QFrame:

/usr/include/qt6/QtWidgets/QFileDialog:

/lib64/libgcc_s.so.1:

/usr/include/qt6/QtGui/qstylehints.h:

/usr/include/qt6/QtGui/qshortcut.h:

/usr/include/qt6/QtGui/qdesktopservices.h:

/usr/lib64/libicudata.so.76:

/usr/include/qt6/QtGui/QTextCursor:

/usr/include/qt6/QtGui/QKeySequence:

/usr/include/qt6/QtGui/QIcon:

/usr/include/qt6/QtGui/QFont:

/usr/include/qt6/QtCore/quuid.h:

/usr/include/qt6/QtCore/qstandardpaths.h:

/usr/include/qt6/QtCore/qjsondocument.h:

/usr/include/qt6/QtCore/qiodevice.h:

/usr/include/qt6/QtCore/qfileinfo.h:

/usr/include/qt6/QtCore/qfile.h:

/usr/include/qt6/QtCore/qdirlisting.h:

/usr/include/qt6/QtCore/qdatetime.h:

/usr/include/qt6/QtCore/QUrl:

/usr/include/qt6/QtCore/QTextStream:

/usr/include/qt6/QtCore/QStandardPaths:

/usr/include/qt6/QtCore/QJsonObject:

/usr/include/qt6/QtCore/QJsonDocument:

/usr/include/qt6/QtCore/QJsonArray:

/usr/include/qt6/QtCore/QDateTime:

/usr/include/c++/15/bits/fs_ops.h:

/usr/include/c++/15/bits/fs_fwd.h:

/home/<USER>/CLionProjects/KNoteDo/app/MainWindow.cpp:

/usr/lib64/libdouble-conversion.so.3:

/usr/include/qt6/QtWidgets/QApplication:

/usr/include/qt6/QtGui/qinputmethod.h:

/usr/include/qt6/QtGui/qguiapplication_platform.h:

/usr/include/qt6/QtGui/qguiapplication.h:

/usr/include/qt6/QtCore/qnativeinterface.h:

/usr/include/qt6/QtCore/qcoreapplication_platform.h:

/usr/include/qt6/QtCore/QString:

/home/<USER>/CLionProjects/KNoteDo/app/AppleNotesTheme.h:

/usr/lib/gcc/x86_64-redhat-linux/15/include/syslimits.h:

/usr/lib/gcc/x86_64-redhat-linux/15/include/stdint.h:

/usr/lib/gcc/x86_64-redhat-linux/15/include/stdarg.h:

/usr/include/wctype.h:

/usr/include/wchar.h:

/usr/include/c++/15/bits/fs_path.h:

/usr/include/sys/types.h:

/usr/include/sys/syscall.h:

/usr/include/sys/select.h:

/usr/include/sys/cdefs.h:

/usr/include/strings.h:

/usr/include/stdio.h:

/usr/include/stdint.h:

/usr/include/sched.h:

/usr/include/qt6/QtWidgets/qwidget.h:

/usr/include/c++/15/functional:

/usr/include/qt6/QtWidgets/qabstractscrollarea.h:

/usr/include/c++/15/bits/exception_ptr.h:

/usr/include/c++/15/ext/alloc_traits.h:

/usr/include/qt6/QtCore/qdarwinhelpers.h:

/usr/include/c++/15/ext/aligned_buffer.h:

/usr/include/qt6/QtCore/QSize:

/usr/include/bits/getopt_core.h:

/usr/include/qt6/QtGui/qtguiexports.h:

/usr/lib64/libpcre2-16.so.0:

/usr/include/qt6/QtWidgets/QColorDialog:

/usr/include/c++/15/ctime:

/usr/include/c++/15/cmath:

/usr/include/qt6/QtCore/qline.h:

/usr/include/qt6/QtGui/qtextlayout.h:

/usr/include/qt6/QtGui/qevent.h:

/usr/include/bits/floatn-common.h:

/usr/include/qt6/QtWidgets/qtabwidget.h:

/usr/include/c++/15/bits/uniform_int_dist.h:

/usr/include/c++/15/cwctype:

/usr/include/c++/15/bits/functexcept.h:

/usr/include/c++/15/x86_64-redhat-linux/bits/os_defines.h:

/usr/include/c++/15/cstring:

/usr/include/string.h:

/usr/include/c++/15/bits/stl_tree.h:

/usr/include/qt6/QtWidgets/QStyleFactory:

/usr/include/bits/types/struct___jmp_buf_tag.h:

/usr/include/bits/wchar.h:

/usr/include/bits/waitstatus.h:

/usr/include/c++/15/bits/stl_tempbuf.h:

/usr/include/c++/15/bits/stl_set.h:

/usr/include/qt6/QtGui/qcolor.h:

/usr/include/c++/15/bits/stl_multiset.h:

/usr/include/c++/15/bits/unicode.h:

/usr/include/qt6/QtGui/qbrush.h:

/usr/include/qt6/QtCore/QTemporaryDir:

/usr/include/qt6/QtWidgets/QWidget:

/usr/include/c++/15/bits/shared_ptr_base.h:

/usr/include/c++/15/bits/stl_list.h:

/usr/include/c++/15/bits/stl_iterator_base_types.h:

/usr/lib64/libsystemd.so.0:

/usr/include/bits/types/struct_tm.h:

/usr/include/c++/15/bits/ptr_traits.h:

/usr/lib64/libQt6Gui.so.6.9.1:

/usr/include/qt6/QtCore/qeventloop.h:

/usr/include/c++/15/bits/stl_bvector.h:

/usr/include/c++/15/cstdint:

/usr/include/c++/15/bits/stl_pair.h:

/usr/include/qt6/QtCore/q20algorithm.h:

/usr/include/c++/15/clocale:

/usr/include/c++/15/bits/stl_algobase.h:

/usr/include/c++/15/bits/stl_relops.h:

/usr/include/qt6/QtCore/qsettings.h:

/usr/include/c++/15/bits/cxxabi_forced.h:

/usr/include/c++/15/concepts:

/usr/include/qt6/QtWidgets/QLineEdit:

/usr/include/c++/15/bits/std_function.h:

/usr/include/c++/15/bits/codecvt.h:

/usr/include/c++/15/bits/stringfwd.h:

/usr/include/qt6/QtGui/qpaintdevice.h:

CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.o:

/usr/include/bits/types/struct_FILE.h:

/usr/include/c++/15/bits/sstream.tcc:

/usr/include/bits/environments.h:

/usr/include/c++/15/bits/ranges_uninitialized.h:

/usr/include/c++/15/bits/ranges_algobase.h:

/usr/include/asm/unistd.h:

/usr/include/qt6/QtWidgets/qtoolbar.h:

/usr/include/c++/15/bits/quoted_string.h:

/usr/include/qt6/QtGui/QTransform:

/usr/include/c++/15/bits/postypes.h:

/usr/include/qt6/QtGui/QTextCharFormat:

/usr/include/bits/local_lim.h:

/usr/include/qt6/QtCore/qtcoreexports.h:

/usr/include/qt6/QtWidgets/qrubberband.h:

/usr/include/c++/15/bits/parse_numbers.h:

/usr/include/qt6/QtWidgets/QMainWindow:

/usr/include/c++/15/bits/ostream.tcc:

/usr/include/c++/15/bits/ranges_base.h:

/usr/include/qt6/QtWidgets/QTreeWidget:

/usr/include/asm/posix_types_64.h:

/usr/lib64/libfontconfig.so.1:

/usr/include/linux/sched/types.h:

/usr/lib64/libOpenGL.so:

/usr/include/c++/15/bits/max_size_type.h:

/usr/include/c++/15/bits/locale_facets_nonio.tcc:

/usr/include/qt6/QtCore/qabstractitemmodel.h:

/usr/include/c++/15/bits/unicode-data.h:

/usr/include/c++/15/bits/ranges_cmp.h:

/usr/include/c++/15/bits/locale_facets_nonio.h:

/usr/include/qt6/QtWidgets/qabstractbutton.h:

/usr/include/bits/types/wint_t.h:

/lib64/ld-linux-x86-64.so.2:

/usr/include/c++/15/bits/istream.tcc:

/usr/include/c++/15/bits/ios_base.h:

/usr/include/qt6/QtWidgets/qtextedit.h:

/usr/include/c++/15/bits/uses_allocator.h:

/usr/include/c++/15/bits/new_allocator.h:

/usr/include/bits/cpu-set.h:

/usr/include/qt6/QtGui/qinputdevice.h:

/usr/include/qt6/QtGui/QPalette:

/usr/include/c++/15/bits/hashtable_policy.h:

/lib64/libmvec.so.1:

/usr/include/c++/15/ext/string_conversions.h:

/usr/lib/gcc/x86_64-redhat-linux/15/include/stddef.h:

/usr/include/qt6/QtCore/qshareddata.h:

/usr/include/c++/15/bits/locale_classes.tcc:

/usr/include/c++/15/bits/hash_bytes.h:

/usr/include/c++/15/algorithm:

/usr/include/qt6/QtCore/qcontainertools_impl.h:

/usr/include/qt6/QtWidgets/QTextEdit:

/usr/include/qt6/QtWidgets/qinputdialog.h:

/usr/include/c++/15/ext/type_traits.h:

/usr/include/c++/15/bits/functional_hash.h:

/usr/include/qt6/QtCore/QFile:

/usr/include/c++/15/bits/iterator_concepts.h:

/usr/include/errno.h:

/usr/include/c++/15/bits/predefined_ops.h:

/usr/include/qt6/QtCore/qjsonarray.h:

/usr/include/c++/15/optional:

/usr/include/c++/15/bits/exception_defines.h:

/usr/lib64/libc_nonshared.a:

/usr/include/c++/15/bits/stl_heap.h:

/usr/include/bits/types/timer_t.h:

/usr/include/c++/15/backward/auto_ptr.h:

/usr/include/qt6/QtCore/qvariant.h:

/usr/include/c++/15/bits/exception.h:

/usr/include/bits/types/clockid_t.h:

/usr/include/c++/15/typeinfo:

/usr/include/bits/types/error_t.h:

/usr/include/c++/15/bits/ostream_insert.h:

/usr/include/qt6/QtCore/qstringalgorithms.h:

/usr/include/qt6/QtGui/qfontinfo.h:

/usr/include/qt6/QtGui/QDesktopServices:

/usr/include/qt6/QtGui/qtextdocument.h:

/usr/include/c++/15/climits:

/usr/include/c++/15/bits/vector.tcc:

/usr/include/qt6/QtCore/qcompare.h:

/usr/include/qt6/QtWidgets/QPushButton:

/usr/include/c++/15/debug/assertions.h:

/usr/include/bits/select.h:

/usr/include/qt6/QtWidgets/QLabel:

/usr/include/bits/types/__FILE.h:

/usr/include/asm/errno.h:

/usr/include/bits/types/cookie_io_functions_t.h:

/usr/lib64/libfreetype.so.6:

/usr/include/bits/pthreadtypes-arch.h:

/usr/include/qt6/QtWidgets/QFontComboBox:

/usr/include/bits/pthread_stack_min-dynamic.h:

/usr/include/bits/posix1_lim.h:

/usr/include/c++/15/sstream:

/usr/include/qt6/QtGui/qtexttable.h:

/usr/include/bits/fp-fast.h:

/usr/include/c++/15/bits/formatfwd.h:

/usr/include/bits/mathcalls-narrow.h:

/usr/lib/gcc/x86_64-redhat-linux/15/crtbegin.o:

/usr/include/qt6/QtGui/qkeysequence.h:

/usr/include/qt6/QtGui/qpixelformat.h:

/usr/include/qt6/QtCore/QDir:

/usr/include/qt6/QtCore/q20functional.h:

/usr/include/c++/15/bits/concept_check.h:

/usr/include/bits/uio_lim.h:

/usr/include/bits/math-vector.h:

/usr/lib64/libbrotlicommon.so.1:

/usr/include/c++/15/bits/align.h:

/usr/include/c++/15/cwchar:

/usr/include/qt6/QtGui/qaction.h:

/usr/include/bits/mathcalls-helper-functions.h:

/usr/include/qt6/QtCore/q20type_traits.h:

/usr/include/c++/15/bits/stl_algo.h:

/usr/include/bits/types/time_t.h:

/usr/include/qt6/QtCore/qtresource.h:

/usr/include/bits/libc-header-start.h:

/usr/include/c++/15/bits/atomic_wait.h:

/usr/include/bits/types.h:

/usr/include/qt6/QtCore/qtaggedpointer.h:

/usr/include/bits/floatn.h:

/usr/include/linux/stddef.h:

/usr/include/bits/fp-logb.h:

/usr/include/sys/single_threaded.h:

/usr/include/asm-generic/errno.h:

/usr/include/asm-generic/posix_types.h:

/usr/include/c++/15/bits/allocator.h:

/usr/include/bits/types/__sigset_t.h:

/usr/include/bits/pthreadtypes.h:

/usr/include/qt6/QtGui/qscreen_platform.h:

/usr/include/qt6/QtWidgets/QListWidgetItem:

/usr/include/qt6/QtWidgets/qapplication.h:

/usr/include/c++/15/bits/unique_ptr.h:

/usr/include/c++/15/bits/move.h:

/usr/include/c++/15/bits/ranges_algo.h:

/usr/include/qt6/QtGui/qtgui-config.h:

/usr/include/bits/stdint-least.h:

/usr/include/alloca.h:

/usr/include/asm/types.h:

/usr/include/c++/15/bits/node_handle.h:

/usr/lib64/libQt6Core.so.6.9.1:

/usr/include/bits/typesizes.h:

/usr/include/bits/uintn-identity.h:

/usr/include/qt6/QtCore/qlist.h:

/usr/include/qt6/QtCore/qstringfwd.h:

/usr/include/qt6/QtCore/qjsonvalue.h:

/usr/include/c++/15/bits/stl_function.h:

/usr/include/bits/types/struct_itimerspec.h:

/usr/include/c++/15/bits/memory_resource.h:

/usr/include/c++/15/set:

/usr/lib64/libgomp.so.1:

/usr/include/c++/15/x86_64-redhat-linux/bits/c++locale.h:

/usr/include/qt6/QtCore/qnamespace.h:

/usr/include/c++/15/bits/atomic_lockfree_defines.h:

/usr/include/bits/byteswap.h:

/usr/include/bits/setjmp.h:

/usr/include/c++/15/cctype:

/usr/include/c++/15/bits/char_traits.h:

/usr/include/c++/15/bits/uses_allocator_args.h:

/usr/include/c++/15/bits/chrono.h:

/usr/lib/gcc/x86_64-redhat-linux/15/crtend.o:

KNoteDo_autogen/VJIZ3MDCXP/moc_RichTextEditor.cpp:

/usr/include/c++/15/bits/localefwd.h:

/usr/include/qt6/QtCore/qhashfunctions.h:

/usr/include/qt6/QtWidgets/qabstractspinbox.h:

/usr/include/bits/types/struct_timespec.h:

/usr/include/qt6/QtCore/qiterable.h:

KNoteDo_autogen/VJIZ3MDCXP/moc_MainWindow.cpp:

/usr/include/qt6/QtCore/qanystringview.h:

/usr/include/asm-generic/errno-base.h:

/usr/include/c++/15/filesystem:

/usr/include/qt6/QtCore/qshareddata_impl.h:

/usr/include/bits/struct_rwlock.h:

/usr/include/bits/sched.h:

/usr/include/qt6/QtCore/qconfig.h:

/usr/include/asm-generic/types.h:

/usr/include/asm-generic/bitsperlong.h:

/usr/include/qt6/QtGui/QTextTableFormat:

/usr/include/bits/endian.h:

/usr/include/c++/15/codecvt:

/usr/include/c++/15/system_error:

/usr/include/c++/15/string_view:

/usr/include/c++/15/bits/unordered_map.h:

/usr/include/c++/15/bits/erase_if.h:

/usr/include/c++/15/charconv:

/usr/include/c++/15/bits/std_mutex.h:

/usr/include/bits/locale.h:

/usr/include/c++/15/streambuf:

/usr/include/c++/15/format:

/usr/include/c++/15/bits/fs_dir.h:

/usr/include/qt6/QtCore/qoverload.h:

/usr/include/c++/15/bits/stl_numeric.h:

/usr/include/qt6/QtCore/qdir.h:

/usr/include/bits/iscanonical.h:

/usr/include/qt6/QtCore/qdatastream.h:

/usr/include/bits/syscall.h:

/usr/include/c++/15/cstdio:

/usr/include/c++/15/ext/concurrence.h:

/usr/include/c++/15/ext/atomicity.h:

/usr/include/c++/15/bits/stl_iterator_base_funcs.h:

/usr/include/c++/15/bits/monostate.h:

/home/<USER>/CLionProjects/KNoteDo/app/MainWindow.h:

/usr/include/bits/posix_opt.h:

/usr/include/bits/atomic_wide_counter.h:

/usr/include/c++/15/cassert:

/usr/include/qt6/QtCore/qobject_impl.h:

/usr/include/qt6/QtCore/qstringconverter.h:

/usr/include/qt6/QtCore/qregularexpression.h:

/usr/include/c++/15/bits/streambuf.tcc:

/usr/include/qt6/QtGui/QAction:

/usr/lib/gcc/x86_64-redhat-linux/15/include/stdbool.h:

/usr/include/c++/15/bits/basic_string.tcc:

/usr/include/qt6/QtCore/qcontainerinfo.h:

/usr/include/bits/posix2_lim.h:

/usr/include/asm-generic/int-ll64.h:

/usr/include/qt6/QtCore/qxptype_traits.h:

/usr/include/bits/mathcalls-macros.h:

/usr/include/time.h:

/usr/include/qt6/QtCore/qlocale.h:

/usr/include/c++/15/tr1/hypergeometric.tcc:

/usr/include/bits/long-double.h:

/usr/include/qt6/QtWidgets/qtreeview.h:

/usr/include/asm/unistd_64.h:

/usr/include/qt6/QtGui/qvector2d.h:

/usr/include/c++/15/bits/specfun.h:

/usr/include/bits/types/__fpos_t.h:

/usr/include/c++/15/bits/ostream.h:

/usr/include/c++/15/bits/stl_raw_storage_iter.h:

/usr/include/c++/15/bits/ranges_util.h:

/usr/include/qt6/QtCore/qassert.h:

/usr/include/c++/15/chrono:

/usr/include/bits/confname.h:

/usr/include/c++/15/bits/cpp_type_traits.h:

/usr/include/bits/stdint-intn.h:

/usr/include/bits/wctype-wchar.h:

/usr/include/c++/15/ostream:

/usr/include/c++/15/bits/basic_string.h:

/usr/include/bits/libm-simd-decl-stubs.h:

/usr/include/qt6/QtCore/QTimer:

/usr/include/bits/endianness.h:

KNoteDo_autogen/VJIZ3MDCXP/moc_SettingsDialog.cpp:

/usr/include/c++/15/x86_64-redhat-linux/bits/c++allocator.h:

/usr/include/bits/time.h:

/usr/include/qt6/QtCore/qtdeprecationdefinitions.h:

/usr/include/asm/posix_types.h:

/usr/include/c++/15/bits/stl_iterator.h:

/usr/include/bits/types/__mbstate_t.h:

/usr/include/c++/15/bits/stream_iterator.h:

/usr/include/qt6/QtCore/qtimezone.h:

/usr/include/bits/stdio_lim.h:

/usr/include/c++/15/list:

/usr/include/bits/types/struct_timeval.h:

/usr/include/c++/15/bits/hashtable.h:

/usr/include/qt6/QtWidgets/qabstractitemview.h:

/usr/include/bits/struct_mutex.h:

/usr/include/qt6/QtGui/QStyleHints:

/usr/include/bits/thread-shared-types.h:

/usr/lib64/libXau.so.6:

/usr/include/bits/stdlib-float.h:

/usr/include/c++/15/x86_64-redhat-linux/bits/gthr-default.h:

/usr/include/qt6/QtCore/qcborcommon.h:

/usr/include/c++/15/bits/stl_vector.h:

/usr/include/qt6/QtGui/qtransform.h:

/usr/include/c++/15/bits/requires_hosted.h:

/usr/include/bits/time64.h:

/usr/include/bits/errno.h:

/usr/include/qt6/QtCore/qstdlibdetection.h:

/usr/include/bits/timex.h:

/usr/include/bits/flt-eval-method.h:

/usr/include/bits/types/FILE.h:

/usr/include/linux/posix_types.h:

/usr/include/qt6/QtCore/q23utility.h:

/usr/lib64/crti.o:

/usr/include/c++/15/bit:

/usr/include/bits/types/__fpos64_t.h:

/usr/include/qt6/QtGui/qvectornd.h:

/usr/lib/gcc/x86_64-redhat-linux/15/include/limits.h:

/usr/include/bits/types/__locale_t.h:

/usr/include/bits/mathcalls.h:

/usr/include/bits/types/clock_t.h:

/usr/include/c++/15/bits/charconv.h:

/usr/include/qt6/QtCore/qexceptionhandling.h:

/usr/include/c++/15/pstl/glue_algorithm_defs.h:

/usr/include/c++/15/tr1/poly_laguerre.tcc:

/usr/include/bits/types/locale_t.h:

/usr/include/c++/15/bits/alloc_traits.h:

/usr/include/bits/types/mbstate_t.h:

/usr/include/bits/types/sigset_t.h:

/usr/include/qt6/QtCore/qmimedata.h:

/usr/include/qt6/QtGui/qfontmetrics.h:

/usr/include/qt6/QtGui/qeventpoint.h:

/usr/include/c++/15/bits/cxxabi_init_exception.h:

/usr/include/c++/15/bits/shared_ptr_atomic.h:

/usr/include/bits/types/struct_sched_param.h:

/usr/include/qt6/QtWidgets/QStackedWidget:

/usr/include/qt6/QtCore/qjsonparseerror.h:

/usr/include/c++/15/bits/locale_conv.h:

/usr/include/qt6/QtCore/qcomparehelpers.h:

/usr/include/bits/waitflags.h:

/usr/include/qt6/QtCore/qcompilerdetection.h:

/usr/include/c++/15/x86_64-redhat-linux/bits/cpu_defines.h:

/usr/include/bits/unistd_ext.h:

/usr/include/qt6/QtCore/qiterator.h:

/usr/include/qt6/QtCore/qminmax.h:

/usr/include/c++/15/bits/range_access.h:

/home/<USER>/CLionProjects/KNoteDo/app/AppleNotesTheme.cpp:

/usr/include/bits/wordsize.h:

/usr/include/c++/15/bits/string_view.tcc:

/usr/include/bits/xopen_lim.h:

/usr/include/c++/15/array:

/usr/include/c++/15/bits/shared_ptr.h:

/usr/include/c++/15/iomanip:

/usr/include/qt6/QtCore/qobjectdefs_impl.h:

/usr/include/c++/15/atomic:

/usr/include/c++/15/stdexcept:

/usr/include/c++/15/bits/atomic_base.h:

/usr/include/features.h:

/usr/include/qt6/QtCore/q20iterator.h:

/usr/include/c++/15/bits/algorithmfwd.h:

/usr/include/asm/bitsperlong.h:

/usr/include/c++/15/initializer_list:

/usr/include/c++/15/bits/std_abs.h:

/usr/include/c++/15/ios:

/usr/include/c++/15/bits/chrono_io.h:

/usr/include/c++/15/iosfwd:

/usr/include/qt6/QtCore/qcalendar.h:

/usr/include/qt6/QtCore/qrefcount.h:

/usr/include/qt6/QtCore/qtmocconstants.h:

/usr/include/c++/15/istream:

/usr/include/c++/15/x86_64-redhat-linux/bits/ctype_base.h:

/usr/include/c++/15/iterator:

/usr/include/qt6/QtCore/qbasicatomic.h:

/usr/include/qt6/QtWidgets/qmenubar.h:

/usr/include/c++/15/limits:

/usr/lib64/libc.so:

/usr/include/qt6/QtCore/qmalloc.h:

/usr/include/c++/15/bits/unordered_set.h:

/usr/include/qt6/QtGui/qpen.h:

/usr/include/qt6/QtGui/qrgba64.h:

/usr/include/c++/15/locale:

/usr/include/c++/15/bits/refwrap.h:

/usr/include/c++/15/map:

/usr/include/qt6/QtGui/QTextList:

/usr/include/c++/15/bits/invoke.h:

/usr/include/qt6/QtCore/qfunctionpointer.h:

/usr/include/qt6/QtCore/qbytearray.h:

/usr/include/c++/15/memory:

/usr/include/c++/15/new:

/usr/include/linux/errno.h:

/usr/include/qt6/QtWidgets/qtwidgets-config.h:

/usr/include/c++/15/backward/binders.h:

/usr/include/c++/15/exception:

/usr/include/c++/15/numbers:

/usr/include/qt6/QtCore/qtversion.h:

/usr/include/c++/15/numeric:

/usr/include/c++/15/pstl/execution_defs.h:

/usr/include/qt6/QtCore/qjsonobject.h:

/usr/include/c++/15/bits/streambuf_iterator.h:

/usr/include/qt6/QtWidgets/qstyle.h:

/usr/include/c++/15/pstl/glue_memory_defs.h:

/lib64/libm.so.6:

/usr/include/c++/15/bits/basic_ios.tcc:

/usr/include/c++/15/pstl/pstl_config.h:

/usr/include/qt6/QtCore/qforeach.h:

/usr/include/c++/15/cerrno:

/usr/include/c++/15/span:

/usr/include/bits/timesize.h:

/usr/include/c++/15/string:

/usr/include/qt6/QtCore/qstring.h:

/usr/include/c++/15/tr1/bessel_function.tcc:

/usr/include/c++/15/tr1/beta_function.tcc:

/usr/include/c++/15/unordered_set:

/usr/include/stdc-predef.h:

/usr/include/c++/15/tr1/ell_integral.tcc:

/usr/include/c++/15/tr1/exp_integral.tcc:

/usr/include/qt6/QtCore/qtimer.h:

/usr/include/c++/15/tr1/legendre_function.tcc:

/usr/include/c++/15/tr1/modified_bessel_func.tcc:

/usr/include/c++/15/tr1/poly_hermite.tcc:

/usr/include/c++/15/tr1/riemann_zeta.tcc:

/usr/include/c++/15/x86_64-redhat-linux/bits/time_members.h:

/usr/include/c++/15/tr1/special_function_util.h:

/usr/include/qt6/QtGui/qtextcursor.h:

/usr/include/c++/15/tuple:

/usr/include/c++/15/type_traits:

/usr/include/c++/15/utility:

/usr/include/c++/15/bits/basic_ios.h:

/usr/include/c++/15/variant:

/usr/include/c++/15/vector:

/usr/include/c++/15/version:

/usr/include/c++/15/x86_64-redhat-linux/bits/atomic_word.h:

/lib64/libc.so.6:

/usr/include/c++/15/bits/stl_uninitialized.h:

/usr/include/c++/15/x86_64-redhat-linux/bits/ctype_inline.h:

/usr/include/qt6/QtCore/qcborvalue.h:

/usr/include/c++/15/x86_64-redhat-linux/bits/error_constants.h:

/usr/include/qt6/QtCore/qcoreapplication.h:

/usr/include/c++/15/x86_64-redhat-linux/bits/gthr.h:

/usr/include/qt6/QtCore/QSettings:

/usr/include/c++/15/bits/list.tcc:

/usr/include/c++/15/x86_64-redhat-linux/bits/messages_members.h:

/usr/include/ctype.h:

/usr/include/endian.h:

/usr/include/qt6/QtCore/qitemselectionmodel.h:

/usr/include/qt6/QtGui/QShortcut:

/usr/include/qt6/QtCore/qstringbuilder.h:

/usr/include/c++/15/bits/stl_construct.h:

/usr/include/qt6/QtGui/qregion.h:

/usr/include/features-time64.h:

/usr/include/qt6/QtCore/q17memory.h:

/usr/include/qt6/QtGui/QFontMetrics:

/usr/include/gnu/stubs-64.h:

/usr/include/qt6/QtCore/qtversionchecks.h:

/usr/include/qt6/QtWidgets/qmessagebox.h:

/usr/include/qt6/QtCore/qatomic.h:

/usr/include/linux/close_range.h:

/usr/include/qt6/QtCore/qconstructormacros.h:

/usr/include/qt6/QtCore/qutf8stringview.h:

/usr/include/linux/limits.h:

/usr/include/linux/types.h:

/usr/include/math.h:

/usr/include/pthread.h:

/usr/include/qt6/QtCore/q20memory.h:

/usr/include/qt6/QtGui/qimage.h:

/usr/include/qt6/QtCore/q20utility.h:

/usr/include/qt6/QtCore/q23type_traits.h:

/usr/include/qt6/QtWidgets/QHBoxLayout:

/usr/include/qt6/QtCore/qabstracteventdispatcher.h:

/usr/include/qt6/QtWidgets/QHeaderView:

/usr/include/qt6/QtCore/qarraydata.h:

/usr/include/qt6/QtCore/qarraydataops.h:

/usr/include/c++/15/cstdlib:

/usr/include/qt6/QtCore/qflags.h:

/usr/include/c++/15/unordered_map:

/usr/include/qt6/QtCore/qarraydatapointer.h:

/usr/include/qt6/QtCore/qatomic_cxx11.h:

CMakeFiles/KNoteDo.dir/app/AppleNotesTheme.cpp.o:

/usr/include/qt6/QtCore/qbindingstorage.h:

/usr/include/c++/15/bits/version.h:

/usr/include/qt6/QtCore/qbytearraylist.h:

/usr/include/c++/15/debug/debug.h:

/usr/include/qt6/QtCore/qbytearrayview.h:

/usr/include/qt6/QtCore/qchar.h:

/usr/include/qt6/QtCore/qcompare_impl.h:

/usr/include/c++/15/bits/utility.h:

/usr/include/qt6/QtCore/qconfig-64.h:

/usr/include/qt6/QtCore/qcontainerfwd.h:

/usr/include/qt6/QtCore/qcontiguouscache.h:

/usr/include/qt6/QtWidgets/QVBoxLayout:

/usr/include/qt6/QtCore/qdeadlinetimer.h:

/usr/include/qt6/QtCore/qdebug.h:

/usr/include/qt6/QtCore/qfiledevice.h:

/usr/include/qt6/QtCore/qelapsedtimer.h:

/usr/include/qt6/QtCore/qurl.h:

/usr/include/qt6/QtCore/qendian.h:

/usr/include/qt6/QtCore/qfloat16.h:

/usr/include/qt6/QtWidgets/QDialogButtonBox:

/usr/include/qt6/QtCore/qlatin1stringview.h:

/usr/include/bits/getopt_posix.h:

/usr/include/qt6/QtGui/qpolygon.h:

/usr/include/qt6/QtCore/qfunctionaltools_impl.h:

/usr/include/qt6/QtCore/qsharedpointer_impl.h:

/usr/include/qt6/QtCore/qgenericatomic.h:

/usr/include/qt6/QtCore/qglobal.h:

/usr/include/qt6/QtCore/qhash.h:

/usr/lib64/libharfbuzz.so.0:

/usr/include/qt6/QtCore/qiodevicebase.h:

/usr/include/bits/stdint-uintn.h:

/usr/include/qt6/QtCore/qlogging.h:

/usr/include/qt6/QtCore/qmap.h:

/usr/include/qt6/QtCore/qmargins.h:

/usr/include/qt6/QtWidgets/qsizepolicy.h:

/usr/include/qt6/QtWidgets/QSplitter:

/usr/include/qt6/QtCore/qmetacontainer.h:

/usr/include/qt6/QtCore/qmetatype.h:

/usr/include/c++/15/pstl/glue_numeric_defs.h:

/home/<USER>/CLionProjects/KNoteDo/app/RichTextEditor.h:

/usr/include/qt6/QtCore/qnumeric.h:

/usr/include/qt6/QtCore/qobject.h:

/usr/include/c++/15/tr1/gamma.tcc:

/usr/include/limits.h:

/usr/include/qt6/QtCore/qglobalstatic.h:

/usr/include/qt6/QtCore/qobjectdefs.h:

/usr/include/qt6/QtCore/qpair.h:

/usr/include/qt6/QtCore/qpoint.h:

/usr/lib64/crt1.o:

/usr/include/qt6/QtCore/qprocessordetection.h:

/usr/include/qt6/QtCore/qscopedpointer.h:

/usr/include/qt6/QtCore/qscopeguard.h:

/usr/include/c++/15/bits/enable_special_members.h:

/usr/include/qt6/QtCore/qset.h:

/usr/include/c++/15/bits/stl_map.h:

/usr/include/c++/15/ratio:

/usr/include/qt6/QtCore/qsharedpointer.h:

/usr/include/qt6/QtGui/QTextBlockFormat:

/usr/include/qt6/QtCore/qsize.h:

/usr/include/qt6/QtCore/qtnoop.h:

/usr/include/qt6/QtCore/qspan.h:

/usr/include/qt6/QtCore/qstringlist.h:

/usr/include/qt6/QtCore/qstringliteral.h:

/usr/include/assert.h:

/usr/include/qt6/QtCore/qstringmatcher.h:

/usr/include/c++/15/ext/numeric_traits.h:

/usr/include/qt6/QtWidgets/qdialog.h:

/usr/include/c++/15/compare:

/usr/include/qt6/QtCore/qstringtokenizer.h:

/usr/include/qt6/QtCore/qstringview.h:

/usr/include/syscall.h:

/usr/include/qt6/QtCore/qswap.h:

/usr/include/c++/15/bits/nested_exception.h:

/usr/include/c++/15/bits/memoryfwd.h:

/usr/include/qt6/QtCore/qsysinfo.h:

/usr/include/qt6/QtCore/qsystemdetection.h:

/usr/include/qt6/QtCore/qtclasshelpermacros.h:

CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.o:

/usr/include/c++/15/bits/locale_facets.h:

/usr/include/gnu/stubs.h:

/usr/include/qt6/QtCore/qtconfigmacros.h:

/usr/include/qt6/QtCore/qtcore-config.h:

/usr/include/c++/15/cstddef:

/usr/include/qt6/QtCore/qtcoreglobal.h:

/usr/include/c++/15/bits/allocated_ptr.h:

/usr/include/locale.h:

/usr/include/qt6/QtCore/qtdeprecationmarkers.h:

KNoteDo_autogen/mocs_compilation.cpp:

/usr/include/qt6/QtCore/qtenvironmentvariables.h:

/usr/include/qt6/QtCore/qtextstream.h:

/usr/include/qt6/QtCore/qtformat_impl.h:

/usr/include/qt6/QtCore/qtmetamacros.h:

/usr/include/qt6/QtCore/qcoreevent.h:

/usr/include/qt6/QtGui/qbitmap.h:

/usr/include/qt6/QtCore/qtpreprocessorsupport.h:

/usr/include/qt6/QtWidgets/qabstractslider.h:

/usr/include/qt6/QtCore/qtypeinfo.h:

/usr/include/qt6/QtGui/qpalette.h:

/usr/include/qt6/QtCore/qttranslation.h:

/usr/include/c++/15/bits/stl_multimap.h:

/usr/include/qt6/QtCore/qversiontagging.h:

/usr/include/qt6/QtCore/qttypetraits.h:

/usr/include/c++/15/bits/locale_classes.h:

/usr/include/qt6/QtCore/qtconfiginclude.h:

/usr/include/qt6/QtCore/qtypes.h:

/usr/include/qt6/QtCore/qalgorithms.h:

/usr/include/qt6/QtGui/qwindowdefs.h:

/usr/include/c++/15/stdlib.h:

/usr/include/c++/15/x86_64-redhat-linux/bits/c++config.h:

/usr/include/qt6/QtCore/qvarlengtharray.h:

/usr/include/qt6/QtCore/qyieldcpu.h:

/usr/include/qt6/QtGui/qcursor.h:

/usr/include/qt6/QtCore/qbasictimer.h:

/usr/include/qt6/QtGui/qfontvariableaxis.h:

/usr/include/qt6/QtGui/qicon.h:

/usr/lib64/libcrypto.so.3:

/usr/include/qt6/QtWidgets/QToolBar:

/usr/include/qt6/QtCore/qstringconverter_base.h:

/usr/include/qt6/QtGui/qpixmap.h:

/usr/include/qt6/QtGui/qpicture.h:

/usr/include/qt6/QtGui/qrgb.h:

/usr/include/qt6/QtCore/qmath.h:

/usr/include/qt6/QtGui/qtextformat.h:

/usr/include/qt6/QtGui/qtextoption.h:

/usr/include/qt6/QtWidgets/qlineedit.h:

/usr/include/unistd.h:

/usr/include/qt6/QtGui/qfont.h:

/usr/include/qt6/QtGui/qtguiglobal.h:

/home/<USER>/CLionProjects/KNoteDo/app/SettingsDialog.h:

/usr/include/qt6/QtGui/qvalidator.h:

/usr/include/qt6/QtWidgets/QDialog:

/usr/include/qt6/QtWidgets/qabstractitemdelegate.h:

/usr/include/qt6/QtCore/qbytearrayalgorithms.h:

/usr/include/qt6/QtWidgets/qframe.h:

/usr/include/qt6/QtWidgets/qlistview.h:

/usr/include/qt6/QtCore/qtmochelpers.h:

/usr/include/qt6/QtWidgets/qlistwidget.h:

/usr/include/qt6/QtWidgets/qmainwindow.h:

/usr/include/stdlib.h:

/usr/include/c++/15/bits/locale_facets.tcc:

/usr/include/qt6/QtCore/qrect.h:

/usr/include/qt6/QtWidgets/qslider.h:

/usr/include/qt6/QtWidgets/qstyleoption.h:

/usr/include/libintl.h:

/usr/include/qt6/QtWidgets/qtabbar.h:

/usr/include/qt6/QtWidgets/qtwidgetsexports.h:

/usr/include/qt6/QtWidgets/qtwidgetsglobal.h:
