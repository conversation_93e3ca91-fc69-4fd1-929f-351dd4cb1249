CMakeFiles/KNoteDo.dir/app/AppleNotesTheme.cpp.o: \
 /home/<USER>/CLionProjects/KNoteDo/app/AppleNotesTheme.cpp \
 /usr/include/stdc-predef.h \
 /home/<USER>/CLionProjects/KNoteDo/app/AppleNotesTheme.h \
 /usr/include/qt6/QtCore/QString /usr/include/qt6/QtCore/qstring.h \
 /usr/include/qt6/QtCore/qchar.h /usr/include/qt6/QtCore/qglobal.h \
 /usr/include/c++/15/type_traits \
 /usr/include/c++/15/x86_64-redhat-linux/bits/c++config.h \
 /usr/include/bits/wordsize.h \
 /usr/include/c++/15/x86_64-redhat-linux/bits/os_defines.h \
 /usr/include/features.h /usr/include/features-time64.h \
 /usr/include/bits/timesize.h /usr/include/sys/cdefs.h \
 /usr/include/bits/long-double.h /usr/include/gnu/stubs.h \
 /usr/include/gnu/stubs-64.h \
 /usr/include/c++/15/x86_64-redhat-linux/bits/cpu_defines.h \
 /usr/include/c++/15/pstl/pstl_config.h \
 /usr/include/c++/15/bits/version.h /usr/include/c++/15/cstddef \
 /usr/lib/gcc/x86_64-redhat-linux/15/include/stddef.h \
 /usr/include/c++/15/utility /usr/include/c++/15/bits/stl_relops.h \
 /usr/include/c++/15/bits/stl_pair.h /usr/include/c++/15/bits/move.h \
 /usr/include/c++/15/bits/utility.h /usr/include/c++/15/compare \
 /usr/include/c++/15/concepts /usr/include/c++/15/initializer_list \
 /usr/include/c++/15/ext/numeric_traits.h \
 /usr/include/c++/15/bits/cpp_type_traits.h \
 /usr/include/c++/15/ext/type_traits.h /usr/include/c++/15/cstdint \
 /usr/lib/gcc/x86_64-redhat-linux/15/include/stdint.h \
 /usr/include/stdint.h /usr/include/bits/libc-header-start.h \
 /usr/include/bits/types.h /usr/include/bits/typesizes.h \
 /usr/include/bits/time64.h /usr/include/bits/wchar.h \
 /usr/include/bits/stdint-intn.h /usr/include/bits/stdint-uintn.h \
 /usr/include/bits/stdint-least.h /usr/include/assert.h \
 /usr/lib/gcc/x86_64-redhat-linux/15/include/stdbool.h \
 /usr/include/qt6/QtCore/qtcoreglobal.h \
 /usr/include/qt6/QtCore/qtversionchecks.h \
 /usr/include/qt6/QtCore/qtconfiginclude.h /usr/include/c++/15/version \
 /usr/include/qt6/QtCore/qconfig.h /usr/include/qt6/QtCore/qconfig-64.h \
 /usr/include/qt6/QtCore/qtcore-config.h \
 /usr/include/qt6/QtCore/qtconfigmacros.h \
 /usr/include/qt6/QtCore/qtdeprecationdefinitions.h \
 /usr/include/qt6/QtCore/qcompilerdetection.h \
 /usr/include/qt6/QtCore/qprocessordetection.h \
 /usr/include/qt6/QtCore/qsystemdetection.h \
 /usr/include/qt6/QtCore/qtcoreexports.h \
 /usr/include/qt6/QtCore/qtdeprecationmarkers.h \
 /usr/include/qt6/QtCore/qtclasshelpermacros.h \
 /usr/include/qt6/QtCore/qtpreprocessorsupport.h \
 /usr/include/qt6/QtCore/qassert.h /usr/include/qt6/QtCore/qtnoop.h \
 /usr/include/qt6/QtCore/qtypes.h /usr/include/qt6/QtCore/qtversion.h \
 /usr/include/qt6/QtCore/qtypeinfo.h \
 /usr/include/qt6/QtCore/qcontainerfwd.h /usr/include/c++/15/limits \
 /usr/include/qt6/QtCore/qsysinfo.h /usr/include/qt6/QtCore/qlogging.h \
 /usr/include/qt6/QtCore/qflags.h /usr/include/qt6/QtCore/qcompare_impl.h \
 /usr/include/c++/15/algorithm /usr/include/c++/15/bits/stl_algobase.h \
 /usr/include/c++/15/bits/functexcept.h \
 /usr/include/c++/15/bits/exception_defines.h \
 /usr/include/c++/15/bits/stl_iterator_base_types.h \
 /usr/include/c++/15/bits/iterator_concepts.h \
 /usr/include/c++/15/bits/ptr_traits.h \
 /usr/include/c++/15/bits/ranges_cmp.h \
 /usr/include/c++/15/bits/stl_iterator_base_funcs.h \
 /usr/include/c++/15/bits/concept_check.h \
 /usr/include/c++/15/debug/assertions.h \
 /usr/include/c++/15/bits/stl_iterator.h /usr/include/c++/15/new \
 /usr/include/c++/15/bits/exception.h \
 /usr/include/c++/15/bits/stl_construct.h \
 /usr/include/c++/15/debug/debug.h \
 /usr/include/c++/15/bits/predefined_ops.h /usr/include/c++/15/bit \
 /usr/include/c++/15/bits/stl_algo.h \
 /usr/include/c++/15/bits/algorithmfwd.h \
 /usr/include/c++/15/bits/stl_heap.h \
 /usr/include/c++/15/bits/uniform_int_dist.h \
 /usr/include/c++/15/bits/stl_tempbuf.h /usr/include/c++/15/cstdlib \
 /usr/include/stdlib.h /usr/include/bits/waitflags.h \
 /usr/include/bits/waitstatus.h /usr/include/bits/floatn.h \
 /usr/include/bits/floatn-common.h /usr/include/bits/types/locale_t.h \
 /usr/include/bits/types/__locale_t.h /usr/include/sys/types.h \
 /usr/include/bits/types/clock_t.h /usr/include/bits/types/clockid_t.h \
 /usr/include/bits/types/time_t.h /usr/include/bits/types/timer_t.h \
 /usr/include/endian.h /usr/include/bits/endian.h \
 /usr/include/bits/endianness.h /usr/include/bits/byteswap.h \
 /usr/include/bits/uintn-identity.h /usr/include/sys/select.h \
 /usr/include/bits/select.h /usr/include/bits/types/sigset_t.h \
 /usr/include/bits/types/__sigset_t.h \
 /usr/include/bits/types/struct_timeval.h \
 /usr/include/bits/types/struct_timespec.h \
 /usr/include/bits/pthreadtypes.h /usr/include/bits/thread-shared-types.h \
 /usr/include/bits/pthreadtypes-arch.h \
 /usr/include/bits/atomic_wide_counter.h /usr/include/bits/struct_mutex.h \
 /usr/include/bits/struct_rwlock.h /usr/include/alloca.h \
 /usr/include/bits/stdlib-float.h /usr/include/c++/15/bits/std_abs.h \
 /usr/include/c++/15/bits/ranges_algo.h \
 /usr/include/c++/15/bits/ranges_algobase.h \
 /usr/include/c++/15/bits/ranges_base.h \
 /usr/include/c++/15/bits/max_size_type.h /usr/include/c++/15/numbers \
 /usr/include/c++/15/bits/invoke.h /usr/include/c++/15/bits/ranges_util.h \
 /usr/include/c++/15/pstl/glue_algorithm_defs.h \
 /usr/include/c++/15/pstl/execution_defs.h \
 /usr/include/qt6/QtCore/qatomic.h /usr/include/qt6/QtCore/qbasicatomic.h \
 /usr/include/qt6/QtCore/qatomic_cxx11.h \
 /usr/include/qt6/QtCore/qgenericatomic.h \
 /usr/include/qt6/QtCore/qyieldcpu.h /usr/include/c++/15/atomic \
 /usr/include/c++/15/bits/atomic_base.h \
 /usr/include/c++/15/bits/atomic_lockfree_defines.h \
 /usr/include/c++/15/bits/atomic_wait.h \
 /usr/include/c++/15/bits/functional_hash.h \
 /usr/include/c++/15/bits/hash_bytes.h \
 /usr/include/c++/15/x86_64-redhat-linux/bits/gthr.h \
 /usr/include/c++/15/x86_64-redhat-linux/bits/gthr-default.h \
 /usr/include/pthread.h /usr/include/sched.h /usr/include/bits/sched.h \
 /usr/include/linux/sched/types.h /usr/include/linux/types.h \
 /usr/include/asm/types.h /usr/include/asm-generic/types.h \
 /usr/include/asm-generic/int-ll64.h /usr/include/asm/bitsperlong.h \
 /usr/include/asm-generic/bitsperlong.h /usr/include/linux/posix_types.h \
 /usr/include/linux/stddef.h /usr/include/asm/posix_types.h \
 /usr/include/asm/posix_types_64.h /usr/include/asm-generic/posix_types.h \
 /usr/include/bits/types/struct_sched_param.h /usr/include/bits/cpu-set.h \
 /usr/include/time.h /usr/include/bits/time.h /usr/include/bits/timex.h \
 /usr/include/bits/types/struct_tm.h \
 /usr/include/bits/types/struct_itimerspec.h /usr/include/bits/setjmp.h \
 /usr/include/bits/types/struct___jmp_buf_tag.h \
 /usr/include/bits/pthread_stack_min-dynamic.h /usr/include/c++/15/cerrno \
 /usr/include/errno.h /usr/include/bits/errno.h \
 /usr/include/linux/errno.h /usr/include/asm/errno.h \
 /usr/include/asm-generic/errno.h /usr/include/asm-generic/errno-base.h \
 /usr/include/bits/types/error_t.h /usr/include/c++/15/climits \
 /usr/lib/gcc/x86_64-redhat-linux/15/include/limits.h \
 /usr/lib/gcc/x86_64-redhat-linux/15/include/syslimits.h \
 /usr/include/limits.h /usr/include/bits/posix1_lim.h \
 /usr/include/bits/local_lim.h /usr/include/linux/limits.h \
 /usr/include/bits/posix2_lim.h /usr/include/bits/xopen_lim.h \
 /usr/include/bits/uio_lim.h /usr/include/unistd.h \
 /usr/include/bits/posix_opt.h /usr/include/bits/environments.h \
 /usr/include/bits/confname.h /usr/include/bits/getopt_posix.h \
 /usr/include/bits/getopt_core.h /usr/include/bits/unistd_ext.h \
 /usr/include/linux/close_range.h /usr/include/syscall.h \
 /usr/include/sys/syscall.h /usr/include/asm/unistd.h \
 /usr/include/asm/unistd_64.h /usr/include/bits/syscall.h \
 /usr/include/c++/15/bits/std_mutex.h \
 /usr/include/qt6/QtCore/qconstructormacros.h \
 /usr/include/qt6/QtCore/qdarwinhelpers.h \
 /usr/include/qt6/QtCore/qexceptionhandling.h \
 /usr/include/qt6/QtCore/qforeach.h \
 /usr/include/qt6/QtCore/qttypetraits.h /usr/include/c++/15/optional \
 /usr/include/c++/15/exception /usr/include/c++/15/bits/exception_ptr.h \
 /usr/include/c++/15/bits/cxxabi_init_exception.h \
 /usr/include/c++/15/typeinfo /usr/include/c++/15/bits/nested_exception.h \
 /usr/include/c++/15/bits/enable_special_members.h \
 /usr/include/c++/15/tuple /usr/include/c++/15/bits/uses_allocator.h \
 /usr/include/c++/15/variant /usr/include/c++/15/bits/monostate.h \
 /usr/include/c++/15/bits/parse_numbers.h \
 /usr/include/qt6/QtCore/qfunctionpointer.h \
 /usr/include/qt6/QtCore/qglobalstatic.h \
 /usr/include/qt6/QtCore/qmalloc.h /usr/include/qt6/QtCore/qminmax.h \
 /usr/include/qt6/QtCore/qnumeric.h /usr/include/c++/15/cmath \
 /usr/include/c++/15/bits/requires_hosted.h /usr/include/math.h \
 /usr/include/bits/math-vector.h /usr/include/bits/libm-simd-decl-stubs.h \
 /usr/include/bits/flt-eval-method.h /usr/include/bits/fp-logb.h \
 /usr/include/bits/fp-fast.h /usr/include/bits/mathcalls-macros.h \
 /usr/include/bits/mathcalls-helper-functions.h \
 /usr/include/bits/mathcalls.h /usr/include/bits/mathcalls-narrow.h \
 /usr/include/bits/iscanonical.h /usr/include/c++/15/bits/specfun.h \
 /usr/include/c++/15/tr1/gamma.tcc \
 /usr/include/c++/15/tr1/special_function_util.h \
 /usr/include/c++/15/tr1/bessel_function.tcc \
 /usr/include/c++/15/tr1/beta_function.tcc \
 /usr/include/c++/15/tr1/ell_integral.tcc \
 /usr/include/c++/15/tr1/exp_integral.tcc \
 /usr/include/c++/15/tr1/hypergeometric.tcc \
 /usr/include/c++/15/tr1/legendre_function.tcc \
 /usr/include/c++/15/tr1/modified_bessel_func.tcc \
 /usr/include/c++/15/tr1/poly_hermite.tcc \
 /usr/include/c++/15/tr1/poly_laguerre.tcc \
 /usr/include/c++/15/tr1/riemann_zeta.tcc \
 /usr/include/qt6/QtCore/qoverload.h /usr/include/qt6/QtCore/qswap.h \
 /usr/include/qt6/QtCore/qtenvironmentvariables.h \
 /usr/include/qt6/QtCore/qtresource.h \
 /usr/include/qt6/QtCore/qttranslation.h \
 /usr/include/qt6/QtCore/qversiontagging.h \
 /usr/include/qt6/QtCore/qcompare.h \
 /usr/include/qt6/QtCore/qstdlibdetection.h \
 /usr/include/qt6/QtCore/qcomparehelpers.h \
 /usr/include/qt6/QtCore/q20type_traits.h /usr/include/c++/15/functional \
 /usr/include/c++/15/bits/stl_function.h \
 /usr/include/c++/15/backward/binders.h \
 /usr/include/c++/15/bits/refwrap.h \
 /usr/include/c++/15/bits/std_function.h \
 /usr/include/c++/15/unordered_map \
 /usr/include/c++/15/bits/unordered_map.h \
 /usr/include/c++/15/bits/hashtable.h \
 /usr/include/c++/15/bits/hashtable_policy.h \
 /usr/include/c++/15/ext/aligned_buffer.h \
 /usr/include/c++/15/ext/alloc_traits.h \
 /usr/include/c++/15/bits/alloc_traits.h \
 /usr/include/c++/15/bits/memoryfwd.h \
 /usr/include/c++/15/bits/allocator.h \
 /usr/include/c++/15/x86_64-redhat-linux/bits/c++allocator.h \
 /usr/include/c++/15/bits/new_allocator.h \
 /usr/include/c++/15/bits/node_handle.h \
 /usr/include/c++/15/bits/range_access.h \
 /usr/include/c++/15/bits/erase_if.h \
 /usr/include/c++/15/bits/memory_resource.h \
 /usr/include/c++/15/bits/uses_allocator_args.h \
 /usr/include/c++/15/vector /usr/include/c++/15/bits/stl_uninitialized.h \
 /usr/include/c++/15/bits/stl_vector.h \
 /usr/include/c++/15/bits/stl_bvector.h \
 /usr/include/c++/15/bits/vector.tcc /usr/include/c++/15/array \
 /usr/include/qt6/QtCore/qstringview.h \
 /usr/include/qt6/QtCore/qbytearray.h /usr/include/qt6/QtCore/qrefcount.h \
 /usr/include/qt6/QtCore/qnamespace.h \
 /usr/include/qt6/QtCore/qtmetamacros.h \
 /usr/include/qt6/QtCore/qarraydata.h /usr/include/qt6/QtCore/qpair.h \
 /usr/include/string.h /usr/include/strings.h \
 /usr/include/qt6/QtCore/qarraydatapointer.h \
 /usr/include/qt6/QtCore/qarraydataops.h \
 /usr/include/qt6/QtCore/qcontainertools_impl.h \
 /usr/include/qt6/QtCore/qxptype_traits.h /usr/include/c++/15/cstring \
 /usr/include/c++/15/iterator /usr/include/c++/15/bits/stream_iterator.h \
 /usr/include/c++/15/iosfwd /usr/include/c++/15/bits/stringfwd.h \
 /usr/include/c++/15/bits/postypes.h /usr/include/c++/15/cwchar \
 /usr/include/wchar.h \
 /usr/lib/gcc/x86_64-redhat-linux/15/include/stdarg.h \
 /usr/include/bits/types/wint_t.h /usr/include/bits/types/mbstate_t.h \
 /usr/include/bits/types/__mbstate_t.h /usr/include/bits/types/__FILE.h \
 /usr/include/bits/types/FILE.h \
 /usr/include/c++/15/bits/streambuf_iterator.h \
 /usr/include/c++/15/streambuf /usr/include/c++/15/bits/localefwd.h \
 /usr/include/c++/15/x86_64-redhat-linux/bits/c++locale.h \
 /usr/include/c++/15/clocale /usr/include/locale.h \
 /usr/include/bits/locale.h /usr/include/c++/15/cctype \
 /usr/include/ctype.h /usr/include/c++/15/bits/ios_base.h \
 /usr/include/c++/15/ext/atomicity.h \
 /usr/include/c++/15/x86_64-redhat-linux/bits/atomic_word.h \
 /usr/include/sys/single_threaded.h \
 /usr/include/c++/15/bits/locale_classes.h /usr/include/c++/15/string \
 /usr/include/c++/15/bits/char_traits.h \
 /usr/include/c++/15/bits/ostream_insert.h \
 /usr/include/c++/15/bits/cxxabi_forced.h \
 /usr/include/c++/15/bits/basic_string.h /usr/include/c++/15/string_view \
 /usr/include/c++/15/bits/string_view.tcc \
 /usr/include/c++/15/ext/string_conversions.h /usr/include/c++/15/cstdio \
 /usr/include/stdio.h /usr/include/bits/types/__fpos_t.h \
 /usr/include/bits/types/__fpos64_t.h \
 /usr/include/bits/types/struct_FILE.h \
 /usr/include/bits/types/cookie_io_functions_t.h \
 /usr/include/bits/stdio_lim.h /usr/include/c++/15/bits/charconv.h \
 /usr/include/c++/15/bits/basic_string.tcc \
 /usr/include/c++/15/bits/locale_classes.tcc \
 /usr/include/c++/15/system_error \
 /usr/include/c++/15/x86_64-redhat-linux/bits/error_constants.h \
 /usr/include/c++/15/stdexcept /usr/include/c++/15/bits/streambuf.tcc \
 /usr/include/c++/15/memory \
 /usr/include/c++/15/bits/stl_raw_storage_iter.h \
 /usr/include/c++/15/bits/align.h /usr/include/c++/15/bits/unique_ptr.h \
 /usr/include/c++/15/bits/ostream.h /usr/include/c++/15/ios \
 /usr/include/c++/15/bits/basic_ios.h \
 /usr/include/c++/15/bits/locale_facets.h /usr/include/c++/15/cwctype \
 /usr/include/wctype.h /usr/include/bits/wctype-wchar.h \
 /usr/include/c++/15/x86_64-redhat-linux/bits/ctype_base.h \
 /usr/include/c++/15/x86_64-redhat-linux/bits/ctype_inline.h \
 /usr/include/c++/15/bits/locale_facets.tcc \
 /usr/include/c++/15/bits/basic_ios.tcc \
 /usr/include/c++/15/bits/shared_ptr.h \
 /usr/include/c++/15/bits/shared_ptr_base.h \
 /usr/include/c++/15/bits/allocated_ptr.h \
 /usr/include/c++/15/ext/concurrence.h \
 /usr/include/c++/15/bits/shared_ptr_atomic.h \
 /usr/include/c++/15/backward/auto_ptr.h \
 /usr/include/c++/15/bits/ranges_uninitialized.h \
 /usr/include/c++/15/pstl/glue_memory_defs.h \
 /usr/include/qt6/QtCore/q20functional.h \
 /usr/include/qt6/QtCore/q20memory.h /usr/include/qt6/QtCore/q17memory.h \
 /usr/include/qt6/QtCore/qbytearrayalgorithms.h \
 /usr/include/qt6/QtCore/qbytearrayview.h \
 /usr/include/qt6/QtCore/qstringfwd.h /usr/include/c++/15/stdlib.h \
 /usr/include/qt6/QtCore/qstringliteral.h \
 /usr/include/qt6/QtCore/qstringalgorithms.h \
 /usr/include/qt6/QtCore/qlatin1stringview.h \
 /usr/include/qt6/QtCore/qanystringview.h \
 /usr/include/qt6/QtCore/qutf8stringview.h \
 /usr/include/qt6/QtCore/qstringtokenizer.h \
 /usr/include/qt6/QtCore/qstringbuilder.h \
 /usr/include/qt6/QtCore/qstring.h \
 /usr/include/qt6/QtCore/qstringconverter.h \
 /usr/include/qt6/QtCore/qstringconverter_base.h \
 /usr/include/qt6/QtWidgets/QWidget /usr/include/qt6/QtWidgets/qwidget.h \
 /usr/include/qt6/QtWidgets/qtwidgetsglobal.h \
 /usr/include/qt6/QtGui/qtguiglobal.h \
 /usr/include/qt6/QtGui/qtgui-config.h \
 /usr/include/qt6/QtGui/qtguiexports.h \
 /usr/include/qt6/QtWidgets/qtwidgets-config.h \
 /usr/include/qt6/QtWidgets/qtwidgetsexports.h \
 /usr/include/qt6/QtGui/qwindowdefs.h \
 /usr/include/qt6/QtCore/qobjectdefs.h \
 /usr/include/qt6/QtCore/qobjectdefs_impl.h \
 /usr/include/qt6/QtCore/qfunctionaltools_impl.h \
 /usr/include/qt6/QtCore/qobject.h /usr/include/qt6/QtCore/qlist.h \
 /usr/include/qt6/QtCore/qhashfunctions.h /usr/include/c++/15/numeric \
 /usr/include/c++/15/bits/stl_numeric.h \
 /usr/include/c++/15/pstl/glue_numeric_defs.h \
 /usr/include/qt6/QtCore/qiterator.h \
 /usr/include/qt6/QtCore/qbytearraylist.h \
 /usr/include/qt6/QtCore/qstringlist.h \
 /usr/include/qt6/QtCore/qalgorithms.h \
 /usr/include/qt6/QtCore/qstringmatcher.h \
 /usr/include/qt6/QtCore/qscopedpointer.h \
 /usr/include/qt6/QtCore/qmetatype.h \
 /usr/include/qt6/QtCore/qdatastream.h \
 /usr/include/qt6/QtCore/qiodevicebase.h \
 /usr/include/qt6/QtCore/qfloat16.h /usr/include/qt6/QtCore/qmath.h \
 /usr/include/qt6/QtCore/qtformat_impl.h /usr/include/c++/15/format \
 /usr/include/c++/15/charconv /usr/include/c++/15/locale \
 /usr/include/c++/15/bits/locale_facets_nonio.h /usr/include/c++/15/ctime \
 /usr/include/c++/15/x86_64-redhat-linux/bits/time_members.h \
 /usr/include/c++/15/x86_64-redhat-linux/bits/messages_members.h \
 /usr/include/libintl.h /usr/include/c++/15/bits/codecvt.h \
 /usr/include/c++/15/bits/locale_facets_nonio.tcc \
 /usr/include/c++/15/bits/locale_conv.h /usr/include/c++/15/span \
 /usr/include/c++/15/bits/formatfwd.h /usr/include/c++/15/bits/unicode.h \
 /usr/include/c++/15/bits/unicode-data.h \
 /usr/include/qt6/QtCore/qiterable.h \
 /usr/include/qt6/QtCore/qmetacontainer.h \
 /usr/include/qt6/QtCore/qcontainerinfo.h \
 /usr/include/qt6/QtCore/qtaggedpointer.h \
 /usr/include/qt6/QtCore/qscopeguard.h /usr/include/c++/15/list \
 /usr/include/c++/15/bits/stl_list.h /usr/include/c++/15/bits/list.tcc \
 /usr/include/c++/15/map /usr/include/c++/15/bits/stl_tree.h \
 /usr/include/c++/15/bits/stl_map.h \
 /usr/include/c++/15/bits/stl_multimap.h \
 /usr/include/qt6/QtCore/qobject_impl.h \
 /usr/include/qt6/QtCore/qbindingstorage.h /usr/include/c++/15/chrono \
 /usr/include/c++/15/bits/chrono.h /usr/include/c++/15/ratio \
 /usr/include/c++/15/sstream /usr/include/c++/15/istream \
 /usr/include/c++/15/ostream /usr/include/c++/15/bits/ostream.tcc \
 /usr/include/c++/15/bits/istream.tcc \
 /usr/include/c++/15/bits/sstream.tcc \
 /usr/include/c++/15/bits/chrono_io.h /usr/include/c++/15/iomanip \
 /usr/include/c++/15/bits/quoted_string.h \
 /usr/include/qt6/QtCore/qmargins.h /usr/include/qt6/QtCore/q23utility.h \
 /usr/include/qt6/QtCore/q20utility.h /usr/include/qt6/QtGui/qaction.h \
 /usr/include/qt6/QtGui/qkeysequence.h /usr/include/qt6/QtGui/qicon.h \
 /usr/include/qt6/QtCore/qsize.h /usr/include/qt6/QtGui/qpixmap.h \
 /usr/include/qt6/QtGui/qpaintdevice.h /usr/include/qt6/QtCore/qrect.h \
 /usr/include/qt6/QtCore/qpoint.h /usr/include/qt6/QtGui/qcolor.h \
 /usr/include/qt6/QtGui/qrgb.h /usr/include/qt6/QtGui/qrgba64.h \
 /usr/include/qt6/QtCore/qshareddata.h /usr/include/qt6/QtGui/qimage.h \
 /usr/include/qt6/QtGui/qpixelformat.h \
 /usr/include/qt6/QtGui/qtransform.h /usr/include/qt6/QtGui/qpolygon.h \
 /usr/include/qt6/QtGui/qregion.h /usr/include/qt6/QtCore/qspan.h \
 /usr/include/c++/15/cassert /usr/include/qt6/QtCore/q20iterator.h \
 /usr/include/qt6/QtCore/qline.h /usr/include/qt6/QtCore/qvariant.h \
 /usr/include/qt6/QtCore/qdebug.h /usr/include/qt6/QtCore/qtextstream.h \
 /usr/include/qt6/QtCore/qcontiguouscache.h \
 /usr/include/qt6/QtCore/qsharedpointer.h \
 /usr/include/qt6/QtCore/qsharedpointer_impl.h /usr/include/c++/15/set \
 /usr/include/c++/15/bits/stl_set.h \
 /usr/include/c++/15/bits/stl_multiset.h \
 /usr/include/c++/15/unordered_set \
 /usr/include/c++/15/bits/unordered_set.h /usr/include/qt6/QtCore/qmap.h \
 /usr/include/qt6/QtCore/qshareddata_impl.h \
 /usr/include/qt6/QtCore/qset.h /usr/include/qt6/QtCore/qhash.h \
 /usr/include/qt6/QtCore/qvarlengtharray.h \
 /usr/include/qt6/QtGui/qpalette.h /usr/include/qt6/QtGui/qbrush.h \
 /usr/include/qt6/QtGui/qfont.h /usr/include/qt6/QtCore/qendian.h \
 /usr/include/qt6/QtGui/qfontmetrics.h /usr/include/qt6/QtGui/qfontinfo.h \
 /usr/include/qt6/QtGui/qfontvariableaxis.h \
 /usr/include/qt6/QtWidgets/qsizepolicy.h \
 /usr/include/qt6/QtGui/qcursor.h /usr/include/qt6/QtGui/qbitmap.h \
 /usr/include/qt6/QtWidgets/QApplication \
 /usr/include/qt6/QtWidgets/qapplication.h \
 /usr/include/qt6/QtCore/qcoreapplication.h \
 /usr/include/qt6/QtCore/qcoreevent.h \
 /usr/include/qt6/QtCore/qbasictimer.h \
 /usr/include/qt6/QtCore/qabstracteventdispatcher.h \
 /usr/include/qt6/QtCore/qeventloop.h \
 /usr/include/qt6/QtCore/qdeadlinetimer.h \
 /usr/include/qt6/QtCore/qelapsedtimer.h \
 /usr/include/qt6/QtCore/qnativeinterface.h \
 /usr/include/qt6/QtCore/qcoreapplication_platform.h \
 /usr/include/qt6/QtGui/qguiapplication.h \
 /usr/include/qt6/QtGui/qinputmethod.h /usr/include/qt6/QtCore/qlocale.h \
 /usr/include/qt6/QtGui/qguiapplication_platform.h
