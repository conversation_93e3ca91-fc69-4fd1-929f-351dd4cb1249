# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/CLionProjects/KNoteDo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/CLionProjects/KNoteDo/build

# Include any dependencies generated for this target.
include CMakeFiles/KNoteDo.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/KNoteDo.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/KNoteDo.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/KNoteDo.dir/flags.make

CMakeFiles/KNoteDo.dir/codegen:
.PHONY : CMakeFiles/KNoteDo.dir/codegen

CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.o: CMakeFiles/KNoteDo.dir/flags.make
CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.o: KNoteDo_autogen/mocs_compilation.cpp
CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.o: CMakeFiles/KNoteDo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.o -MF CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.o -c /home/<USER>/CLionProjects/KNoteDo/build/KNoteDo_autogen/mocs_compilation.cpp

CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/CLionProjects/KNoteDo/build/KNoteDo_autogen/mocs_compilation.cpp > CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.i

CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/CLionProjects/KNoteDo/build/KNoteDo_autogen/mocs_compilation.cpp -o CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.s

CMakeFiles/KNoteDo.dir/main.cpp.o: CMakeFiles/KNoteDo.dir/flags.make
CMakeFiles/KNoteDo.dir/main.cpp.o: /home/<USER>/CLionProjects/KNoteDo/main.cpp
CMakeFiles/KNoteDo.dir/main.cpp.o: CMakeFiles/KNoteDo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/KNoteDo.dir/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/KNoteDo.dir/main.cpp.o -MF CMakeFiles/KNoteDo.dir/main.cpp.o.d -o CMakeFiles/KNoteDo.dir/main.cpp.o -c /home/<USER>/CLionProjects/KNoteDo/main.cpp

CMakeFiles/KNoteDo.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/KNoteDo.dir/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/CLionProjects/KNoteDo/main.cpp > CMakeFiles/KNoteDo.dir/main.cpp.i

CMakeFiles/KNoteDo.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/KNoteDo.dir/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/CLionProjects/KNoteDo/main.cpp -o CMakeFiles/KNoteDo.dir/main.cpp.s

CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.o: CMakeFiles/KNoteDo.dir/flags.make
CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.o: /home/<USER>/CLionProjects/KNoteDo/app/MainWindow.cpp
CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.o: CMakeFiles/KNoteDo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.o -MF CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.o.d -o CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.o -c /home/<USER>/CLionProjects/KNoteDo/app/MainWindow.cpp

CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/CLionProjects/KNoteDo/app/MainWindow.cpp > CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.i

CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/CLionProjects/KNoteDo/app/MainWindow.cpp -o CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.s

CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.o: CMakeFiles/KNoteDo.dir/flags.make
CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.o: /home/<USER>/CLionProjects/KNoteDo/app/RichTextEditor.cpp
CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.o: CMakeFiles/KNoteDo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.o -MF CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.o.d -o CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.o -c /home/<USER>/CLionProjects/KNoteDo/app/RichTextEditor.cpp

CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/CLionProjects/KNoteDo/app/RichTextEditor.cpp > CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.i

CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/CLionProjects/KNoteDo/app/RichTextEditor.cpp -o CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.s

CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.o: CMakeFiles/KNoteDo.dir/flags.make
CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.o: /home/<USER>/CLionProjects/KNoteDo/app/SettingsDialog.cpp
CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.o: CMakeFiles/KNoteDo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.o -MF CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.o.d -o CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.o -c /home/<USER>/CLionProjects/KNoteDo/app/SettingsDialog.cpp

CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/CLionProjects/KNoteDo/app/SettingsDialog.cpp > CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.i

CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/CLionProjects/KNoteDo/app/SettingsDialog.cpp -o CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.s

# Object files for target KNoteDo
KNoteDo_OBJECTS = \
"CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.o" \
"CMakeFiles/KNoteDo.dir/main.cpp.o" \
"CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.o" \
"CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.o" \
"CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.o"

# External object files for target KNoteDo
KNoteDo_EXTERNAL_OBJECTS =

KNoteDo: CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.o
KNoteDo: CMakeFiles/KNoteDo.dir/main.cpp.o
KNoteDo: CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.o
KNoteDo: CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.o
KNoteDo: CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.o
KNoteDo: CMakeFiles/KNoteDo.dir/build.make
KNoteDo: CMakeFiles/KNoteDo.dir/compiler_depend.ts
KNoteDo: /usr/lib64/libQt6PrintSupport.so.6.9.1
KNoteDo: /usr/lib64/libQt6Widgets.so.6.9.1
KNoteDo: /usr/lib64/libQt6Gui.so.6.9.1
KNoteDo: /usr/lib64/libGLX.so
KNoteDo: /usr/lib64/libOpenGL.so
KNoteDo: /usr/lib64/libQt6Core.so.6.9.1
KNoteDo: CMakeFiles/KNoteDo.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Linking CXX executable KNoteDo"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/KNoteDo.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/KNoteDo.dir/build: KNoteDo
.PHONY : CMakeFiles/KNoteDo.dir/build

CMakeFiles/KNoteDo.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/KNoteDo.dir/cmake_clean.cmake
.PHONY : CMakeFiles/KNoteDo.dir/clean

CMakeFiles/KNoteDo.dir/depend:
	cd /home/<USER>/CLionProjects/KNoteDo/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/CLionProjects/KNoteDo /home/<USER>/CLionProjects/KNoteDo /home/<USER>/CLionProjects/KNoteDo/build /home/<USER>/CLionProjects/KNoteDo/build /home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles/KNoteDo.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/KNoteDo.dir/depend

