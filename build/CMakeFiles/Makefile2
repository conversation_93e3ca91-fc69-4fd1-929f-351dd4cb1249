# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/CLionProjects/KNoteDo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/CLionProjects/KNoteDo/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/KNoteDo.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/KNoteDo.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/KNoteDo.dir/clean
clean: CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/KNoteDo_autogen.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/KNoteDo.dir

# All Build rule for target.
CMakeFiles/KNoteDo.dir/all: CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/all
CMakeFiles/KNoteDo.dir/all: CMakeFiles/KNoteDo_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10 "Built target KNoteDo"
.PHONY : CMakeFiles/KNoteDo.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/KNoteDo.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles 11
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/KNoteDo.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles 0
.PHONY : CMakeFiles/KNoteDo.dir/rule

# Convenience name for target.
KNoteDo: CMakeFiles/KNoteDo.dir/rule
.PHONY : KNoteDo

# codegen rule for target.
CMakeFiles/KNoteDo.dir/codegen: CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10 "Finished codegen for target KNoteDo"
.PHONY : CMakeFiles/KNoteDo.dir/codegen

# clean rule for target.
CMakeFiles/KNoteDo.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/clean
.PHONY : CMakeFiles/KNoteDo.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/KNoteDo_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/build.make CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/build.make CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles --progress-num= "Built target KNoteDo_autogen_timestamp_deps"
.PHONY : CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles 0
.PHONY : CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/rule

# Convenience name for target.
KNoteDo_autogen_timestamp_deps: CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/rule
.PHONY : KNoteDo_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/build.make CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles --progress-num= "Finished codegen for target KNoteDo_autogen_timestamp_deps"
.PHONY : CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/build.make CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/KNoteDo_autogen.dir

# All Build rule for target.
CMakeFiles/KNoteDo_autogen.dir/all: CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo_autogen.dir/build.make CMakeFiles/KNoteDo_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo_autogen.dir/build.make CMakeFiles/KNoteDo_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles --progress-num=11 "Built target KNoteDo_autogen"
.PHONY : CMakeFiles/KNoteDo_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/KNoteDo_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/KNoteDo_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles 0
.PHONY : CMakeFiles/KNoteDo_autogen.dir/rule

# Convenience name for target.
KNoteDo_autogen: CMakeFiles/KNoteDo_autogen.dir/rule
.PHONY : KNoteDo_autogen

# codegen rule for target.
CMakeFiles/KNoteDo_autogen.dir/codegen: CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo_autogen.dir/build.make CMakeFiles/KNoteDo_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles --progress-num=11 "Finished codegen for target KNoteDo_autogen"
.PHONY : CMakeFiles/KNoteDo_autogen.dir/codegen

# clean rule for target.
CMakeFiles/KNoteDo_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo_autogen.dir/build.make CMakeFiles/KNoteDo_autogen.dir/clean
.PHONY : CMakeFiles/KNoteDo_autogen.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

