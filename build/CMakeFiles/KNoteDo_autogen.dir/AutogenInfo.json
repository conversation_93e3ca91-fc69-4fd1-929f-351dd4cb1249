{"BUILD_DIR": "/home/<USER>/CLionProjects/KNoteDo/build/KNoteDo_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/CLionProjects/KNoteDo/build", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/CLionProjects/KNoteDo/build", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/CLionProjects/KNoteDo", "CMAKE_EXECUTABLE": "/usr/bin/cmake", "CMAKE_LIST_FILES": ["/home/<USER>/CLionProjects/KNoteDo/CMakeLists.txt", "/home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles/3.31.6/CMakeSystem.cmake", "/usr/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake", "/usr/share/cmake/Modules/Platform/Linux-Initialize.cmake", "/home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles/3.31.6/CMakeCXXCompiler.cmake", "/usr/share/cmake/Modules/CMakeSystemSpecificInformation.cmake", "/usr/share/cmake/Modules/CMakeGenericSystem.cmake", "/usr/share/cmake/Modules/CMakeInitializeConfigs.cmake", "/usr/share/cmake/Modules/Platform/Linux.cmake", "/usr/share/cmake/Modules/Platform/UnixPaths.cmake", "/usr/share/cmake/Modules/CMakeCXXInformation.cmake", "/usr/share/cmake/Modules/CMakeLanguageInformation.cmake", "/usr/share/cmake/Modules/Compiler/GNU-CXX.cmake", "/usr/share/cmake/Modules/Compiler/GNU.cmake", "/usr/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "/usr/share/cmake/Modules/Platform/Linux-GNU-CXX.cmake", "/usr/share/cmake/Modules/Platform/Linux-GNU.cmake", "/usr/share/cmake/Modules/CMakeCommonLanguageInclude.cmake", "/usr/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake", "/usr/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake", "/usr/share/cmake/Modules/Linker/GNU-CXX.cmake", "/usr/share/cmake/Modules/Linker/GNU.cmake", "/usr/share/cmake/Modules/Platform/Linker/Linux-GNU-CXX.cmake", "/usr/share/cmake/Modules/Platform/Linker/Linux-GNU.cmake", "/usr/share/cmake/Modules/Platform/Linker/GNU.cmake", "/usr/lib64/cmake/Qt6/Qt6ConfigVersion.cmake", "/usr/lib64/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "/usr/lib64/cmake/Qt6/Qt6Config.cmake", "/usr/lib64/cmake/Qt6/Qt6ConfigExtras.cmake", "/usr/lib64/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/usr/lib64/cmake/Qt6/QtInstallPaths.cmake", "/usr/lib64/cmake/Qt6/Qt6Targets.cmake", "/usr/lib64/cmake/Qt6/Qt6VersionlessAliasTargets.cmake", "/usr/lib64/cmake/Qt6/QtFeature.cmake", "/usr/share/cmake/Modules/CheckCXXCompilerFlag.cmake", "/usr/share/cmake/Modules/Internal/CheckCompilerFlag.cmake", "/usr/share/cmake/Modules/Internal/CheckFlagCommonConfig.cmake", "/usr/share/cmake/Modules/Internal/CheckSourceCompiles.cmake", "/usr/share/cmake/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake", "/usr/share/cmake/Modules/CheckCXXSourceCompiles.cmake", "/usr/share/cmake/Modules/Internal/CheckSourceCompiles.cmake", "/usr/lib64/cmake/Qt6/QtFeatureCommon.cmake", "/usr/lib64/cmake/Qt6/QtPublicAndroidHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicAppleHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicExternalProjectHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicGitHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicPluginHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicPluginHelpers_v2.cmake", "/usr/lib64/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicSbomCpeHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicSbomDepHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicSbomFileHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicSbomHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicSbomOpsHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicSbomPurlHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicSbomPythonHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicTargetHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicTestHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicToolHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "/usr/lib64/cmake/Qt6/QtPublicWindowsHelpers.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6/Qt6Dependencies.cmake", "/usr/share/cmake/Modules/FindThreads.cmake", "/usr/share/cmake/Modules/CheckLibraryExists.cmake", "/usr/share/cmake/Modules/CheckIncludeFileCXX.cmake", "/usr/share/cmake/Modules/CheckCXXSourceCompiles.cmake", "/usr/share/cmake/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake/Modules/FindPackageMessage.cmake", "/usr/lib64/cmake/Qt6Core/Qt6CoreConfigVersion.cmake", "/usr/lib64/cmake/Qt6Core/Qt6CoreConfigVersionImpl.cmake", "/usr/lib64/cmake/Qt6Core/Qt6CoreConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Core/Qt6CoreDependencies.cmake", "/usr/lib64/cmake/Qt6/FindWrapAtomic.cmake", "/usr/share/cmake/Modules/CheckCXXSourceCompiles.cmake", "/usr/share/cmake/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake/Modules/FindPackageMessage.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersion.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersionImpl.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsDependencies.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsTargets.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsVersionlessTargets.cmake", "/usr/lib64/cmake/Qt6Core/Qt6CoreTargets.cmake", "/usr/lib64/cmake/Qt6Core/Qt6CoreTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Core/Qt6CoreAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Core/Qt6CoreMacros.cmake", "/usr/lib64/cmake/Qt6Core/Qt6CoreConfigExtras.cmake", "/usr/share/cmake/Modules/GNUInstallDirs.cmake", "/usr/lib64/cmake/Qt6Core/Qt6CoreVersionlessAliasTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6GuiConfigVersion.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6GuiConfigVersionImpl.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6GuiConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6GuiDependencies.cmake", "/usr/lib64/cmake/Qt6/FindWrapOpenGL.cmake", "/usr/share/cmake/Modules/FindOpenGL.cmake", "/usr/share/cmake/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake/Modules/FindPackageMessage.cmake", "/usr/share/cmake/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake/Modules/FindPackageMessage.cmake", "/usr/lib64/cmake/Qt6/FindWrapVulkanHeaders.cmake", "/usr/share/cmake/Modules/FindVulkan.cmake", "/usr/share/cmake/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake/Modules/FindPackageMessage.cmake", "/usr/share/cmake/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake/Modules/FindPackageMessage.cmake", "/usr/lib64/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersion.cmake", "/usr/lib64/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersionImpl.cmake", "/usr/lib64/cmake/Qt6GuiTools/Qt6GuiToolsConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6GuiTools/Qt6GuiToolsDependencies.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersion.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersionImpl.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsDependencies.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsTargets.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsVersionlessTargets.cmake", "/usr/lib64/cmake/Qt6GuiTools/Qt6GuiToolsTargets.cmake", "/usr/lib64/cmake/Qt6GuiTools/Qt6GuiToolsTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6GuiTools/Qt6GuiToolsAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6GuiTools/Qt6GuiToolsVersionlessTargets.cmake", "/usr/lib64/cmake/Qt6DBus/Qt6DBusConfigVersion.cmake", "/usr/lib64/cmake/Qt6DBus/Qt6DBusConfigVersionImpl.cmake", "/usr/lib64/cmake/Qt6DBus/Qt6DBusConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6DBus/Qt6DBusDependencies.cmake", "/usr/lib64/cmake/Qt6DBusTools/Qt6DBusToolsConfigVersion.cmake", "/usr/lib64/cmake/Qt6DBusTools/Qt6DBusToolsConfigVersionImpl.cmake", "/usr/lib64/cmake/Qt6DBusTools/Qt6DBusToolsConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6DBusTools/Qt6DBusToolsDependencies.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersion.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersionImpl.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsDependencies.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsTargets.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsVersionlessTargets.cmake", "/usr/lib64/cmake/Qt6DBusTools/Qt6DBusToolsTargets.cmake", "/usr/lib64/cmake/Qt6DBusTools/Qt6DBusToolsTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6DBusTools/Qt6DBusToolsAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6DBusTools/Qt6DBusToolsVersionlessTargets.cmake", "/usr/lib64/cmake/Qt6DBus/Qt6DBusTargets.cmake", "/usr/lib64/cmake/Qt6DBus/Qt6DBusTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6DBus/Qt6DBusAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6DBus/Qt6DBusMacros.cmake", "/usr/share/cmake/Modules/MacroAddFileDependencies.cmake", "/usr/lib64/cmake/Qt6DBus/Qt6DBusVersionlessAliasTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6GuiTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6GuiTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6GuiAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6GuiPlugins.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QComposePlatformInputContextPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QComposePlatformInputContextPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QComposePlatformInputContextPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QComposePlatformInputContextPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSEmulatorIntegrationPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSEmulatorIntegrationPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSEmulatorIntegrationPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSEmulatorIntegrationPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSIntegrationPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSIntegrationPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSIntegrationPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSIntegrationPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSKmsEglDeviceIntegrationPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSKmsEglDeviceIntegrationPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSKmsEglDeviceIntegrationPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSKmsEglDeviceIntegrationPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSKmsGbmIntegrationPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSKmsGbmIntegrationPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSKmsGbmIntegrationPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSKmsGbmIntegrationPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSX11IntegrationPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSX11IntegrationPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSX11IntegrationPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEglFSX11IntegrationPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEvdevKeyboardPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEvdevKeyboardPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEvdevKeyboardPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEvdevKeyboardPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEvdevMousePluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEvdevMousePluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEvdevMousePluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEvdevMousePluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEvdevTabletPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEvdevTabletPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEvdevTabletPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEvdevTabletPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEvdevTouchScreenPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEvdevTouchScreenPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEvdevTouchScreenPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QEvdevTouchScreenPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QGifPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QGifPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QGifPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QGifPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QGtk3ThemePluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QGtk3ThemePluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QGtk3ThemePluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QGtk3ThemePluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QICNSPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QICNSPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QICNSPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QICNSPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QICOPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QICOPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QICOPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QICOPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QIbusPlatformInputContextPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QIbusPlatformInputContextPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QIbusPlatformInputContextPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QIbusPlatformInputContextPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QJp2PluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QJp2PluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QJp2PluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QJp2PluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QJpegPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QJpegPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QJpegPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QJpegPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QLibInputPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QLibInputPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QLibInputPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QLibInputPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QLinuxFbIntegrationPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QLinuxFbIntegrationPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QLinuxFbIntegrationPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QLinuxFbIntegrationPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QMinimalEglIntegrationPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QMinimalEglIntegrationPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QMinimalEglIntegrationPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QMinimalEglIntegrationPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QMngPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QMngPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QMngPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QMngPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QTgaPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QTgaPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QTgaPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QTgaPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QTiffPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QTiffPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QTiffPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QTiffPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QTsLibPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QTsLibPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QTsLibPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QTsLibPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QTuioTouchPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QTuioTouchPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QTuioTouchPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QVkKhrDisplayIntegrationPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QVkKhrDisplayIntegrationPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QVkKhrDisplayIntegrationPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QVkKhrDisplayIntegrationPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QVncIntegrationPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QVncIntegrationPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QVncIntegrationPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QVncIntegrationPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QWbmpPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QWbmpPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QWbmpPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QWbmpPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QWebpPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QWebpPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QWebpPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QWebpPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QXcbEglIntegrationPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QXcbEglIntegrationPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QXcbEglIntegrationPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QXcbEglIntegrationPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QXcbGlxIntegrationPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QXcbGlxIntegrationPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QXcbGlxIntegrationPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QXcbGlxIntegrationPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QXcbIntegrationPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QXcbIntegrationPluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QXcbIntegrationPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QXcbIntegrationPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QXdgDesktopPortalThemePluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QXdgDesktopPortalThemePluginTargets.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QXdgDesktopPortalThemePluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6QXdgDesktopPortalThemePluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Gui/Qt6GuiVersionlessAliasTargets.cmake", "/usr/lib64/cmake/Qt6Widgets/Qt6WidgetsConfigVersion.cmake", "/usr/lib64/cmake/Qt6Widgets/Qt6WidgetsConfigVersionImpl.cmake", "/usr/lib64/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "/usr/lib64/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfigVersion.cmake", "/usr/lib64/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfigVersionImpl.cmake", "/usr/lib64/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6WidgetsTools/Qt6WidgetsToolsDependencies.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersion.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersionImpl.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsDependencies.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsTargets.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsVersionlessTargets.cmake", "/usr/lib64/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersion.cmake", "/usr/lib64/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersionImpl.cmake", "/usr/lib64/cmake/Qt6GuiTools/Qt6GuiToolsConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6GuiTools/Qt6GuiToolsDependencies.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersion.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersionImpl.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsDependencies.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsTargets.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6CoreTools/Qt6CoreToolsVersionlessTargets.cmake", "/usr/lib64/cmake/Qt6GuiTools/Qt6GuiToolsTargets.cmake", "/usr/lib64/cmake/Qt6GuiTools/Qt6GuiToolsAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6GuiTools/Qt6GuiToolsVersionlessTargets.cmake", "/usr/lib64/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets.cmake", "/usr/lib64/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6WidgetsTools/Qt6WidgetsToolsAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6WidgetsTools/Qt6WidgetsToolsVersionlessTargets.cmake", "/usr/lib64/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "/usr/lib64/cmake/Qt6Widgets/Qt6WidgetsTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6Widgets/Qt6WidgetsAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6Widgets/Qt6WidgetsMacros.cmake", "/usr/lib64/cmake/Qt6Widgets/Qt6WidgetsPlugins.cmake", "/usr/lib64/cmake/Qt6Widgets/Qt6WidgetsVersionlessAliasTargets.cmake", "/usr/lib64/cmake/Qt6PrintSupport/Qt6PrintSupportConfigVersion.cmake", "/usr/lib64/cmake/Qt6PrintSupport/Qt6PrintSupportConfigVersionImpl.cmake", "/usr/lib64/cmake/Qt6PrintSupport/Qt6PrintSupportConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6PrintSupport/Qt6PrintSupportDependencies.cmake", "/usr/share/cmake/Modules/FindCups.cmake", "/usr/share/cmake/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake/Modules/FindPackageMessage.cmake", "/usr/lib64/cmake/Qt6PrintSupport/Qt6PrintSupportTargets.cmake", "/usr/lib64/cmake/Qt6PrintSupport/Qt6PrintSupportTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6PrintSupport/Qt6PrintSupportAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6PrintSupport/Qt6PrintSupportPlugins.cmake", "/usr/lib64/cmake/Qt6PrintSupport/Qt6QCupsPrinterSupportPluginConfig.cmake", "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib64/cmake/Qt6PrintSupport/Qt6QCupsPrinterSupportPluginTargets.cmake", "/usr/lib64/cmake/Qt6PrintSupport/Qt6QCupsPrinterSupportPluginTargets-relwithdebinfo.cmake", "/usr/lib64/cmake/Qt6PrintSupport/Qt6QCupsPrinterSupportPluginAdditionalTargetInfo.cmake", "/usr/lib64/cmake/Qt6PrintSupport/Qt6PrintSupportVersionlessAliasTargets.cmake"], "CMAKE_SOURCE_DIR": "/home/<USER>/CLionProjects/KNoteDo", "CROSS_CONFIG": false, "DEP_FILE": "/home/<USER>/CLionProjects/KNoteDo/build/KNoteDo_autogen/deps", "DEP_FILE_RULE_NAME": "KNoteDo_autogen/timestamp", "HEADERS": [["/home/<USER>/CLionProjects/KNoteDo/app/AppleNotesTheme.h", "MU", "VJIZ3MDCXP/moc_AppleNotesTheme.cpp", null], ["/home/<USER>/CLionProjects/KNoteDo/app/MainWindow.h", "MU", "VJIZ3MDCXP/moc_MainWindow.cpp", null], ["/home/<USER>/CLionProjects/KNoteDo/app/RichTextEditor.h", "MU", "VJIZ3MDCXP/moc_RichTextEditor.cpp", null], ["/home/<USER>/CLionProjects/KNoteDo/app/SettingsDialog.h", "MU", "VJIZ3MDCXP/moc_SettingsDialog.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/home/<USER>/CLionProjects/KNoteDo/build/KNoteDo_autogen/include", "MOC_COMPILATION_FILE": "/home/<USER>/CLionProjects/KNoteDo/build/KNoteDo_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_PRINTSUPPORT_LIB", "QT_WIDGETS_LIB"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["/home/<USER>/CLionProjects/KNoteDo/app", "/usr/include/qt6", "/usr/include/qt6/QtWidgets", "/usr/include/qt6/QtGui", "/usr/include/qt6/QtCore", "/usr/lib64/qt6/mkspecs/linux-g++", "/usr/include/qt6/QtPrintSupport", "/usr/include", "/usr/include/c++/15", "/usr/include/c++/15/x86_64-redhat-linux", "/usr/include/c++/15/backward", "/usr/lib/gcc/x86_64-redhat-linux/15/include", "/usr/local/include"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT", "Q_ENUM_NS"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["/usr/bin/c++", "-std=gnu++20", "-dM", "-E", "-c", "/usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "/home/<USER>/CLionProjects/KNoteDo/build/KNoteDo_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 8, "PARSE_CACHE_FILE": "/home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles/KNoteDo_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "/usr/lib64/qt6/libexec/moc", "QT_UIC_EXECUTABLE": "/usr/lib64/qt6/libexec/uic", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 9, "SETTINGS_FILE": "/home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles/KNoteDo_autogen.dir/AutogenUsed.txt", "SOURCES": [["/home/<USER>/CLionProjects/KNoteDo/app/AppleNotesTheme.cpp", "MU", null], ["/home/<USER>/CLionProjects/KNoteDo/app/MainWindow.cpp", "MU", null], ["/home/<USER>/CLionProjects/KNoteDo/app/RichTextEditor.cpp", "MU", null], ["/home/<USER>/CLionProjects/KNoteDo/app/SettingsDialog.cpp", "MU", null], ["/home/<USER>/CLionProjects/KNoteDo/main.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "USE_BETTER_GRAPH": true, "VERBOSITY": 0}