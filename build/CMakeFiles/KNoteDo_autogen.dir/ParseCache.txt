# Generated by CMake. Changes will be overwritten.
/home/<USER>/CLionProjects/KNoteDo/app/AppleNotesTheme.cpp
/home/<USER>/CLionProjects/KNoteDo/app/AppleNotesTheme.h
/home/<USER>/CLionProjects/KNoteDo/main.cpp
/home/<USER>/CLionProjects/KNoteDo/app/SettingsDialog.cpp
/home/<USER>/CLionProjects/KNoteDo/app/MainWindow.cpp
/home/<USER>/CLionProjects/KNoteDo/app/MainWindow.h
 mmc:Q_OBJECT
 mdp:/home/<USER>/CLionProjects/KNoteDo/app/MainWindow.h
 mdp:/home/<USER>/CLionProjects/KNoteDo/build/KNoteDo_autogen/moc_predefs.h
 mdp:/usr/include/alloca.h
 mdp:/usr/include/asm-generic/bitsperlong.h
 mdp:/usr/include/asm-generic/errno-base.h
 mdp:/usr/include/asm-generic/errno.h
 mdp:/usr/include/asm-generic/int-ll64.h
 mdp:/usr/include/asm-generic/posix_types.h
 mdp:/usr/include/asm-generic/types.h
 mdp:/usr/include/asm/bitsperlong.h
 mdp:/usr/include/asm/errno.h
 mdp:/usr/include/asm/posix_types.h
 mdp:/usr/include/asm/posix_types_64.h
 mdp:/usr/include/asm/types.h
 mdp:/usr/include/asm/unistd.h
 mdp:/usr/include/asm/unistd_64.h
 mdp:/usr/include/assert.h
 mdp:/usr/include/bits/atomic_wide_counter.h
 mdp:/usr/include/bits/byteswap.h
 mdp:/usr/include/bits/confname.h
 mdp:/usr/include/bits/cpu-set.h
 mdp:/usr/include/bits/endian.h
 mdp:/usr/include/bits/endianness.h
 mdp:/usr/include/bits/environments.h
 mdp:/usr/include/bits/errno.h
 mdp:/usr/include/bits/floatn-common.h
 mdp:/usr/include/bits/floatn.h
 mdp:/usr/include/bits/getopt_core.h
 mdp:/usr/include/bits/getopt_posix.h
 mdp:/usr/include/bits/libc-header-start.h
 mdp:/usr/include/bits/local_lim.h
 mdp:/usr/include/bits/locale.h
 mdp:/usr/include/bits/long-double.h
 mdp:/usr/include/bits/posix1_lim.h
 mdp:/usr/include/bits/posix2_lim.h
 mdp:/usr/include/bits/posix_opt.h
 mdp:/usr/include/bits/pthread_stack_min-dynamic.h
 mdp:/usr/include/bits/pthreadtypes-arch.h
 mdp:/usr/include/bits/pthreadtypes.h
 mdp:/usr/include/bits/sched.h
 mdp:/usr/include/bits/select.h
 mdp:/usr/include/bits/setjmp.h
 mdp:/usr/include/bits/stdint-intn.h
 mdp:/usr/include/bits/stdio_lim.h
 mdp:/usr/include/bits/stdlib-float.h
 mdp:/usr/include/bits/struct_mutex.h
 mdp:/usr/include/bits/struct_rwlock.h
 mdp:/usr/include/bits/syscall.h
 mdp:/usr/include/bits/thread-shared-types.h
 mdp:/usr/include/bits/time.h
 mdp:/usr/include/bits/time64.h
 mdp:/usr/include/bits/timesize.h
 mdp:/usr/include/bits/timex.h
 mdp:/usr/include/bits/types.h
 mdp:/usr/include/bits/types/FILE.h
 mdp:/usr/include/bits/types/__FILE.h
 mdp:/usr/include/bits/types/__fpos64_t.h
 mdp:/usr/include/bits/types/__fpos_t.h
 mdp:/usr/include/bits/types/__locale_t.h
 mdp:/usr/include/bits/types/__mbstate_t.h
 mdp:/usr/include/bits/types/__sigset_t.h
 mdp:/usr/include/bits/types/clock_t.h
 mdp:/usr/include/bits/types/clockid_t.h
 mdp:/usr/include/bits/types/cookie_io_functions_t.h
 mdp:/usr/include/bits/types/error_t.h
 mdp:/usr/include/bits/types/locale_t.h
 mdp:/usr/include/bits/types/mbstate_t.h
 mdp:/usr/include/bits/types/sigset_t.h
 mdp:/usr/include/bits/types/struct_FILE.h
 mdp:/usr/include/bits/types/struct___jmp_buf_tag.h
 mdp:/usr/include/bits/types/struct_itimerspec.h
 mdp:/usr/include/bits/types/struct_sched_param.h
 mdp:/usr/include/bits/types/struct_timespec.h
 mdp:/usr/include/bits/types/struct_timeval.h
 mdp:/usr/include/bits/types/struct_tm.h
 mdp:/usr/include/bits/types/time_t.h
 mdp:/usr/include/bits/types/timer_t.h
 mdp:/usr/include/bits/types/wint_t.h
 mdp:/usr/include/bits/typesizes.h
 mdp:/usr/include/bits/uintn-identity.h
 mdp:/usr/include/bits/uio_lim.h
 mdp:/usr/include/bits/unistd_ext.h
 mdp:/usr/include/bits/waitflags.h
 mdp:/usr/include/bits/waitstatus.h
 mdp:/usr/include/bits/wchar.h
 mdp:/usr/include/bits/wctype-wchar.h
 mdp:/usr/include/bits/wordsize.h
 mdp:/usr/include/bits/xopen_lim.h
 mdp:/usr/include/c++/15/algorithm
 mdp:/usr/include/c++/15/array
 mdp:/usr/include/c++/15/atomic
 mdp:/usr/include/c++/15/backward/auto_ptr.h
 mdp:/usr/include/c++/15/backward/binders.h
 mdp:/usr/include/c++/15/bit
 mdp:/usr/include/c++/15/bits/algorithmfwd.h
 mdp:/usr/include/c++/15/bits/align.h
 mdp:/usr/include/c++/15/bits/alloc_traits.h
 mdp:/usr/include/c++/15/bits/allocated_ptr.h
 mdp:/usr/include/c++/15/bits/allocator.h
 mdp:/usr/include/c++/15/bits/atomic_base.h
 mdp:/usr/include/c++/15/bits/atomic_lockfree_defines.h
 mdp:/usr/include/c++/15/bits/atomic_wait.h
 mdp:/usr/include/c++/15/bits/basic_ios.h
 mdp:/usr/include/c++/15/bits/basic_ios.tcc
 mdp:/usr/include/c++/15/bits/basic_string.h
 mdp:/usr/include/c++/15/bits/basic_string.tcc
 mdp:/usr/include/c++/15/bits/char_traits.h
 mdp:/usr/include/c++/15/bits/charconv.h
 mdp:/usr/include/c++/15/bits/chrono.h
 mdp:/usr/include/c++/15/bits/chrono_io.h
 mdp:/usr/include/c++/15/bits/codecvt.h
 mdp:/usr/include/c++/15/bits/concept_check.h
 mdp:/usr/include/c++/15/bits/cpp_type_traits.h
 mdp:/usr/include/c++/15/bits/cxxabi_forced.h
 mdp:/usr/include/c++/15/bits/cxxabi_init_exception.h
 mdp:/usr/include/c++/15/bits/enable_special_members.h
 mdp:/usr/include/c++/15/bits/erase_if.h
 mdp:/usr/include/c++/15/bits/exception.h
 mdp:/usr/include/c++/15/bits/exception_defines.h
 mdp:/usr/include/c++/15/bits/exception_ptr.h
 mdp:/usr/include/c++/15/bits/functexcept.h
 mdp:/usr/include/c++/15/bits/functional_hash.h
 mdp:/usr/include/c++/15/bits/hash_bytes.h
 mdp:/usr/include/c++/15/bits/hashtable.h
 mdp:/usr/include/c++/15/bits/hashtable_policy.h
 mdp:/usr/include/c++/15/bits/invoke.h
 mdp:/usr/include/c++/15/bits/ios_base.h
 mdp:/usr/include/c++/15/bits/istream.tcc
 mdp:/usr/include/c++/15/bits/iterator_concepts.h
 mdp:/usr/include/c++/15/bits/list.tcc
 mdp:/usr/include/c++/15/bits/locale_classes.h
 mdp:/usr/include/c++/15/bits/locale_classes.tcc
 mdp:/usr/include/c++/15/bits/locale_conv.h
 mdp:/usr/include/c++/15/bits/locale_facets.h
 mdp:/usr/include/c++/15/bits/locale_facets.tcc
 mdp:/usr/include/c++/15/bits/locale_facets_nonio.h
 mdp:/usr/include/c++/15/bits/locale_facets_nonio.tcc
 mdp:/usr/include/c++/15/bits/localefwd.h
 mdp:/usr/include/c++/15/bits/max_size_type.h
 mdp:/usr/include/c++/15/bits/memory_resource.h
 mdp:/usr/include/c++/15/bits/memoryfwd.h
 mdp:/usr/include/c++/15/bits/move.h
 mdp:/usr/include/c++/15/bits/nested_exception.h
 mdp:/usr/include/c++/15/bits/new_allocator.h
 mdp:/usr/include/c++/15/bits/node_handle.h
 mdp:/usr/include/c++/15/bits/ostream.h
 mdp:/usr/include/c++/15/bits/ostream.tcc
 mdp:/usr/include/c++/15/bits/ostream_insert.h
 mdp:/usr/include/c++/15/bits/parse_numbers.h
 mdp:/usr/include/c++/15/bits/postypes.h
 mdp:/usr/include/c++/15/bits/predefined_ops.h
 mdp:/usr/include/c++/15/bits/ptr_traits.h
 mdp:/usr/include/c++/15/bits/quoted_string.h
 mdp:/usr/include/c++/15/bits/range_access.h
 mdp:/usr/include/c++/15/bits/ranges_algo.h
 mdp:/usr/include/c++/15/bits/ranges_algobase.h
 mdp:/usr/include/c++/15/bits/ranges_base.h
 mdp:/usr/include/c++/15/bits/ranges_cmp.h
 mdp:/usr/include/c++/15/bits/ranges_uninitialized.h
 mdp:/usr/include/c++/15/bits/ranges_util.h
 mdp:/usr/include/c++/15/bits/refwrap.h
 mdp:/usr/include/c++/15/bits/requires_hosted.h
 mdp:/usr/include/c++/15/bits/shared_ptr.h
 mdp:/usr/include/c++/15/bits/shared_ptr_atomic.h
 mdp:/usr/include/c++/15/bits/shared_ptr_base.h
 mdp:/usr/include/c++/15/bits/specfun.h
 mdp:/usr/include/c++/15/bits/sstream.tcc
 mdp:/usr/include/c++/15/bits/std_abs.h
 mdp:/usr/include/c++/15/bits/std_function.h
 mdp:/usr/include/c++/15/bits/std_mutex.h
 mdp:/usr/include/c++/15/bits/stl_algo.h
 mdp:/usr/include/c++/15/bits/stl_algobase.h
 mdp:/usr/include/c++/15/bits/stl_bvector.h
 mdp:/usr/include/c++/15/bits/stl_construct.h
 mdp:/usr/include/c++/15/bits/stl_function.h
 mdp:/usr/include/c++/15/bits/stl_heap.h
 mdp:/usr/include/c++/15/bits/stl_iterator.h
 mdp:/usr/include/c++/15/bits/stl_iterator_base_funcs.h
 mdp:/usr/include/c++/15/bits/stl_iterator_base_types.h
 mdp:/usr/include/c++/15/bits/stl_list.h
 mdp:/usr/include/c++/15/bits/stl_map.h
 mdp:/usr/include/c++/15/bits/stl_multimap.h
 mdp:/usr/include/c++/15/bits/stl_multiset.h
 mdp:/usr/include/c++/15/bits/stl_numeric.h
 mdp:/usr/include/c++/15/bits/stl_pair.h
 mdp:/usr/include/c++/15/bits/stl_raw_storage_iter.h
 mdp:/usr/include/c++/15/bits/stl_relops.h
 mdp:/usr/include/c++/15/bits/stl_set.h
 mdp:/usr/include/c++/15/bits/stl_tempbuf.h
 mdp:/usr/include/c++/15/bits/stl_tree.h
 mdp:/usr/include/c++/15/bits/stl_uninitialized.h
 mdp:/usr/include/c++/15/bits/stl_vector.h
 mdp:/usr/include/c++/15/bits/stream_iterator.h
 mdp:/usr/include/c++/15/bits/streambuf.tcc
 mdp:/usr/include/c++/15/bits/streambuf_iterator.h
 mdp:/usr/include/c++/15/bits/string_view.tcc
 mdp:/usr/include/c++/15/bits/stringfwd.h
 mdp:/usr/include/c++/15/bits/uniform_int_dist.h
 mdp:/usr/include/c++/15/bits/unique_ptr.h
 mdp:/usr/include/c++/15/bits/unordered_map.h
 mdp:/usr/include/c++/15/bits/unordered_set.h
 mdp:/usr/include/c++/15/bits/uses_allocator.h
 mdp:/usr/include/c++/15/bits/uses_allocator_args.h
 mdp:/usr/include/c++/15/bits/utility.h
 mdp:/usr/include/c++/15/bits/vector.tcc
 mdp:/usr/include/c++/15/bits/version.h
 mdp:/usr/include/c++/15/cassert
 mdp:/usr/include/c++/15/cctype
 mdp:/usr/include/c++/15/cerrno
 mdp:/usr/include/c++/15/charconv
 mdp:/usr/include/c++/15/chrono
 mdp:/usr/include/c++/15/climits
 mdp:/usr/include/c++/15/clocale
 mdp:/usr/include/c++/15/cmath
 mdp:/usr/include/c++/15/compare
 mdp:/usr/include/c++/15/concepts
 mdp:/usr/include/c++/15/cstddef
 mdp:/usr/include/c++/15/cstdint
 mdp:/usr/include/c++/15/cstdio
 mdp:/usr/include/c++/15/cstdlib
 mdp:/usr/include/c++/15/cstring
 mdp:/usr/include/c++/15/ctime
 mdp:/usr/include/c++/15/cwchar
 mdp:/usr/include/c++/15/cwctype
 mdp:/usr/include/c++/15/debug/assertions.h
 mdp:/usr/include/c++/15/debug/debug.h
 mdp:/usr/include/c++/15/exception
 mdp:/usr/include/c++/15/ext/aligned_buffer.h
 mdp:/usr/include/c++/15/ext/alloc_traits.h
 mdp:/usr/include/c++/15/ext/atomicity.h
 mdp:/usr/include/c++/15/ext/concurrence.h
 mdp:/usr/include/c++/15/ext/numeric_traits.h
 mdp:/usr/include/c++/15/ext/string_conversions.h
 mdp:/usr/include/c++/15/ext/type_traits.h
 mdp:/usr/include/c++/15/format
 mdp:/usr/include/c++/15/functional
 mdp:/usr/include/c++/15/initializer_list
 mdp:/usr/include/c++/15/iomanip
 mdp:/usr/include/c++/15/ios
 mdp:/usr/include/c++/15/iosfwd
 mdp:/usr/include/c++/15/istream
 mdp:/usr/include/c++/15/iterator
 mdp:/usr/include/c++/15/limits
 mdp:/usr/include/c++/15/list
 mdp:/usr/include/c++/15/locale
 mdp:/usr/include/c++/15/map
 mdp:/usr/include/c++/15/memory
 mdp:/usr/include/c++/15/new
 mdp:/usr/include/c++/15/numeric
 mdp:/usr/include/c++/15/optional
 mdp:/usr/include/c++/15/ostream
 mdp:/usr/include/c++/15/pstl/execution_defs.h
 mdp:/usr/include/c++/15/pstl/glue_numeric_defs.h
 mdp:/usr/include/c++/15/ratio
 mdp:/usr/include/c++/15/set
 mdp:/usr/include/c++/15/sstream
 mdp:/usr/include/c++/15/stdexcept
 mdp:/usr/include/c++/15/streambuf
 mdp:/usr/include/c++/15/string
 mdp:/usr/include/c++/15/string_view
 mdp:/usr/include/c++/15/system_error
 mdp:/usr/include/c++/15/tr1/bessel_function.tcc
 mdp:/usr/include/c++/15/tr1/beta_function.tcc
 mdp:/usr/include/c++/15/tr1/ell_integral.tcc
 mdp:/usr/include/c++/15/tr1/exp_integral.tcc
 mdp:/usr/include/c++/15/tr1/gamma.tcc
 mdp:/usr/include/c++/15/tr1/hypergeometric.tcc
 mdp:/usr/include/c++/15/tr1/legendre_function.tcc
 mdp:/usr/include/c++/15/tr1/modified_bessel_func.tcc
 mdp:/usr/include/c++/15/tr1/poly_hermite.tcc
 mdp:/usr/include/c++/15/tr1/poly_laguerre.tcc
 mdp:/usr/include/c++/15/tr1/riemann_zeta.tcc
 mdp:/usr/include/c++/15/tr1/special_function_util.h
 mdp:/usr/include/c++/15/tuple
 mdp:/usr/include/c++/15/type_traits
 mdp:/usr/include/c++/15/typeinfo
 mdp:/usr/include/c++/15/unordered_map
 mdp:/usr/include/c++/15/unordered_set
 mdp:/usr/include/c++/15/utility
 mdp:/usr/include/c++/15/variant
 mdp:/usr/include/c++/15/vector
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/atomic_word.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/c++allocator.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/c++config.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/c++locale.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/cpu_defines.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/ctype_base.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/ctype_inline.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/error_constants.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/gthr-default.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/gthr.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/messages_members.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/os_defines.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/time_members.h
 mdp:/usr/include/ctype.h
 mdp:/usr/include/endian.h
 mdp:/usr/include/errno.h
 mdp:/usr/include/features-time64.h
 mdp:/usr/include/features.h
 mdp:/usr/include/gnu/stubs-64.h
 mdp:/usr/include/gnu/stubs.h
 mdp:/usr/include/libintl.h
 mdp:/usr/include/limits.h
 mdp:/usr/include/linux/errno.h
 mdp:/usr/include/linux/limits.h
 mdp:/usr/include/linux/posix_types.h
 mdp:/usr/include/linux/stddef.h
 mdp:/usr/include/linux/types.h
 mdp:/usr/include/locale.h
 mdp:/usr/include/pthread.h
 mdp:/usr/include/qt6/QtCore/QTimer
 mdp:/usr/include/qt6/QtCore/q17memory.h
 mdp:/usr/include/qt6/QtCore/q20functional.h
 mdp:/usr/include/qt6/QtCore/q20iterator.h
 mdp:/usr/include/qt6/QtCore/q20memory.h
 mdp:/usr/include/qt6/QtCore/q20type_traits.h
 mdp:/usr/include/qt6/QtCore/q20utility.h
 mdp:/usr/include/qt6/QtCore/q23utility.h
 mdp:/usr/include/qt6/QtCore/qabstracteventdispatcher.h
 mdp:/usr/include/qt6/QtCore/qabstractitemmodel.h
 mdp:/usr/include/qt6/QtCore/qalgorithms.h
 mdp:/usr/include/qt6/QtCore/qanystringview.h
 mdp:/usr/include/qt6/QtCore/qarraydata.h
 mdp:/usr/include/qt6/QtCore/qarraydataops.h
 mdp:/usr/include/qt6/QtCore/qarraydatapointer.h
 mdp:/usr/include/qt6/QtCore/qassert.h
 mdp:/usr/include/qt6/QtCore/qatomic.h
 mdp:/usr/include/qt6/QtCore/qatomic_cxx11.h
 mdp:/usr/include/qt6/QtCore/qbasicatomic.h
 mdp:/usr/include/qt6/QtCore/qbasictimer.h
 mdp:/usr/include/qt6/QtCore/qbindingstorage.h
 mdp:/usr/include/qt6/QtCore/qbytearray.h
 mdp:/usr/include/qt6/QtCore/qbytearrayalgorithms.h
 mdp:/usr/include/qt6/QtCore/qbytearraylist.h
 mdp:/usr/include/qt6/QtCore/qbytearrayview.h
 mdp:/usr/include/qt6/QtCore/qchar.h
 mdp:/usr/include/qt6/QtCore/qcompare.h
 mdp:/usr/include/qt6/QtCore/qcompare_impl.h
 mdp:/usr/include/qt6/QtCore/qcomparehelpers.h
 mdp:/usr/include/qt6/QtCore/qcompilerdetection.h
 mdp:/usr/include/qt6/QtCore/qconfig-64.h
 mdp:/usr/include/qt6/QtCore/qconfig.h
 mdp:/usr/include/qt6/QtCore/qconstructormacros.h
 mdp:/usr/include/qt6/QtCore/qcontainerfwd.h
 mdp:/usr/include/qt6/QtCore/qcontainerinfo.h
 mdp:/usr/include/qt6/QtCore/qcontainertools_impl.h
 mdp:/usr/include/qt6/QtCore/qcontiguouscache.h
 mdp:/usr/include/qt6/QtCore/qdarwinhelpers.h
 mdp:/usr/include/qt6/QtCore/qdatastream.h
 mdp:/usr/include/qt6/QtCore/qdeadlinetimer.h
 mdp:/usr/include/qt6/QtCore/qdebug.h
 mdp:/usr/include/qt6/QtCore/qelapsedtimer.h
 mdp:/usr/include/qt6/QtCore/qendian.h
 mdp:/usr/include/qt6/QtCore/qeventloop.h
 mdp:/usr/include/qt6/QtCore/qexceptionhandling.h
 mdp:/usr/include/qt6/QtCore/qflags.h
 mdp:/usr/include/qt6/QtCore/qfloat16.h
 mdp:/usr/include/qt6/QtCore/qforeach.h
 mdp:/usr/include/qt6/QtCore/qfunctionaltools_impl.h
 mdp:/usr/include/qt6/QtCore/qfunctionpointer.h
 mdp:/usr/include/qt6/QtCore/qgenericatomic.h
 mdp:/usr/include/qt6/QtCore/qglobal.h
 mdp:/usr/include/qt6/QtCore/qglobalstatic.h
 mdp:/usr/include/qt6/QtCore/qhash.h
 mdp:/usr/include/qt6/QtCore/qhashfunctions.h
 mdp:/usr/include/qt6/QtCore/qiodevicebase.h
 mdp:/usr/include/qt6/QtCore/qitemselectionmodel.h
 mdp:/usr/include/qt6/QtCore/qiterable.h
 mdp:/usr/include/qt6/QtCore/qiterator.h
 mdp:/usr/include/qt6/QtCore/qlatin1stringview.h
 mdp:/usr/include/qt6/QtCore/qline.h
 mdp:/usr/include/qt6/QtCore/qlist.h
 mdp:/usr/include/qt6/QtCore/qlocale.h
 mdp:/usr/include/qt6/QtCore/qlogging.h
 mdp:/usr/include/qt6/QtCore/qmalloc.h
 mdp:/usr/include/qt6/QtCore/qmap.h
 mdp:/usr/include/qt6/QtCore/qmargins.h
 mdp:/usr/include/qt6/QtCore/qmath.h
 mdp:/usr/include/qt6/QtCore/qmetacontainer.h
 mdp:/usr/include/qt6/QtCore/qmetatype.h
 mdp:/usr/include/qt6/QtCore/qminmax.h
 mdp:/usr/include/qt6/QtCore/qnamespace.h
 mdp:/usr/include/qt6/QtCore/qnumeric.h
 mdp:/usr/include/qt6/QtCore/qobject.h
 mdp:/usr/include/qt6/QtCore/qobject_impl.h
 mdp:/usr/include/qt6/QtCore/qobjectdefs.h
 mdp:/usr/include/qt6/QtCore/qobjectdefs_impl.h
 mdp:/usr/include/qt6/QtCore/qoverload.h
 mdp:/usr/include/qt6/QtCore/qpair.h
 mdp:/usr/include/qt6/QtCore/qpoint.h
 mdp:/usr/include/qt6/QtCore/qprocessordetection.h
 mdp:/usr/include/qt6/QtCore/qrect.h
 mdp:/usr/include/qt6/QtCore/qrefcount.h
 mdp:/usr/include/qt6/QtCore/qregularexpression.h
 mdp:/usr/include/qt6/QtCore/qscopedpointer.h
 mdp:/usr/include/qt6/QtCore/qscopeguard.h
 mdp:/usr/include/qt6/QtCore/qset.h
 mdp:/usr/include/qt6/QtCore/qshareddata.h
 mdp:/usr/include/qt6/QtCore/qshareddata_impl.h
 mdp:/usr/include/qt6/QtCore/qsharedpointer.h
 mdp:/usr/include/qt6/QtCore/qsharedpointer_impl.h
 mdp:/usr/include/qt6/QtCore/qsize.h
 mdp:/usr/include/qt6/QtCore/qspan.h
 mdp:/usr/include/qt6/QtCore/qstdlibdetection.h
 mdp:/usr/include/qt6/QtCore/qstring.h
 mdp:/usr/include/qt6/QtCore/qstringalgorithms.h
 mdp:/usr/include/qt6/QtCore/qstringbuilder.h
 mdp:/usr/include/qt6/QtCore/qstringconverter.h
 mdp:/usr/include/qt6/QtCore/qstringconverter_base.h
 mdp:/usr/include/qt6/QtCore/qstringfwd.h
 mdp:/usr/include/qt6/QtCore/qstringlist.h
 mdp:/usr/include/qt6/QtCore/qstringliteral.h
 mdp:/usr/include/qt6/QtCore/qstringmatcher.h
 mdp:/usr/include/qt6/QtCore/qstringtokenizer.h
 mdp:/usr/include/qt6/QtCore/qstringview.h
 mdp:/usr/include/qt6/QtCore/qswap.h
 mdp:/usr/include/qt6/QtCore/qsysinfo.h
 mdp:/usr/include/qt6/QtCore/qsystemdetection.h
 mdp:/usr/include/qt6/QtCore/qtaggedpointer.h
 mdp:/usr/include/qt6/QtCore/qtclasshelpermacros.h
 mdp:/usr/include/qt6/QtCore/qtconfiginclude.h
 mdp:/usr/include/qt6/QtCore/qtconfigmacros.h
 mdp:/usr/include/qt6/QtCore/qtcore-config.h
 mdp:/usr/include/qt6/QtCore/qtcoreexports.h
 mdp:/usr/include/qt6/QtCore/qtcoreglobal.h
 mdp:/usr/include/qt6/QtCore/qtdeprecationdefinitions.h
 mdp:/usr/include/qt6/QtCore/qtdeprecationmarkers.h
 mdp:/usr/include/qt6/QtCore/qtenvironmentvariables.h
 mdp:/usr/include/qt6/QtCore/qtextstream.h
 mdp:/usr/include/qt6/QtCore/qtformat_impl.h
 mdp:/usr/include/qt6/QtCore/qtimer.h
 mdp:/usr/include/qt6/QtCore/qtmetamacros.h
 mdp:/usr/include/qt6/QtCore/qtnoop.h
 mdp:/usr/include/qt6/QtCore/qtpreprocessorsupport.h
 mdp:/usr/include/qt6/QtCore/qtresource.h
 mdp:/usr/include/qt6/QtCore/qttranslation.h
 mdp:/usr/include/qt6/QtCore/qttypetraits.h
 mdp:/usr/include/qt6/QtCore/qtversion.h
 mdp:/usr/include/qt6/QtCore/qtversionchecks.h
 mdp:/usr/include/qt6/QtCore/qtypeinfo.h
 mdp:/usr/include/qt6/QtCore/qtypes.h
 mdp:/usr/include/qt6/QtCore/qutf8stringview.h
 mdp:/usr/include/qt6/QtCore/qvariant.h
 mdp:/usr/include/qt6/QtCore/qvarlengtharray.h
 mdp:/usr/include/qt6/QtCore/qversiontagging.h
 mdp:/usr/include/qt6/QtCore/qxptype_traits.h
 mdp:/usr/include/qt6/QtCore/qyieldcpu.h
 mdp:/usr/include/qt6/QtGui/qaction.h
 mdp:/usr/include/qt6/QtGui/qbitmap.h
 mdp:/usr/include/qt6/QtGui/qbrush.h
 mdp:/usr/include/qt6/QtGui/qcolor.h
 mdp:/usr/include/qt6/QtGui/qcursor.h
 mdp:/usr/include/qt6/QtGui/qfont.h
 mdp:/usr/include/qt6/QtGui/qfontinfo.h
 mdp:/usr/include/qt6/QtGui/qfontmetrics.h
 mdp:/usr/include/qt6/QtGui/qfontvariableaxis.h
 mdp:/usr/include/qt6/QtGui/qicon.h
 mdp:/usr/include/qt6/QtGui/qimage.h
 mdp:/usr/include/qt6/QtGui/qkeysequence.h
 mdp:/usr/include/qt6/QtGui/qpaintdevice.h
 mdp:/usr/include/qt6/QtGui/qpalette.h
 mdp:/usr/include/qt6/QtGui/qpixelformat.h
 mdp:/usr/include/qt6/QtGui/qpixmap.h
 mdp:/usr/include/qt6/QtGui/qpolygon.h
 mdp:/usr/include/qt6/QtGui/qregion.h
 mdp:/usr/include/qt6/QtGui/qrgb.h
 mdp:/usr/include/qt6/QtGui/qrgba64.h
 mdp:/usr/include/qt6/QtGui/qtgui-config.h
 mdp:/usr/include/qt6/QtGui/qtguiexports.h
 mdp:/usr/include/qt6/QtGui/qtguiglobal.h
 mdp:/usr/include/qt6/QtGui/qtransform.h
 mdp:/usr/include/qt6/QtGui/qvalidator.h
 mdp:/usr/include/qt6/QtGui/qwindowdefs.h
 mdp:/usr/include/qt6/QtWidgets/QListWidgetItem
 mdp:/usr/include/qt6/QtWidgets/QMainWindow
 mdp:/usr/include/qt6/QtWidgets/qabstractitemdelegate.h
 mdp:/usr/include/qt6/QtWidgets/qabstractitemview.h
 mdp:/usr/include/qt6/QtWidgets/qabstractscrollarea.h
 mdp:/usr/include/qt6/QtWidgets/qabstractslider.h
 mdp:/usr/include/qt6/QtWidgets/qabstractspinbox.h
 mdp:/usr/include/qt6/QtWidgets/qframe.h
 mdp:/usr/include/qt6/QtWidgets/qlistview.h
 mdp:/usr/include/qt6/QtWidgets/qlistwidget.h
 mdp:/usr/include/qt6/QtWidgets/qmainwindow.h
 mdp:/usr/include/qt6/QtWidgets/qrubberband.h
 mdp:/usr/include/qt6/QtWidgets/qsizepolicy.h
 mdp:/usr/include/qt6/QtWidgets/qslider.h
 mdp:/usr/include/qt6/QtWidgets/qstyle.h
 mdp:/usr/include/qt6/QtWidgets/qstyleoption.h
 mdp:/usr/include/qt6/QtWidgets/qtabbar.h
 mdp:/usr/include/qt6/QtWidgets/qtabwidget.h
 mdp:/usr/include/qt6/QtWidgets/qtwidgets-config.h
 mdp:/usr/include/qt6/QtWidgets/qtwidgetsexports.h
 mdp:/usr/include/qt6/QtWidgets/qtwidgetsglobal.h
 mdp:/usr/include/qt6/QtWidgets/qwidget.h
 mdp:/usr/include/sched.h
 mdp:/usr/include/stdc-predef.h
 mdp:/usr/include/stdio.h
 mdp:/usr/include/stdlib.h
 mdp:/usr/include/string.h
 mdp:/usr/include/strings.h
 mdp:/usr/include/sys/cdefs.h
 mdp:/usr/include/sys/select.h
 mdp:/usr/include/sys/syscall.h
 mdp:/usr/include/sys/types.h
 mdp:/usr/include/syscall.h
 mdp:/usr/include/time.h
 mdp:/usr/include/unistd.h
 mdp:/usr/include/wchar.h
 mdp:/usr/include/wctype.h
 mdp:/usr/lib/gcc/x86_64-redhat-linux/15/include/stdarg.h
 mdp:/usr/lib/gcc/x86_64-redhat-linux/15/include/stdbool.h
 mdp:/usr/lib/gcc/x86_64-redhat-linux/15/include/stddef.h
/home/<USER>/CLionProjects/KNoteDo/app/RichTextEditor.h
 mmc:Q_OBJECT
 mdp:/home/<USER>/CLionProjects/KNoteDo/app/RichTextEditor.h
 mdp:/home/<USER>/CLionProjects/KNoteDo/build/KNoteDo_autogen/moc_predefs.h
 mdp:/usr/include/alloca.h
 mdp:/usr/include/asm-generic/bitsperlong.h
 mdp:/usr/include/asm-generic/errno-base.h
 mdp:/usr/include/asm-generic/errno.h
 mdp:/usr/include/asm-generic/int-ll64.h
 mdp:/usr/include/asm-generic/posix_types.h
 mdp:/usr/include/asm-generic/types.h
 mdp:/usr/include/asm/bitsperlong.h
 mdp:/usr/include/asm/errno.h
 mdp:/usr/include/asm/posix_types.h
 mdp:/usr/include/asm/posix_types_64.h
 mdp:/usr/include/asm/types.h
 mdp:/usr/include/asm/unistd.h
 mdp:/usr/include/asm/unistd_64.h
 mdp:/usr/include/assert.h
 mdp:/usr/include/bits/atomic_wide_counter.h
 mdp:/usr/include/bits/byteswap.h
 mdp:/usr/include/bits/confname.h
 mdp:/usr/include/bits/cpu-set.h
 mdp:/usr/include/bits/endian.h
 mdp:/usr/include/bits/endianness.h
 mdp:/usr/include/bits/environments.h
 mdp:/usr/include/bits/errno.h
 mdp:/usr/include/bits/floatn-common.h
 mdp:/usr/include/bits/floatn.h
 mdp:/usr/include/bits/getopt_core.h
 mdp:/usr/include/bits/getopt_posix.h
 mdp:/usr/include/bits/libc-header-start.h
 mdp:/usr/include/bits/local_lim.h
 mdp:/usr/include/bits/locale.h
 mdp:/usr/include/bits/long-double.h
 mdp:/usr/include/bits/posix1_lim.h
 mdp:/usr/include/bits/posix2_lim.h
 mdp:/usr/include/bits/posix_opt.h
 mdp:/usr/include/bits/pthread_stack_min-dynamic.h
 mdp:/usr/include/bits/pthreadtypes-arch.h
 mdp:/usr/include/bits/pthreadtypes.h
 mdp:/usr/include/bits/sched.h
 mdp:/usr/include/bits/select.h
 mdp:/usr/include/bits/setjmp.h
 mdp:/usr/include/bits/stdint-intn.h
 mdp:/usr/include/bits/stdio_lim.h
 mdp:/usr/include/bits/stdlib-float.h
 mdp:/usr/include/bits/struct_mutex.h
 mdp:/usr/include/bits/struct_rwlock.h
 mdp:/usr/include/bits/syscall.h
 mdp:/usr/include/bits/thread-shared-types.h
 mdp:/usr/include/bits/time.h
 mdp:/usr/include/bits/time64.h
 mdp:/usr/include/bits/timesize.h
 mdp:/usr/include/bits/timex.h
 mdp:/usr/include/bits/types.h
 mdp:/usr/include/bits/types/FILE.h
 mdp:/usr/include/bits/types/__FILE.h
 mdp:/usr/include/bits/types/__fpos64_t.h
 mdp:/usr/include/bits/types/__fpos_t.h
 mdp:/usr/include/bits/types/__locale_t.h
 mdp:/usr/include/bits/types/__mbstate_t.h
 mdp:/usr/include/bits/types/__sigset_t.h
 mdp:/usr/include/bits/types/clock_t.h
 mdp:/usr/include/bits/types/clockid_t.h
 mdp:/usr/include/bits/types/cookie_io_functions_t.h
 mdp:/usr/include/bits/types/error_t.h
 mdp:/usr/include/bits/types/locale_t.h
 mdp:/usr/include/bits/types/mbstate_t.h
 mdp:/usr/include/bits/types/sigset_t.h
 mdp:/usr/include/bits/types/struct_FILE.h
 mdp:/usr/include/bits/types/struct___jmp_buf_tag.h
 mdp:/usr/include/bits/types/struct_itimerspec.h
 mdp:/usr/include/bits/types/struct_sched_param.h
 mdp:/usr/include/bits/types/struct_timespec.h
 mdp:/usr/include/bits/types/struct_timeval.h
 mdp:/usr/include/bits/types/struct_tm.h
 mdp:/usr/include/bits/types/time_t.h
 mdp:/usr/include/bits/types/timer_t.h
 mdp:/usr/include/bits/types/wint_t.h
 mdp:/usr/include/bits/typesizes.h
 mdp:/usr/include/bits/uintn-identity.h
 mdp:/usr/include/bits/uio_lim.h
 mdp:/usr/include/bits/unistd_ext.h
 mdp:/usr/include/bits/waitflags.h
 mdp:/usr/include/bits/waitstatus.h
 mdp:/usr/include/bits/wchar.h
 mdp:/usr/include/bits/wctype-wchar.h
 mdp:/usr/include/bits/wordsize.h
 mdp:/usr/include/bits/xopen_lim.h
 mdp:/usr/include/c++/15/algorithm
 mdp:/usr/include/c++/15/array
 mdp:/usr/include/c++/15/atomic
 mdp:/usr/include/c++/15/backward/auto_ptr.h
 mdp:/usr/include/c++/15/backward/binders.h
 mdp:/usr/include/c++/15/bit
 mdp:/usr/include/c++/15/bits/algorithmfwd.h
 mdp:/usr/include/c++/15/bits/align.h
 mdp:/usr/include/c++/15/bits/alloc_traits.h
 mdp:/usr/include/c++/15/bits/allocated_ptr.h
 mdp:/usr/include/c++/15/bits/allocator.h
 mdp:/usr/include/c++/15/bits/atomic_base.h
 mdp:/usr/include/c++/15/bits/atomic_lockfree_defines.h
 mdp:/usr/include/c++/15/bits/atomic_wait.h
 mdp:/usr/include/c++/15/bits/basic_ios.h
 mdp:/usr/include/c++/15/bits/basic_ios.tcc
 mdp:/usr/include/c++/15/bits/basic_string.h
 mdp:/usr/include/c++/15/bits/basic_string.tcc
 mdp:/usr/include/c++/15/bits/char_traits.h
 mdp:/usr/include/c++/15/bits/charconv.h
 mdp:/usr/include/c++/15/bits/chrono.h
 mdp:/usr/include/c++/15/bits/chrono_io.h
 mdp:/usr/include/c++/15/bits/codecvt.h
 mdp:/usr/include/c++/15/bits/concept_check.h
 mdp:/usr/include/c++/15/bits/cpp_type_traits.h
 mdp:/usr/include/c++/15/bits/cxxabi_forced.h
 mdp:/usr/include/c++/15/bits/cxxabi_init_exception.h
 mdp:/usr/include/c++/15/bits/enable_special_members.h
 mdp:/usr/include/c++/15/bits/erase_if.h
 mdp:/usr/include/c++/15/bits/exception.h
 mdp:/usr/include/c++/15/bits/exception_defines.h
 mdp:/usr/include/c++/15/bits/exception_ptr.h
 mdp:/usr/include/c++/15/bits/functexcept.h
 mdp:/usr/include/c++/15/bits/functional_hash.h
 mdp:/usr/include/c++/15/bits/hash_bytes.h
 mdp:/usr/include/c++/15/bits/hashtable.h
 mdp:/usr/include/c++/15/bits/hashtable_policy.h
 mdp:/usr/include/c++/15/bits/invoke.h
 mdp:/usr/include/c++/15/bits/ios_base.h
 mdp:/usr/include/c++/15/bits/istream.tcc
 mdp:/usr/include/c++/15/bits/iterator_concepts.h
 mdp:/usr/include/c++/15/bits/list.tcc
 mdp:/usr/include/c++/15/bits/locale_classes.h
 mdp:/usr/include/c++/15/bits/locale_classes.tcc
 mdp:/usr/include/c++/15/bits/locale_conv.h
 mdp:/usr/include/c++/15/bits/locale_facets.h
 mdp:/usr/include/c++/15/bits/locale_facets.tcc
 mdp:/usr/include/c++/15/bits/locale_facets_nonio.h
 mdp:/usr/include/c++/15/bits/locale_facets_nonio.tcc
 mdp:/usr/include/c++/15/bits/localefwd.h
 mdp:/usr/include/c++/15/bits/max_size_type.h
 mdp:/usr/include/c++/15/bits/memory_resource.h
 mdp:/usr/include/c++/15/bits/memoryfwd.h
 mdp:/usr/include/c++/15/bits/move.h
 mdp:/usr/include/c++/15/bits/nested_exception.h
 mdp:/usr/include/c++/15/bits/new_allocator.h
 mdp:/usr/include/c++/15/bits/node_handle.h
 mdp:/usr/include/c++/15/bits/ostream.h
 mdp:/usr/include/c++/15/bits/ostream.tcc
 mdp:/usr/include/c++/15/bits/ostream_insert.h
 mdp:/usr/include/c++/15/bits/parse_numbers.h
 mdp:/usr/include/c++/15/bits/postypes.h
 mdp:/usr/include/c++/15/bits/predefined_ops.h
 mdp:/usr/include/c++/15/bits/ptr_traits.h
 mdp:/usr/include/c++/15/bits/quoted_string.h
 mdp:/usr/include/c++/15/bits/range_access.h
 mdp:/usr/include/c++/15/bits/ranges_algo.h
 mdp:/usr/include/c++/15/bits/ranges_algobase.h
 mdp:/usr/include/c++/15/bits/ranges_base.h
 mdp:/usr/include/c++/15/bits/ranges_cmp.h
 mdp:/usr/include/c++/15/bits/ranges_uninitialized.h
 mdp:/usr/include/c++/15/bits/ranges_util.h
 mdp:/usr/include/c++/15/bits/refwrap.h
 mdp:/usr/include/c++/15/bits/requires_hosted.h
 mdp:/usr/include/c++/15/bits/shared_ptr.h
 mdp:/usr/include/c++/15/bits/shared_ptr_atomic.h
 mdp:/usr/include/c++/15/bits/shared_ptr_base.h
 mdp:/usr/include/c++/15/bits/specfun.h
 mdp:/usr/include/c++/15/bits/sstream.tcc
 mdp:/usr/include/c++/15/bits/std_abs.h
 mdp:/usr/include/c++/15/bits/std_function.h
 mdp:/usr/include/c++/15/bits/std_mutex.h
 mdp:/usr/include/c++/15/bits/stl_algo.h
 mdp:/usr/include/c++/15/bits/stl_algobase.h
 mdp:/usr/include/c++/15/bits/stl_bvector.h
 mdp:/usr/include/c++/15/bits/stl_construct.h
 mdp:/usr/include/c++/15/bits/stl_function.h
 mdp:/usr/include/c++/15/bits/stl_heap.h
 mdp:/usr/include/c++/15/bits/stl_iterator.h
 mdp:/usr/include/c++/15/bits/stl_iterator_base_funcs.h
 mdp:/usr/include/c++/15/bits/stl_iterator_base_types.h
 mdp:/usr/include/c++/15/bits/stl_list.h
 mdp:/usr/include/c++/15/bits/stl_map.h
 mdp:/usr/include/c++/15/bits/stl_multimap.h
 mdp:/usr/include/c++/15/bits/stl_multiset.h
 mdp:/usr/include/c++/15/bits/stl_numeric.h
 mdp:/usr/include/c++/15/bits/stl_pair.h
 mdp:/usr/include/c++/15/bits/stl_raw_storage_iter.h
 mdp:/usr/include/c++/15/bits/stl_relops.h
 mdp:/usr/include/c++/15/bits/stl_set.h
 mdp:/usr/include/c++/15/bits/stl_tempbuf.h
 mdp:/usr/include/c++/15/bits/stl_tree.h
 mdp:/usr/include/c++/15/bits/stl_uninitialized.h
 mdp:/usr/include/c++/15/bits/stl_vector.h
 mdp:/usr/include/c++/15/bits/stream_iterator.h
 mdp:/usr/include/c++/15/bits/streambuf.tcc
 mdp:/usr/include/c++/15/bits/streambuf_iterator.h
 mdp:/usr/include/c++/15/bits/string_view.tcc
 mdp:/usr/include/c++/15/bits/stringfwd.h
 mdp:/usr/include/c++/15/bits/uniform_int_dist.h
 mdp:/usr/include/c++/15/bits/unique_ptr.h
 mdp:/usr/include/c++/15/bits/unordered_map.h
 mdp:/usr/include/c++/15/bits/unordered_set.h
 mdp:/usr/include/c++/15/bits/uses_allocator.h
 mdp:/usr/include/c++/15/bits/uses_allocator_args.h
 mdp:/usr/include/c++/15/bits/utility.h
 mdp:/usr/include/c++/15/bits/vector.tcc
 mdp:/usr/include/c++/15/bits/version.h
 mdp:/usr/include/c++/15/cassert
 mdp:/usr/include/c++/15/cctype
 mdp:/usr/include/c++/15/cerrno
 mdp:/usr/include/c++/15/charconv
 mdp:/usr/include/c++/15/chrono
 mdp:/usr/include/c++/15/climits
 mdp:/usr/include/c++/15/clocale
 mdp:/usr/include/c++/15/cmath
 mdp:/usr/include/c++/15/compare
 mdp:/usr/include/c++/15/concepts
 mdp:/usr/include/c++/15/cstddef
 mdp:/usr/include/c++/15/cstdint
 mdp:/usr/include/c++/15/cstdio
 mdp:/usr/include/c++/15/cstdlib
 mdp:/usr/include/c++/15/cstring
 mdp:/usr/include/c++/15/ctime
 mdp:/usr/include/c++/15/cwchar
 mdp:/usr/include/c++/15/cwctype
 mdp:/usr/include/c++/15/debug/assertions.h
 mdp:/usr/include/c++/15/debug/debug.h
 mdp:/usr/include/c++/15/exception
 mdp:/usr/include/c++/15/ext/aligned_buffer.h
 mdp:/usr/include/c++/15/ext/alloc_traits.h
 mdp:/usr/include/c++/15/ext/atomicity.h
 mdp:/usr/include/c++/15/ext/concurrence.h
 mdp:/usr/include/c++/15/ext/numeric_traits.h
 mdp:/usr/include/c++/15/ext/string_conversions.h
 mdp:/usr/include/c++/15/ext/type_traits.h
 mdp:/usr/include/c++/15/format
 mdp:/usr/include/c++/15/functional
 mdp:/usr/include/c++/15/initializer_list
 mdp:/usr/include/c++/15/iomanip
 mdp:/usr/include/c++/15/ios
 mdp:/usr/include/c++/15/iosfwd
 mdp:/usr/include/c++/15/istream
 mdp:/usr/include/c++/15/iterator
 mdp:/usr/include/c++/15/limits
 mdp:/usr/include/c++/15/list
 mdp:/usr/include/c++/15/locale
 mdp:/usr/include/c++/15/map
 mdp:/usr/include/c++/15/memory
 mdp:/usr/include/c++/15/new
 mdp:/usr/include/c++/15/numeric
 mdp:/usr/include/c++/15/optional
 mdp:/usr/include/c++/15/ostream
 mdp:/usr/include/c++/15/pstl/execution_defs.h
 mdp:/usr/include/c++/15/pstl/glue_numeric_defs.h
 mdp:/usr/include/c++/15/ratio
 mdp:/usr/include/c++/15/set
 mdp:/usr/include/c++/15/sstream
 mdp:/usr/include/c++/15/stdexcept
 mdp:/usr/include/c++/15/streambuf
 mdp:/usr/include/c++/15/string
 mdp:/usr/include/c++/15/string_view
 mdp:/usr/include/c++/15/system_error
 mdp:/usr/include/c++/15/tr1/bessel_function.tcc
 mdp:/usr/include/c++/15/tr1/beta_function.tcc
 mdp:/usr/include/c++/15/tr1/ell_integral.tcc
 mdp:/usr/include/c++/15/tr1/exp_integral.tcc
 mdp:/usr/include/c++/15/tr1/gamma.tcc
 mdp:/usr/include/c++/15/tr1/hypergeometric.tcc
 mdp:/usr/include/c++/15/tr1/legendre_function.tcc
 mdp:/usr/include/c++/15/tr1/modified_bessel_func.tcc
 mdp:/usr/include/c++/15/tr1/poly_hermite.tcc
 mdp:/usr/include/c++/15/tr1/poly_laguerre.tcc
 mdp:/usr/include/c++/15/tr1/riemann_zeta.tcc
 mdp:/usr/include/c++/15/tr1/special_function_util.h
 mdp:/usr/include/c++/15/tuple
 mdp:/usr/include/c++/15/type_traits
 mdp:/usr/include/c++/15/typeinfo
 mdp:/usr/include/c++/15/unordered_map
 mdp:/usr/include/c++/15/unordered_set
 mdp:/usr/include/c++/15/utility
 mdp:/usr/include/c++/15/variant
 mdp:/usr/include/c++/15/vector
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/atomic_word.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/c++allocator.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/c++config.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/c++locale.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/cpu_defines.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/ctype_base.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/ctype_inline.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/error_constants.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/gthr-default.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/gthr.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/messages_members.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/os_defines.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/time_members.h
 mdp:/usr/include/ctype.h
 mdp:/usr/include/endian.h
 mdp:/usr/include/errno.h
 mdp:/usr/include/features-time64.h
 mdp:/usr/include/features.h
 mdp:/usr/include/gnu/stubs-64.h
 mdp:/usr/include/gnu/stubs.h
 mdp:/usr/include/libintl.h
 mdp:/usr/include/limits.h
 mdp:/usr/include/linux/errno.h
 mdp:/usr/include/linux/limits.h
 mdp:/usr/include/linux/posix_types.h
 mdp:/usr/include/linux/stddef.h
 mdp:/usr/include/linux/types.h
 mdp:/usr/include/locale.h
 mdp:/usr/include/pthread.h
 mdp:/usr/include/qt6/QtCore/q17memory.h
 mdp:/usr/include/qt6/QtCore/q20functional.h
 mdp:/usr/include/qt6/QtCore/q20iterator.h
 mdp:/usr/include/qt6/QtCore/q20memory.h
 mdp:/usr/include/qt6/QtCore/q20type_traits.h
 mdp:/usr/include/qt6/QtCore/q20utility.h
 mdp:/usr/include/qt6/QtCore/q23utility.h
 mdp:/usr/include/qt6/QtCore/qalgorithms.h
 mdp:/usr/include/qt6/QtCore/qanystringview.h
 mdp:/usr/include/qt6/QtCore/qarraydata.h
 mdp:/usr/include/qt6/QtCore/qarraydataops.h
 mdp:/usr/include/qt6/QtCore/qarraydatapointer.h
 mdp:/usr/include/qt6/QtCore/qassert.h
 mdp:/usr/include/qt6/QtCore/qatomic.h
 mdp:/usr/include/qt6/QtCore/qatomic_cxx11.h
 mdp:/usr/include/qt6/QtCore/qbasicatomic.h
 mdp:/usr/include/qt6/QtCore/qbindingstorage.h
 mdp:/usr/include/qt6/QtCore/qbytearray.h
 mdp:/usr/include/qt6/QtCore/qbytearrayalgorithms.h
 mdp:/usr/include/qt6/QtCore/qbytearraylist.h
 mdp:/usr/include/qt6/QtCore/qbytearrayview.h
 mdp:/usr/include/qt6/QtCore/qchar.h
 mdp:/usr/include/qt6/QtCore/qcompare.h
 mdp:/usr/include/qt6/QtCore/qcompare_impl.h
 mdp:/usr/include/qt6/QtCore/qcomparehelpers.h
 mdp:/usr/include/qt6/QtCore/qcompilerdetection.h
 mdp:/usr/include/qt6/QtCore/qconfig-64.h
 mdp:/usr/include/qt6/QtCore/qconfig.h
 mdp:/usr/include/qt6/QtCore/qconstructormacros.h
 mdp:/usr/include/qt6/QtCore/qcontainerfwd.h
 mdp:/usr/include/qt6/QtCore/qcontainerinfo.h
 mdp:/usr/include/qt6/QtCore/qcontainertools_impl.h
 mdp:/usr/include/qt6/QtCore/qcontiguouscache.h
 mdp:/usr/include/qt6/QtCore/qdarwinhelpers.h
 mdp:/usr/include/qt6/QtCore/qdatastream.h
 mdp:/usr/include/qt6/QtCore/qdebug.h
 mdp:/usr/include/qt6/QtCore/qendian.h
 mdp:/usr/include/qt6/QtCore/qexceptionhandling.h
 mdp:/usr/include/qt6/QtCore/qflags.h
 mdp:/usr/include/qt6/QtCore/qfloat16.h
 mdp:/usr/include/qt6/QtCore/qforeach.h
 mdp:/usr/include/qt6/QtCore/qfunctionaltools_impl.h
 mdp:/usr/include/qt6/QtCore/qfunctionpointer.h
 mdp:/usr/include/qt6/QtCore/qgenericatomic.h
 mdp:/usr/include/qt6/QtCore/qglobal.h
 mdp:/usr/include/qt6/QtCore/qglobalstatic.h
 mdp:/usr/include/qt6/QtCore/qhash.h
 mdp:/usr/include/qt6/QtCore/qhashfunctions.h
 mdp:/usr/include/qt6/QtCore/qiodevicebase.h
 mdp:/usr/include/qt6/QtCore/qiterable.h
 mdp:/usr/include/qt6/QtCore/qiterator.h
 mdp:/usr/include/qt6/QtCore/qlatin1stringview.h
 mdp:/usr/include/qt6/QtCore/qline.h
 mdp:/usr/include/qt6/QtCore/qlist.h
 mdp:/usr/include/qt6/QtCore/qlogging.h
 mdp:/usr/include/qt6/QtCore/qmalloc.h
 mdp:/usr/include/qt6/QtCore/qmap.h
 mdp:/usr/include/qt6/QtCore/qmargins.h
 mdp:/usr/include/qt6/QtCore/qmath.h
 mdp:/usr/include/qt6/QtCore/qmetacontainer.h
 mdp:/usr/include/qt6/QtCore/qmetatype.h
 mdp:/usr/include/qt6/QtCore/qminmax.h
 mdp:/usr/include/qt6/QtCore/qnamespace.h
 mdp:/usr/include/qt6/QtCore/qnumeric.h
 mdp:/usr/include/qt6/QtCore/qobject.h
 mdp:/usr/include/qt6/QtCore/qobject_impl.h
 mdp:/usr/include/qt6/QtCore/qobjectdefs.h
 mdp:/usr/include/qt6/QtCore/qobjectdefs_impl.h
 mdp:/usr/include/qt6/QtCore/qoverload.h
 mdp:/usr/include/qt6/QtCore/qpair.h
 mdp:/usr/include/qt6/QtCore/qpoint.h
 mdp:/usr/include/qt6/QtCore/qprocessordetection.h
 mdp:/usr/include/qt6/QtCore/qrect.h
 mdp:/usr/include/qt6/QtCore/qrefcount.h
 mdp:/usr/include/qt6/QtCore/qscopedpointer.h
 mdp:/usr/include/qt6/QtCore/qscopeguard.h
 mdp:/usr/include/qt6/QtCore/qset.h
 mdp:/usr/include/qt6/QtCore/qshareddata.h
 mdp:/usr/include/qt6/QtCore/qshareddata_impl.h
 mdp:/usr/include/qt6/QtCore/qsharedpointer.h
 mdp:/usr/include/qt6/QtCore/qsharedpointer_impl.h
 mdp:/usr/include/qt6/QtCore/qsize.h
 mdp:/usr/include/qt6/QtCore/qspan.h
 mdp:/usr/include/qt6/QtCore/qstdlibdetection.h
 mdp:/usr/include/qt6/QtCore/qstring.h
 mdp:/usr/include/qt6/QtCore/qstringalgorithms.h
 mdp:/usr/include/qt6/QtCore/qstringbuilder.h
 mdp:/usr/include/qt6/QtCore/qstringconverter.h
 mdp:/usr/include/qt6/QtCore/qstringconverter_base.h
 mdp:/usr/include/qt6/QtCore/qstringfwd.h
 mdp:/usr/include/qt6/QtCore/qstringlist.h
 mdp:/usr/include/qt6/QtCore/qstringliteral.h
 mdp:/usr/include/qt6/QtCore/qstringmatcher.h
 mdp:/usr/include/qt6/QtCore/qstringtokenizer.h
 mdp:/usr/include/qt6/QtCore/qstringview.h
 mdp:/usr/include/qt6/QtCore/qswap.h
 mdp:/usr/include/qt6/QtCore/qsysinfo.h
 mdp:/usr/include/qt6/QtCore/qsystemdetection.h
 mdp:/usr/include/qt6/QtCore/qtaggedpointer.h
 mdp:/usr/include/qt6/QtCore/qtclasshelpermacros.h
 mdp:/usr/include/qt6/QtCore/qtconfiginclude.h
 mdp:/usr/include/qt6/QtCore/qtconfigmacros.h
 mdp:/usr/include/qt6/QtCore/qtcore-config.h
 mdp:/usr/include/qt6/QtCore/qtcoreexports.h
 mdp:/usr/include/qt6/QtCore/qtcoreglobal.h
 mdp:/usr/include/qt6/QtCore/qtdeprecationdefinitions.h
 mdp:/usr/include/qt6/QtCore/qtdeprecationmarkers.h
 mdp:/usr/include/qt6/QtCore/qtenvironmentvariables.h
 mdp:/usr/include/qt6/QtCore/qtextstream.h
 mdp:/usr/include/qt6/QtCore/qtformat_impl.h
 mdp:/usr/include/qt6/QtCore/qtmetamacros.h
 mdp:/usr/include/qt6/QtCore/qtnoop.h
 mdp:/usr/include/qt6/QtCore/qtpreprocessorsupport.h
 mdp:/usr/include/qt6/QtCore/qtresource.h
 mdp:/usr/include/qt6/QtCore/qttranslation.h
 mdp:/usr/include/qt6/QtCore/qttypetraits.h
 mdp:/usr/include/qt6/QtCore/qtversion.h
 mdp:/usr/include/qt6/QtCore/qtversionchecks.h
 mdp:/usr/include/qt6/QtCore/qtypeinfo.h
 mdp:/usr/include/qt6/QtCore/qtypes.h
 mdp:/usr/include/qt6/QtCore/qurl.h
 mdp:/usr/include/qt6/QtCore/qutf8stringview.h
 mdp:/usr/include/qt6/QtCore/qvariant.h
 mdp:/usr/include/qt6/QtCore/qvarlengtharray.h
 mdp:/usr/include/qt6/QtCore/qversiontagging.h
 mdp:/usr/include/qt6/QtCore/qxptype_traits.h
 mdp:/usr/include/qt6/QtCore/qyieldcpu.h
 mdp:/usr/include/qt6/QtGui/qaction.h
 mdp:/usr/include/qt6/QtGui/qbitmap.h
 mdp:/usr/include/qt6/QtGui/qbrush.h
 mdp:/usr/include/qt6/QtGui/qcolor.h
 mdp:/usr/include/qt6/QtGui/qcursor.h
 mdp:/usr/include/qt6/QtGui/qfont.h
 mdp:/usr/include/qt6/QtGui/qfontinfo.h
 mdp:/usr/include/qt6/QtGui/qfontmetrics.h
 mdp:/usr/include/qt6/QtGui/qfontvariableaxis.h
 mdp:/usr/include/qt6/QtGui/qicon.h
 mdp:/usr/include/qt6/QtGui/qimage.h
 mdp:/usr/include/qt6/QtGui/qkeysequence.h
 mdp:/usr/include/qt6/QtGui/qpaintdevice.h
 mdp:/usr/include/qt6/QtGui/qpalette.h
 mdp:/usr/include/qt6/QtGui/qpen.h
 mdp:/usr/include/qt6/QtGui/qpixelformat.h
 mdp:/usr/include/qt6/QtGui/qpixmap.h
 mdp:/usr/include/qt6/QtGui/qpolygon.h
 mdp:/usr/include/qt6/QtGui/qregion.h
 mdp:/usr/include/qt6/QtGui/qrgb.h
 mdp:/usr/include/qt6/QtGui/qrgba64.h
 mdp:/usr/include/qt6/QtGui/qtextcursor.h
 mdp:/usr/include/qt6/QtGui/qtextdocument.h
 mdp:/usr/include/qt6/QtGui/qtextformat.h
 mdp:/usr/include/qt6/QtGui/qtextoption.h
 mdp:/usr/include/qt6/QtGui/qtgui-config.h
 mdp:/usr/include/qt6/QtGui/qtguiexports.h
 mdp:/usr/include/qt6/QtGui/qtguiglobal.h
 mdp:/usr/include/qt6/QtGui/qtransform.h
 mdp:/usr/include/qt6/QtGui/qwindowdefs.h
 mdp:/usr/include/qt6/QtWidgets/QTextEdit
 mdp:/usr/include/qt6/QtWidgets/QToolBar
 mdp:/usr/include/qt6/QtWidgets/qabstractscrollarea.h
 mdp:/usr/include/qt6/QtWidgets/qframe.h
 mdp:/usr/include/qt6/QtWidgets/qsizepolicy.h
 mdp:/usr/include/qt6/QtWidgets/qtextedit.h
 mdp:/usr/include/qt6/QtWidgets/qtoolbar.h
 mdp:/usr/include/qt6/QtWidgets/qtwidgets-config.h
 mdp:/usr/include/qt6/QtWidgets/qtwidgetsexports.h
 mdp:/usr/include/qt6/QtWidgets/qtwidgetsglobal.h
 mdp:/usr/include/qt6/QtWidgets/qwidget.h
 mdp:/usr/include/sched.h
 mdp:/usr/include/stdc-predef.h
 mdp:/usr/include/stdio.h
 mdp:/usr/include/stdlib.h
 mdp:/usr/include/string.h
 mdp:/usr/include/strings.h
 mdp:/usr/include/sys/cdefs.h
 mdp:/usr/include/sys/select.h
 mdp:/usr/include/sys/syscall.h
 mdp:/usr/include/sys/types.h
 mdp:/usr/include/syscall.h
 mdp:/usr/include/time.h
 mdp:/usr/include/unistd.h
 mdp:/usr/include/wchar.h
 mdp:/usr/include/wctype.h
 mdp:/usr/lib/gcc/x86_64-redhat-linux/15/include/stdarg.h
 mdp:/usr/lib/gcc/x86_64-redhat-linux/15/include/stdbool.h
 mdp:/usr/lib/gcc/x86_64-redhat-linux/15/include/stddef.h
/home/<USER>/CLionProjects/KNoteDo/app/SettingsDialog.h
 mmc:Q_OBJECT
 mdp:/home/<USER>/CLionProjects/KNoteDo/app/SettingsDialog.h
 mdp:/home/<USER>/CLionProjects/KNoteDo/build/KNoteDo_autogen/moc_predefs.h
 mdp:/usr/include/alloca.h
 mdp:/usr/include/asm-generic/bitsperlong.h
 mdp:/usr/include/asm-generic/errno-base.h
 mdp:/usr/include/asm-generic/errno.h
 mdp:/usr/include/asm-generic/int-ll64.h
 mdp:/usr/include/asm-generic/posix_types.h
 mdp:/usr/include/asm-generic/types.h
 mdp:/usr/include/asm/bitsperlong.h
 mdp:/usr/include/asm/errno.h
 mdp:/usr/include/asm/posix_types.h
 mdp:/usr/include/asm/posix_types_64.h
 mdp:/usr/include/asm/types.h
 mdp:/usr/include/asm/unistd.h
 mdp:/usr/include/asm/unistd_64.h
 mdp:/usr/include/assert.h
 mdp:/usr/include/bits/atomic_wide_counter.h
 mdp:/usr/include/bits/byteswap.h
 mdp:/usr/include/bits/confname.h
 mdp:/usr/include/bits/cpu-set.h
 mdp:/usr/include/bits/endian.h
 mdp:/usr/include/bits/endianness.h
 mdp:/usr/include/bits/environments.h
 mdp:/usr/include/bits/errno.h
 mdp:/usr/include/bits/floatn-common.h
 mdp:/usr/include/bits/floatn.h
 mdp:/usr/include/bits/getopt_core.h
 mdp:/usr/include/bits/getopt_posix.h
 mdp:/usr/include/bits/libc-header-start.h
 mdp:/usr/include/bits/local_lim.h
 mdp:/usr/include/bits/locale.h
 mdp:/usr/include/bits/long-double.h
 mdp:/usr/include/bits/posix1_lim.h
 mdp:/usr/include/bits/posix2_lim.h
 mdp:/usr/include/bits/posix_opt.h
 mdp:/usr/include/bits/pthread_stack_min-dynamic.h
 mdp:/usr/include/bits/pthreadtypes-arch.h
 mdp:/usr/include/bits/pthreadtypes.h
 mdp:/usr/include/bits/sched.h
 mdp:/usr/include/bits/select.h
 mdp:/usr/include/bits/setjmp.h
 mdp:/usr/include/bits/stdint-intn.h
 mdp:/usr/include/bits/stdio_lim.h
 mdp:/usr/include/bits/stdlib-float.h
 mdp:/usr/include/bits/struct_mutex.h
 mdp:/usr/include/bits/struct_rwlock.h
 mdp:/usr/include/bits/syscall.h
 mdp:/usr/include/bits/thread-shared-types.h
 mdp:/usr/include/bits/time.h
 mdp:/usr/include/bits/time64.h
 mdp:/usr/include/bits/timesize.h
 mdp:/usr/include/bits/timex.h
 mdp:/usr/include/bits/types.h
 mdp:/usr/include/bits/types/FILE.h
 mdp:/usr/include/bits/types/__FILE.h
 mdp:/usr/include/bits/types/__fpos64_t.h
 mdp:/usr/include/bits/types/__fpos_t.h
 mdp:/usr/include/bits/types/__locale_t.h
 mdp:/usr/include/bits/types/__mbstate_t.h
 mdp:/usr/include/bits/types/__sigset_t.h
 mdp:/usr/include/bits/types/clock_t.h
 mdp:/usr/include/bits/types/clockid_t.h
 mdp:/usr/include/bits/types/cookie_io_functions_t.h
 mdp:/usr/include/bits/types/error_t.h
 mdp:/usr/include/bits/types/locale_t.h
 mdp:/usr/include/bits/types/mbstate_t.h
 mdp:/usr/include/bits/types/sigset_t.h
 mdp:/usr/include/bits/types/struct_FILE.h
 mdp:/usr/include/bits/types/struct___jmp_buf_tag.h
 mdp:/usr/include/bits/types/struct_itimerspec.h
 mdp:/usr/include/bits/types/struct_sched_param.h
 mdp:/usr/include/bits/types/struct_timespec.h
 mdp:/usr/include/bits/types/struct_timeval.h
 mdp:/usr/include/bits/types/struct_tm.h
 mdp:/usr/include/bits/types/time_t.h
 mdp:/usr/include/bits/types/timer_t.h
 mdp:/usr/include/bits/types/wint_t.h
 mdp:/usr/include/bits/typesizes.h
 mdp:/usr/include/bits/uintn-identity.h
 mdp:/usr/include/bits/uio_lim.h
 mdp:/usr/include/bits/unistd_ext.h
 mdp:/usr/include/bits/waitflags.h
 mdp:/usr/include/bits/waitstatus.h
 mdp:/usr/include/bits/wchar.h
 mdp:/usr/include/bits/wctype-wchar.h
 mdp:/usr/include/bits/wordsize.h
 mdp:/usr/include/bits/xopen_lim.h
 mdp:/usr/include/c++/15/algorithm
 mdp:/usr/include/c++/15/array
 mdp:/usr/include/c++/15/atomic
 mdp:/usr/include/c++/15/backward/auto_ptr.h
 mdp:/usr/include/c++/15/backward/binders.h
 mdp:/usr/include/c++/15/bit
 mdp:/usr/include/c++/15/bits/algorithmfwd.h
 mdp:/usr/include/c++/15/bits/align.h
 mdp:/usr/include/c++/15/bits/alloc_traits.h
 mdp:/usr/include/c++/15/bits/allocated_ptr.h
 mdp:/usr/include/c++/15/bits/allocator.h
 mdp:/usr/include/c++/15/bits/atomic_base.h
 mdp:/usr/include/c++/15/bits/atomic_lockfree_defines.h
 mdp:/usr/include/c++/15/bits/atomic_wait.h
 mdp:/usr/include/c++/15/bits/basic_ios.h
 mdp:/usr/include/c++/15/bits/basic_ios.tcc
 mdp:/usr/include/c++/15/bits/basic_string.h
 mdp:/usr/include/c++/15/bits/basic_string.tcc
 mdp:/usr/include/c++/15/bits/char_traits.h
 mdp:/usr/include/c++/15/bits/charconv.h
 mdp:/usr/include/c++/15/bits/chrono.h
 mdp:/usr/include/c++/15/bits/chrono_io.h
 mdp:/usr/include/c++/15/bits/codecvt.h
 mdp:/usr/include/c++/15/bits/concept_check.h
 mdp:/usr/include/c++/15/bits/cpp_type_traits.h
 mdp:/usr/include/c++/15/bits/cxxabi_forced.h
 mdp:/usr/include/c++/15/bits/cxxabi_init_exception.h
 mdp:/usr/include/c++/15/bits/enable_special_members.h
 mdp:/usr/include/c++/15/bits/erase_if.h
 mdp:/usr/include/c++/15/bits/exception.h
 mdp:/usr/include/c++/15/bits/exception_defines.h
 mdp:/usr/include/c++/15/bits/exception_ptr.h
 mdp:/usr/include/c++/15/bits/functexcept.h
 mdp:/usr/include/c++/15/bits/functional_hash.h
 mdp:/usr/include/c++/15/bits/hash_bytes.h
 mdp:/usr/include/c++/15/bits/hashtable.h
 mdp:/usr/include/c++/15/bits/hashtable_policy.h
 mdp:/usr/include/c++/15/bits/invoke.h
 mdp:/usr/include/c++/15/bits/ios_base.h
 mdp:/usr/include/c++/15/bits/istream.tcc
 mdp:/usr/include/c++/15/bits/iterator_concepts.h
 mdp:/usr/include/c++/15/bits/list.tcc
 mdp:/usr/include/c++/15/bits/locale_classes.h
 mdp:/usr/include/c++/15/bits/locale_classes.tcc
 mdp:/usr/include/c++/15/bits/locale_conv.h
 mdp:/usr/include/c++/15/bits/locale_facets.h
 mdp:/usr/include/c++/15/bits/locale_facets.tcc
 mdp:/usr/include/c++/15/bits/locale_facets_nonio.h
 mdp:/usr/include/c++/15/bits/locale_facets_nonio.tcc
 mdp:/usr/include/c++/15/bits/localefwd.h
 mdp:/usr/include/c++/15/bits/max_size_type.h
 mdp:/usr/include/c++/15/bits/memory_resource.h
 mdp:/usr/include/c++/15/bits/memoryfwd.h
 mdp:/usr/include/c++/15/bits/move.h
 mdp:/usr/include/c++/15/bits/nested_exception.h
 mdp:/usr/include/c++/15/bits/new_allocator.h
 mdp:/usr/include/c++/15/bits/node_handle.h
 mdp:/usr/include/c++/15/bits/ostream.h
 mdp:/usr/include/c++/15/bits/ostream.tcc
 mdp:/usr/include/c++/15/bits/ostream_insert.h
 mdp:/usr/include/c++/15/bits/parse_numbers.h
 mdp:/usr/include/c++/15/bits/postypes.h
 mdp:/usr/include/c++/15/bits/predefined_ops.h
 mdp:/usr/include/c++/15/bits/ptr_traits.h
 mdp:/usr/include/c++/15/bits/quoted_string.h
 mdp:/usr/include/c++/15/bits/range_access.h
 mdp:/usr/include/c++/15/bits/ranges_algo.h
 mdp:/usr/include/c++/15/bits/ranges_algobase.h
 mdp:/usr/include/c++/15/bits/ranges_base.h
 mdp:/usr/include/c++/15/bits/ranges_cmp.h
 mdp:/usr/include/c++/15/bits/ranges_uninitialized.h
 mdp:/usr/include/c++/15/bits/ranges_util.h
 mdp:/usr/include/c++/15/bits/refwrap.h
 mdp:/usr/include/c++/15/bits/requires_hosted.h
 mdp:/usr/include/c++/15/bits/shared_ptr.h
 mdp:/usr/include/c++/15/bits/shared_ptr_atomic.h
 mdp:/usr/include/c++/15/bits/shared_ptr_base.h
 mdp:/usr/include/c++/15/bits/specfun.h
 mdp:/usr/include/c++/15/bits/sstream.tcc
 mdp:/usr/include/c++/15/bits/std_abs.h
 mdp:/usr/include/c++/15/bits/std_function.h
 mdp:/usr/include/c++/15/bits/std_mutex.h
 mdp:/usr/include/c++/15/bits/stl_algo.h
 mdp:/usr/include/c++/15/bits/stl_algobase.h
 mdp:/usr/include/c++/15/bits/stl_bvector.h
 mdp:/usr/include/c++/15/bits/stl_construct.h
 mdp:/usr/include/c++/15/bits/stl_function.h
 mdp:/usr/include/c++/15/bits/stl_heap.h
 mdp:/usr/include/c++/15/bits/stl_iterator.h
 mdp:/usr/include/c++/15/bits/stl_iterator_base_funcs.h
 mdp:/usr/include/c++/15/bits/stl_iterator_base_types.h
 mdp:/usr/include/c++/15/bits/stl_list.h
 mdp:/usr/include/c++/15/bits/stl_map.h
 mdp:/usr/include/c++/15/bits/stl_multimap.h
 mdp:/usr/include/c++/15/bits/stl_multiset.h
 mdp:/usr/include/c++/15/bits/stl_numeric.h
 mdp:/usr/include/c++/15/bits/stl_pair.h
 mdp:/usr/include/c++/15/bits/stl_raw_storage_iter.h
 mdp:/usr/include/c++/15/bits/stl_relops.h
 mdp:/usr/include/c++/15/bits/stl_set.h
 mdp:/usr/include/c++/15/bits/stl_tempbuf.h
 mdp:/usr/include/c++/15/bits/stl_tree.h
 mdp:/usr/include/c++/15/bits/stl_uninitialized.h
 mdp:/usr/include/c++/15/bits/stl_vector.h
 mdp:/usr/include/c++/15/bits/stream_iterator.h
 mdp:/usr/include/c++/15/bits/streambuf.tcc
 mdp:/usr/include/c++/15/bits/streambuf_iterator.h
 mdp:/usr/include/c++/15/bits/string_view.tcc
 mdp:/usr/include/c++/15/bits/stringfwd.h
 mdp:/usr/include/c++/15/bits/uniform_int_dist.h
 mdp:/usr/include/c++/15/bits/unique_ptr.h
 mdp:/usr/include/c++/15/bits/unordered_map.h
 mdp:/usr/include/c++/15/bits/unordered_set.h
 mdp:/usr/include/c++/15/bits/uses_allocator.h
 mdp:/usr/include/c++/15/bits/uses_allocator_args.h
 mdp:/usr/include/c++/15/bits/utility.h
 mdp:/usr/include/c++/15/bits/vector.tcc
 mdp:/usr/include/c++/15/bits/version.h
 mdp:/usr/include/c++/15/cassert
 mdp:/usr/include/c++/15/cctype
 mdp:/usr/include/c++/15/cerrno
 mdp:/usr/include/c++/15/charconv
 mdp:/usr/include/c++/15/chrono
 mdp:/usr/include/c++/15/climits
 mdp:/usr/include/c++/15/clocale
 mdp:/usr/include/c++/15/cmath
 mdp:/usr/include/c++/15/compare
 mdp:/usr/include/c++/15/concepts
 mdp:/usr/include/c++/15/cstddef
 mdp:/usr/include/c++/15/cstdint
 mdp:/usr/include/c++/15/cstdio
 mdp:/usr/include/c++/15/cstdlib
 mdp:/usr/include/c++/15/cstring
 mdp:/usr/include/c++/15/ctime
 mdp:/usr/include/c++/15/cwchar
 mdp:/usr/include/c++/15/cwctype
 mdp:/usr/include/c++/15/debug/assertions.h
 mdp:/usr/include/c++/15/debug/debug.h
 mdp:/usr/include/c++/15/exception
 mdp:/usr/include/c++/15/ext/aligned_buffer.h
 mdp:/usr/include/c++/15/ext/alloc_traits.h
 mdp:/usr/include/c++/15/ext/atomicity.h
 mdp:/usr/include/c++/15/ext/concurrence.h
 mdp:/usr/include/c++/15/ext/numeric_traits.h
 mdp:/usr/include/c++/15/ext/string_conversions.h
 mdp:/usr/include/c++/15/ext/type_traits.h
 mdp:/usr/include/c++/15/format
 mdp:/usr/include/c++/15/functional
 mdp:/usr/include/c++/15/initializer_list
 mdp:/usr/include/c++/15/iomanip
 mdp:/usr/include/c++/15/ios
 mdp:/usr/include/c++/15/iosfwd
 mdp:/usr/include/c++/15/istream
 mdp:/usr/include/c++/15/iterator
 mdp:/usr/include/c++/15/limits
 mdp:/usr/include/c++/15/list
 mdp:/usr/include/c++/15/locale
 mdp:/usr/include/c++/15/map
 mdp:/usr/include/c++/15/memory
 mdp:/usr/include/c++/15/new
 mdp:/usr/include/c++/15/numeric
 mdp:/usr/include/c++/15/optional
 mdp:/usr/include/c++/15/ostream
 mdp:/usr/include/c++/15/pstl/execution_defs.h
 mdp:/usr/include/c++/15/pstl/glue_numeric_defs.h
 mdp:/usr/include/c++/15/ratio
 mdp:/usr/include/c++/15/set
 mdp:/usr/include/c++/15/sstream
 mdp:/usr/include/c++/15/stdexcept
 mdp:/usr/include/c++/15/streambuf
 mdp:/usr/include/c++/15/string
 mdp:/usr/include/c++/15/string_view
 mdp:/usr/include/c++/15/system_error
 mdp:/usr/include/c++/15/tr1/bessel_function.tcc
 mdp:/usr/include/c++/15/tr1/beta_function.tcc
 mdp:/usr/include/c++/15/tr1/ell_integral.tcc
 mdp:/usr/include/c++/15/tr1/exp_integral.tcc
 mdp:/usr/include/c++/15/tr1/gamma.tcc
 mdp:/usr/include/c++/15/tr1/hypergeometric.tcc
 mdp:/usr/include/c++/15/tr1/legendre_function.tcc
 mdp:/usr/include/c++/15/tr1/modified_bessel_func.tcc
 mdp:/usr/include/c++/15/tr1/poly_hermite.tcc
 mdp:/usr/include/c++/15/tr1/poly_laguerre.tcc
 mdp:/usr/include/c++/15/tr1/riemann_zeta.tcc
 mdp:/usr/include/c++/15/tr1/special_function_util.h
 mdp:/usr/include/c++/15/tuple
 mdp:/usr/include/c++/15/type_traits
 mdp:/usr/include/c++/15/typeinfo
 mdp:/usr/include/c++/15/unordered_map
 mdp:/usr/include/c++/15/unordered_set
 mdp:/usr/include/c++/15/utility
 mdp:/usr/include/c++/15/variant
 mdp:/usr/include/c++/15/vector
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/atomic_word.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/c++allocator.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/c++config.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/c++locale.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/cpu_defines.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/ctype_base.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/ctype_inline.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/error_constants.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/gthr-default.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/gthr.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/messages_members.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/os_defines.h
 mdp:/usr/include/c++/15/x86_64-redhat-linux/bits/time_members.h
 mdp:/usr/include/ctype.h
 mdp:/usr/include/endian.h
 mdp:/usr/include/errno.h
 mdp:/usr/include/features-time64.h
 mdp:/usr/include/features.h
 mdp:/usr/include/gnu/stubs-64.h
 mdp:/usr/include/gnu/stubs.h
 mdp:/usr/include/libintl.h
 mdp:/usr/include/limits.h
 mdp:/usr/include/linux/errno.h
 mdp:/usr/include/linux/limits.h
 mdp:/usr/include/linux/posix_types.h
 mdp:/usr/include/linux/stddef.h
 mdp:/usr/include/linux/types.h
 mdp:/usr/include/locale.h
 mdp:/usr/include/pthread.h
 mdp:/usr/include/qt6/QtCore/q17memory.h
 mdp:/usr/include/qt6/QtCore/q20functional.h
 mdp:/usr/include/qt6/QtCore/q20iterator.h
 mdp:/usr/include/qt6/QtCore/q20memory.h
 mdp:/usr/include/qt6/QtCore/q20type_traits.h
 mdp:/usr/include/qt6/QtCore/q20utility.h
 mdp:/usr/include/qt6/QtCore/q23utility.h
 mdp:/usr/include/qt6/QtCore/qalgorithms.h
 mdp:/usr/include/qt6/QtCore/qanystringview.h
 mdp:/usr/include/qt6/QtCore/qarraydata.h
 mdp:/usr/include/qt6/QtCore/qarraydataops.h
 mdp:/usr/include/qt6/QtCore/qarraydatapointer.h
 mdp:/usr/include/qt6/QtCore/qassert.h
 mdp:/usr/include/qt6/QtCore/qatomic.h
 mdp:/usr/include/qt6/QtCore/qatomic_cxx11.h
 mdp:/usr/include/qt6/QtCore/qbasicatomic.h
 mdp:/usr/include/qt6/QtCore/qbindingstorage.h
 mdp:/usr/include/qt6/QtCore/qbytearray.h
 mdp:/usr/include/qt6/QtCore/qbytearrayalgorithms.h
 mdp:/usr/include/qt6/QtCore/qbytearraylist.h
 mdp:/usr/include/qt6/QtCore/qbytearrayview.h
 mdp:/usr/include/qt6/QtCore/qchar.h
 mdp:/usr/include/qt6/QtCore/qcompare.h
 mdp:/usr/include/qt6/QtCore/qcompare_impl.h
 mdp:/usr/include/qt6/QtCore/qcomparehelpers.h
 mdp:/usr/include/qt6/QtCore/qcompilerdetection.h
 mdp:/usr/include/qt6/QtCore/qconfig-64.h
 mdp:/usr/include/qt6/QtCore/qconfig.h
 mdp:/usr/include/qt6/QtCore/qconstructormacros.h
 mdp:/usr/include/qt6/QtCore/qcontainerfwd.h
 mdp:/usr/include/qt6/QtCore/qcontainerinfo.h
 mdp:/usr/include/qt6/QtCore/qcontainertools_impl.h
 mdp:/usr/include/qt6/QtCore/qcontiguouscache.h
 mdp:/usr/include/qt6/QtCore/qdarwinhelpers.h
 mdp:/usr/include/qt6/QtCore/qdatastream.h
 mdp:/usr/include/qt6/QtCore/qdebug.h
 mdp:/usr/include/qt6/QtCore/qendian.h
 mdp:/usr/include/qt6/QtCore/qexceptionhandling.h
 mdp:/usr/include/qt6/QtCore/qflags.h
 mdp:/usr/include/qt6/QtCore/qfloat16.h
 mdp:/usr/include/qt6/QtCore/qforeach.h
 mdp:/usr/include/qt6/QtCore/qfunctionaltools_impl.h
 mdp:/usr/include/qt6/QtCore/qfunctionpointer.h
 mdp:/usr/include/qt6/QtCore/qgenericatomic.h
 mdp:/usr/include/qt6/QtCore/qglobal.h
 mdp:/usr/include/qt6/QtCore/qglobalstatic.h
 mdp:/usr/include/qt6/QtCore/qhash.h
 mdp:/usr/include/qt6/QtCore/qhashfunctions.h
 mdp:/usr/include/qt6/QtCore/qiodevicebase.h
 mdp:/usr/include/qt6/QtCore/qiterable.h
 mdp:/usr/include/qt6/QtCore/qiterator.h
 mdp:/usr/include/qt6/QtCore/qlatin1stringview.h
 mdp:/usr/include/qt6/QtCore/qline.h
 mdp:/usr/include/qt6/QtCore/qlist.h
 mdp:/usr/include/qt6/QtCore/qlogging.h
 mdp:/usr/include/qt6/QtCore/qmalloc.h
 mdp:/usr/include/qt6/QtCore/qmap.h
 mdp:/usr/include/qt6/QtCore/qmargins.h
 mdp:/usr/include/qt6/QtCore/qmath.h
 mdp:/usr/include/qt6/QtCore/qmetacontainer.h
 mdp:/usr/include/qt6/QtCore/qmetatype.h
 mdp:/usr/include/qt6/QtCore/qminmax.h
 mdp:/usr/include/qt6/QtCore/qnamespace.h
 mdp:/usr/include/qt6/QtCore/qnumeric.h
 mdp:/usr/include/qt6/QtCore/qobject.h
 mdp:/usr/include/qt6/QtCore/qobject_impl.h
 mdp:/usr/include/qt6/QtCore/qobjectdefs.h
 mdp:/usr/include/qt6/QtCore/qobjectdefs_impl.h
 mdp:/usr/include/qt6/QtCore/qoverload.h
 mdp:/usr/include/qt6/QtCore/qpair.h
 mdp:/usr/include/qt6/QtCore/qpoint.h
 mdp:/usr/include/qt6/QtCore/qprocessordetection.h
 mdp:/usr/include/qt6/QtCore/qrect.h
 mdp:/usr/include/qt6/QtCore/qrefcount.h
 mdp:/usr/include/qt6/QtCore/qscopedpointer.h
 mdp:/usr/include/qt6/QtCore/qscopeguard.h
 mdp:/usr/include/qt6/QtCore/qset.h
 mdp:/usr/include/qt6/QtCore/qshareddata.h
 mdp:/usr/include/qt6/QtCore/qshareddata_impl.h
 mdp:/usr/include/qt6/QtCore/qsharedpointer.h
 mdp:/usr/include/qt6/QtCore/qsharedpointer_impl.h
 mdp:/usr/include/qt6/QtCore/qsize.h
 mdp:/usr/include/qt6/QtCore/qspan.h
 mdp:/usr/include/qt6/QtCore/qstdlibdetection.h
 mdp:/usr/include/qt6/QtCore/qstring.h
 mdp:/usr/include/qt6/QtCore/qstringalgorithms.h
 mdp:/usr/include/qt6/QtCore/qstringbuilder.h
 mdp:/usr/include/qt6/QtCore/qstringconverter.h
 mdp:/usr/include/qt6/QtCore/qstringconverter_base.h
 mdp:/usr/include/qt6/QtCore/qstringfwd.h
 mdp:/usr/include/qt6/QtCore/qstringlist.h
 mdp:/usr/include/qt6/QtCore/qstringliteral.h
 mdp:/usr/include/qt6/QtCore/qstringmatcher.h
 mdp:/usr/include/qt6/QtCore/qstringtokenizer.h
 mdp:/usr/include/qt6/QtCore/qstringview.h
 mdp:/usr/include/qt6/QtCore/qswap.h
 mdp:/usr/include/qt6/QtCore/qsysinfo.h
 mdp:/usr/include/qt6/QtCore/qsystemdetection.h
 mdp:/usr/include/qt6/QtCore/qtaggedpointer.h
 mdp:/usr/include/qt6/QtCore/qtclasshelpermacros.h
 mdp:/usr/include/qt6/QtCore/qtconfiginclude.h
 mdp:/usr/include/qt6/QtCore/qtconfigmacros.h
 mdp:/usr/include/qt6/QtCore/qtcore-config.h
 mdp:/usr/include/qt6/QtCore/qtcoreexports.h
 mdp:/usr/include/qt6/QtCore/qtcoreglobal.h
 mdp:/usr/include/qt6/QtCore/qtdeprecationdefinitions.h
 mdp:/usr/include/qt6/QtCore/qtdeprecationmarkers.h
 mdp:/usr/include/qt6/QtCore/qtenvironmentvariables.h
 mdp:/usr/include/qt6/QtCore/qtextstream.h
 mdp:/usr/include/qt6/QtCore/qtformat_impl.h
 mdp:/usr/include/qt6/QtCore/qtmetamacros.h
 mdp:/usr/include/qt6/QtCore/qtnoop.h
 mdp:/usr/include/qt6/QtCore/qtpreprocessorsupport.h
 mdp:/usr/include/qt6/QtCore/qtresource.h
 mdp:/usr/include/qt6/QtCore/qttranslation.h
 mdp:/usr/include/qt6/QtCore/qttypetraits.h
 mdp:/usr/include/qt6/QtCore/qtversion.h
 mdp:/usr/include/qt6/QtCore/qtversionchecks.h
 mdp:/usr/include/qt6/QtCore/qtypeinfo.h
 mdp:/usr/include/qt6/QtCore/qtypes.h
 mdp:/usr/include/qt6/QtCore/qutf8stringview.h
 mdp:/usr/include/qt6/QtCore/qvariant.h
 mdp:/usr/include/qt6/QtCore/qvarlengtharray.h
 mdp:/usr/include/qt6/QtCore/qversiontagging.h
 mdp:/usr/include/qt6/QtCore/qxptype_traits.h
 mdp:/usr/include/qt6/QtCore/qyieldcpu.h
 mdp:/usr/include/qt6/QtGui/qaction.h
 mdp:/usr/include/qt6/QtGui/qbitmap.h
 mdp:/usr/include/qt6/QtGui/qbrush.h
 mdp:/usr/include/qt6/QtGui/qcolor.h
 mdp:/usr/include/qt6/QtGui/qcursor.h
 mdp:/usr/include/qt6/QtGui/qfont.h
 mdp:/usr/include/qt6/QtGui/qfontinfo.h
 mdp:/usr/include/qt6/QtGui/qfontmetrics.h
 mdp:/usr/include/qt6/QtGui/qfontvariableaxis.h
 mdp:/usr/include/qt6/QtGui/qicon.h
 mdp:/usr/include/qt6/QtGui/qimage.h
 mdp:/usr/include/qt6/QtGui/qkeysequence.h
 mdp:/usr/include/qt6/QtGui/qpaintdevice.h
 mdp:/usr/include/qt6/QtGui/qpalette.h
 mdp:/usr/include/qt6/QtGui/qpixelformat.h
 mdp:/usr/include/qt6/QtGui/qpixmap.h
 mdp:/usr/include/qt6/QtGui/qpolygon.h
 mdp:/usr/include/qt6/QtGui/qregion.h
 mdp:/usr/include/qt6/QtGui/qrgb.h
 mdp:/usr/include/qt6/QtGui/qrgba64.h
 mdp:/usr/include/qt6/QtGui/qtgui-config.h
 mdp:/usr/include/qt6/QtGui/qtguiexports.h
 mdp:/usr/include/qt6/QtGui/qtguiglobal.h
 mdp:/usr/include/qt6/QtGui/qtransform.h
 mdp:/usr/include/qt6/QtGui/qwindowdefs.h
 mdp:/usr/include/qt6/QtWidgets/QDialog
 mdp:/usr/include/qt6/QtWidgets/qdialog.h
 mdp:/usr/include/qt6/QtWidgets/qsizepolicy.h
 mdp:/usr/include/qt6/QtWidgets/qtwidgets-config.h
 mdp:/usr/include/qt6/QtWidgets/qtwidgetsexports.h
 mdp:/usr/include/qt6/QtWidgets/qtwidgetsglobal.h
 mdp:/usr/include/qt6/QtWidgets/qwidget.h
 mdp:/usr/include/sched.h
 mdp:/usr/include/stdc-predef.h
 mdp:/usr/include/stdio.h
 mdp:/usr/include/stdlib.h
 mdp:/usr/include/string.h
 mdp:/usr/include/strings.h
 mdp:/usr/include/sys/cdefs.h
 mdp:/usr/include/sys/select.h
 mdp:/usr/include/sys/syscall.h
 mdp:/usr/include/sys/types.h
 mdp:/usr/include/syscall.h
 mdp:/usr/include/time.h
 mdp:/usr/include/unistd.h
 mdp:/usr/include/wchar.h
 mdp:/usr/include/wctype.h
 mdp:/usr/lib/gcc/x86_64-redhat-linux/15/include/stdarg.h
 mdp:/usr/lib/gcc/x86_64-redhat-linux/15/include/stdbool.h
 mdp:/usr/lib/gcc/x86_64-redhat-linux/15/include/stddef.h
/home/<USER>/CLionProjects/KNoteDo/app/RichTextEditor.cpp
