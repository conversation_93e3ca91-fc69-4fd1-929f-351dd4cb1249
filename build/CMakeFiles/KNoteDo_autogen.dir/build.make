# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/CLionProjects/KNoteDo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/CLionProjects/KNoteDo/build

# Utility rule file for KNoteDo_autogen.

# Include any custom commands dependencies for this target.
include CMakeFiles/KNoteDo_autogen.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/KNoteDo_autogen.dir/progress.make

CMakeFiles/KNoteDo_autogen: KNoteDo_autogen/timestamp

KNoteDo_autogen/timestamp: /usr/lib64/qt6/libexec/moc
KNoteDo_autogen/timestamp: /usr/lib64/qt6/libexec/uic
KNoteDo_autogen/timestamp: CMakeFiles/KNoteDo_autogen.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC and UIC for target KNoteDo"
	/usr/bin/cmake -E cmake_autogen /home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles/KNoteDo_autogen.dir/AutogenInfo.json ""
	/usr/bin/cmake -E touch /home/<USER>/CLionProjects/KNoteDo/build/KNoteDo_autogen/timestamp

CMakeFiles/KNoteDo_autogen.dir/codegen:
.PHONY : CMakeFiles/KNoteDo_autogen.dir/codegen

KNoteDo_autogen: CMakeFiles/KNoteDo_autogen
KNoteDo_autogen: KNoteDo_autogen/timestamp
KNoteDo_autogen: CMakeFiles/KNoteDo_autogen.dir/build.make
.PHONY : KNoteDo_autogen

# Rule to build all files generated by this target.
CMakeFiles/KNoteDo_autogen.dir/build: KNoteDo_autogen
.PHONY : CMakeFiles/KNoteDo_autogen.dir/build

CMakeFiles/KNoteDo_autogen.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/KNoteDo_autogen.dir/cmake_clean.cmake
.PHONY : CMakeFiles/KNoteDo_autogen.dir/clean

CMakeFiles/KNoteDo_autogen.dir/depend:
	cd /home/<USER>/CLionProjects/KNoteDo/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/CLionProjects/KNoteDo /home/<USER>/CLionProjects/KNoteDo /home/<USER>/CLionProjects/KNoteDo/build /home/<USER>/CLionProjects/KNoteDo/build /home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles/KNoteDo_autogen.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/KNoteDo_autogen.dir/depend

