# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/CLionProjects/KNoteDo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/CLionProjects/KNoteDo/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles /home/<USER>/CLionProjects/KNoteDo/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles 0
.PHONY : all

# The main codegen target
codegen: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles /home/<USER>/CLionProjects/KNoteDo/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 codegen
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/KNoteDo/build/CMakeFiles 0
.PHONY : codegen

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named KNoteDo

# Build rule for target.
KNoteDo: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 KNoteDo
.PHONY : KNoteDo

# fast build rule for target.
KNoteDo/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/build
.PHONY : KNoteDo/fast

#=============================================================================
# Target rules for targets named KNoteDo_autogen_timestamp_deps

# Build rule for target.
KNoteDo_autogen_timestamp_deps: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 KNoteDo_autogen_timestamp_deps
.PHONY : KNoteDo_autogen_timestamp_deps

# fast build rule for target.
KNoteDo_autogen_timestamp_deps/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/build.make CMakeFiles/KNoteDo_autogen_timestamp_deps.dir/build
.PHONY : KNoteDo_autogen_timestamp_deps/fast

#=============================================================================
# Target rules for targets named KNoteDo_autogen

# Build rule for target.
KNoteDo_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 KNoteDo_autogen
.PHONY : KNoteDo_autogen

# fast build rule for target.
KNoteDo_autogen/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo_autogen.dir/build.make CMakeFiles/KNoteDo_autogen.dir/build
.PHONY : KNoteDo_autogen/fast

KNoteDo_autogen/mocs_compilation.o: KNoteDo_autogen/mocs_compilation.cpp.o
.PHONY : KNoteDo_autogen/mocs_compilation.o

# target to build an object file
KNoteDo_autogen/mocs_compilation.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.o
.PHONY : KNoteDo_autogen/mocs_compilation.cpp.o

KNoteDo_autogen/mocs_compilation.i: KNoteDo_autogen/mocs_compilation.cpp.i
.PHONY : KNoteDo_autogen/mocs_compilation.i

# target to preprocess a source file
KNoteDo_autogen/mocs_compilation.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.i
.PHONY : KNoteDo_autogen/mocs_compilation.cpp.i

KNoteDo_autogen/mocs_compilation.s: KNoteDo_autogen/mocs_compilation.cpp.s
.PHONY : KNoteDo_autogen/mocs_compilation.s

# target to generate assembly for a file
KNoteDo_autogen/mocs_compilation.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/KNoteDo_autogen/mocs_compilation.cpp.s
.PHONY : KNoteDo_autogen/mocs_compilation.cpp.s

app/MainWindow.o: app/MainWindow.cpp.o
.PHONY : app/MainWindow.o

# target to build an object file
app/MainWindow.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.o
.PHONY : app/MainWindow.cpp.o

app/MainWindow.i: app/MainWindow.cpp.i
.PHONY : app/MainWindow.i

# target to preprocess a source file
app/MainWindow.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.i
.PHONY : app/MainWindow.cpp.i

app/MainWindow.s: app/MainWindow.cpp.s
.PHONY : app/MainWindow.s

# target to generate assembly for a file
app/MainWindow.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/app/MainWindow.cpp.s
.PHONY : app/MainWindow.cpp.s

app/RichTextEditor.o: app/RichTextEditor.cpp.o
.PHONY : app/RichTextEditor.o

# target to build an object file
app/RichTextEditor.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.o
.PHONY : app/RichTextEditor.cpp.o

app/RichTextEditor.i: app/RichTextEditor.cpp.i
.PHONY : app/RichTextEditor.i

# target to preprocess a source file
app/RichTextEditor.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.i
.PHONY : app/RichTextEditor.cpp.i

app/RichTextEditor.s: app/RichTextEditor.cpp.s
.PHONY : app/RichTextEditor.s

# target to generate assembly for a file
app/RichTextEditor.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/app/RichTextEditor.cpp.s
.PHONY : app/RichTextEditor.cpp.s

app/SettingsDialog.o: app/SettingsDialog.cpp.o
.PHONY : app/SettingsDialog.o

# target to build an object file
app/SettingsDialog.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.o
.PHONY : app/SettingsDialog.cpp.o

app/SettingsDialog.i: app/SettingsDialog.cpp.i
.PHONY : app/SettingsDialog.i

# target to preprocess a source file
app/SettingsDialog.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.i
.PHONY : app/SettingsDialog.cpp.i

app/SettingsDialog.s: app/SettingsDialog.cpp.s
.PHONY : app/SettingsDialog.s

# target to generate assembly for a file
app/SettingsDialog.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/app/SettingsDialog.cpp.s
.PHONY : app/SettingsDialog.cpp.s

main.o: main.cpp.o
.PHONY : main.o

# target to build an object file
main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/main.cpp.o
.PHONY : main.cpp.o

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/KNoteDo.dir/build.make CMakeFiles/KNoteDo.dir/main.cpp.s
.PHONY : main.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... codegen"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... KNoteDo_autogen"
	@echo "... KNoteDo_autogen_timestamp_deps"
	@echo "... KNoteDo"
	@echo "... KNoteDo_autogen/mocs_compilation.o"
	@echo "... KNoteDo_autogen/mocs_compilation.i"
	@echo "... KNoteDo_autogen/mocs_compilation.s"
	@echo "... app/MainWindow.o"
	@echo "... app/MainWindow.i"
	@echo "... app/MainWindow.s"
	@echo "... app/RichTextEditor.o"
	@echo "... app/RichTextEditor.i"
	@echo "... app/RichTextEditor.s"
	@echo "... app/SettingsDialog.o"
	@echo "... app/SettingsDialog.i"
	@echo "... app/SettingsDialog.s"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

