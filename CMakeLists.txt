cmake_minimum_required(VERSION 3.31)
project(KNoteDo VERSION 1.0 LANGUAGES CXX)

# Enable C++20 and Qt MOC/UIC features
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# Set your source directory (adjust if you move files)
include_directories(app)

# Find Qt6 modules
find_package(Qt6 REQUIRED COMPONENTS Core Gui Widgets PrintSupport)

include_directories(
        /usr/include/qt6
        /usr/include/qt6/QtWidgets
        /usr/include/qt6/QtGui
        /usr/include/qt6/QtCore
)
# Add your source files
add_executable(KNoteDo
        main.cpp
        app/MainWindow.cpp
        app/MainWindow.h
        app/RichTextEditor.cpp
        app/RichTextEditor.h
        app/SettingsDialog.cpp
        app/SettingsDialog.h
        app/AppleNotesTheme.cpp
        app/AppleNotesTheme.h
        app/FolderManager.cpp
        app/FolderManager.h
        app/ExportImportManager.cpp
        app/ExportImportManager.h
        app/TableEditor.cpp
        app/TableEditor.h
)

# Link Qt libraries
target_link_libraries(KNoteDo
        Qt6::Core
        Qt6::Gui
        Qt6::Widgets
        Qt6::PrintSupport
)
