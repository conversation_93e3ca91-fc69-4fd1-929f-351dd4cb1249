#include "SettingsDialog.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTabWidget>
#include <QComboBox>
#include <QCheckBox>
#include <QSpinBox>
#include <QLineEdit>
#include <QPushButton>
#include <QLabel>
#include <QGroupBox>
#include <QFileDialog>
#include <QFontComboBox>
#include <QStandardPaths>
#include <QDialogButtonBox>

SettingsDialog::SettingsDialog(QWidget *parent) : QDialog(parent) {
    setWindowTitle("Settings");
    setModal(true);
    resize(500, 400);
    
    setupUI();
}

void SettingsDialog::setupUI() {
    auto *mainLayout = new QVBoxLayout(this);
    
    tabWidget = new QTabWidget;
    setupAppearanceTab();
    setupEditorTab();
    setupGeneralTab();
    
    mainLayout->addWidget(tabWidget);
    
    // Dialog buttons
    auto *buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel);
    connect(buttonBox, &QDialogButtonBox::accepted, this, &QDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, this, &QDialog::reject);
    
    resetButton = new QPushButton("Reset to Defaults");
    connect(resetButton, &QPushButton::clicked, this, &SettingsDialog::resetToDefaults);
    buttonBox->addButton(resetButton, QDialogButtonBox::ResetRole);
    
    mainLayout->addWidget(buttonBox);
}

void SettingsDialog::setupAppearanceTab() {
    auto *appearanceWidget = new QWidget;
    auto *layout = new QVBoxLayout(appearanceWidget);
    
    // Theme selection
    auto *themeGroup = new QGroupBox("Theme");
    auto *themeLayout = new QVBoxLayout(themeGroup);
    
    themeComboBox = new QComboBox;
    themeComboBox->addItems({"System Default", "Light", "Dark", "Auto (Follow System)"});
    
    themeLayout->addWidget(new QLabel("Application Theme:"));
    themeLayout->addWidget(themeComboBox);
    
    layout->addWidget(themeGroup);
    layout->addStretch();
    
    tabWidget->addTab(appearanceWidget, "Appearance");
}

void SettingsDialog::setupEditorTab() {
    auto *editorWidget = new QWidget;
    auto *layout = new QVBoxLayout(editorWidget);
    
    // Auto-save settings
    auto *autoSaveGroup = new QGroupBox("Auto-save");
    auto *autoSaveLayout = new QVBoxLayout(autoSaveGroup);
    
    autoSaveCheckBox = new QCheckBox("Enable auto-save");
    autoSaveCheckBox->setChecked(true);
    
    auto *intervalLayout = new QHBoxLayout;
    intervalLayout->addWidget(new QLabel("Save interval:"));
    autoSaveSpinBox = new QSpinBox;
    autoSaveSpinBox->setRange(1, 60);
    autoSaveSpinBox->setValue(5);
    autoSaveSpinBox->setSuffix(" seconds");
    intervalLayout->addWidget(autoSaveSpinBox);
    intervalLayout->addStretch();
    
    autoSaveLayout->addWidget(autoSaveCheckBox);
    autoSaveLayout->addLayout(intervalLayout);
    
    // Font settings
    auto *fontGroup = new QGroupBox("Default Font");
    auto *fontLayout = new QVBoxLayout(fontGroup);
    
    auto *fontSelectLayout = new QHBoxLayout;
    fontSelectLayout->addWidget(new QLabel("Font:"));
    fontComboBox = new QFontComboBox;
    fontSelectLayout->addWidget(fontComboBox);
    
    auto *sizeLayout = new QHBoxLayout;
    sizeLayout->addWidget(new QLabel("Size:"));
    fontSizeSpinBox = new QSpinBox;
    fontSizeSpinBox->setRange(8, 72);
    fontSizeSpinBox->setValue(14);
    fontSizeSpinBox->setSuffix(" pt");
    sizeLayout->addWidget(fontSizeSpinBox);
    sizeLayout->addStretch();
    
    fontLayout->addLayout(fontSelectLayout);
    fontLayout->addLayout(sizeLayout);
    
    // Other editor settings
    auto *otherGroup = new QGroupBox("Other");
    auto *otherLayout = new QVBoxLayout(otherGroup);
    
    spellCheckBox = new QCheckBox("Enable spell checking (if available)");
    otherLayout->addWidget(spellCheckBox);
    
    layout->addWidget(autoSaveGroup);
    layout->addWidget(fontGroup);
    layout->addWidget(otherGroup);
    layout->addStretch();
    
    tabWidget->addTab(editorWidget, "Editor");
}

void SettingsDialog::setupGeneralTab() {
    auto *generalWidget = new QWidget;
    auto *layout = new QVBoxLayout(generalWidget);
    
    // Export settings
    auto *exportGroup = new QGroupBox("Export");
    auto *exportLayout = new QVBoxLayout(exportGroup);
    
    auto *pathLayout = new QHBoxLayout;
    pathLayout->addWidget(new QLabel("Default export path:"));
    exportPathEdit = new QLineEdit;
    exportPathEdit->setText(QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation));
    browseButton = new QPushButton("Browse...");
    connect(browseButton, &QPushButton::clicked, this, &SettingsDialog::browseExportPath);
    
    pathLayout->addWidget(exportPathEdit);
    pathLayout->addWidget(browseButton);
    
    exportLayout->addLayout(pathLayout);
    
    layout->addWidget(exportGroup);
    layout->addStretch();
    
    tabWidget->addTab(generalWidget, "General");
}

void SettingsDialog::browseExportPath() {
    QString dir = QFileDialog::getExistingDirectory(this, "Select Export Directory", 
                                                   exportPathEdit->text());
    if (!dir.isEmpty()) {
        exportPathEdit->setText(dir);
    }
}

void SettingsDialog::resetToDefaults() {
    themeComboBox->setCurrentText("System Default");
    autoSaveCheckBox->setChecked(true);
    autoSaveSpinBox->setValue(5);
    fontComboBox->setCurrentFont(QFont("System"));
    fontSizeSpinBox->setValue(14);
    spellCheckBox->setChecked(false);
    exportPathEdit->setText(QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation));
}

// Getters
QString SettingsDialog::getTheme() const {
    return themeComboBox->currentText();
}

bool SettingsDialog::getAutoSave() const {
    return autoSaveCheckBox->isChecked();
}

int SettingsDialog::getAutoSaveInterval() const {
    return autoSaveSpinBox->value();
}

QString SettingsDialog::getDefaultFont() const {
    return fontComboBox->currentFont().family();
}

int SettingsDialog::getDefaultFontSize() const {
    return fontSizeSpinBox->value();
}

bool SettingsDialog::getSpellCheck() const {
    return spellCheckBox->isChecked();
}

QString SettingsDialog::getExportPath() const {
    return exportPathEdit->text();
}

// Setters
void SettingsDialog::setTheme(const QString &theme) {
    themeComboBox->setCurrentText(theme);
}

void SettingsDialog::setAutoSave(bool enabled) {
    autoSaveCheckBox->setChecked(enabled);
}

void SettingsDialog::setAutoSaveInterval(int seconds) {
    autoSaveSpinBox->setValue(seconds);
}

void SettingsDialog::setDefaultFont(const QString &font) {
    fontComboBox->setCurrentFont(QFont(font));
}

void SettingsDialog::setDefaultFontSize(int size) {
    fontSizeSpinBox->setValue(size);
}

void SettingsDialog::setSpellCheck(bool enabled) {
    spellCheckBox->setChecked(enabled);
}

void SettingsDialog::setExportPath(const QString &path) {
    exportPathEdit->setText(path);
}
