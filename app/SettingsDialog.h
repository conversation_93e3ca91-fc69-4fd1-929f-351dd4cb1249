#pragma once
#include <QDialog>

class QComboBox;
class QCheckBox;
class QSpinBox;
class QLineEdit;
class QPushButton;
class QTabWidget;
class QFontComboBox;

class SettingsDialog : public QDialog {
    Q_OBJECT

public:
    explicit SettingsDialog(QWidget *parent = nullptr);

    // Getters for settings
    QString getTheme() const;
    bool getAutoSave() const;
    int getAutoSaveInterval() const;
    QString getDefaultFont() const;
    int getDefaultFontSize() const;
    bool getSpellCheck() const;
    QString getExportPath() const;

    // Setters for current settings
    void setTheme(const QString &theme);
    void setAutoSave(bool enabled);
    void setAutoSaveInterval(int seconds);
    void setDefaultFont(const QString &font);
    void setDefaultFontSize(int size);
    void setSpellCheck(bool enabled);
    void setExportPath(const QString &path);

private slots:
    void browseExportPath();
    void resetToDefaults();

private:
    void setupUI();
    void setupAppearanceTab();
    void setupEditorTab();
    void setupGeneralTab();

    QTabWidget *tabWidget;
    
    // Appearance settings
    QComboBox *themeComboBox;
    
    // Editor settings
    QCheckBox *autoSaveCheckBox;
    QSpinBox *autoSaveSpinBox;
    QFontComboBox *fontComboBox;
    QSpinBox *fontSizeSpinBox;
    QCheckBox *spellCheckBox;
    
    // General settings
    QLineEdit *exportPathEdit;
    QPushButton *browseButton;
    QPushButton *resetButton;
};
