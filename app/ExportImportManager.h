#pragma once
#include <QObject>
#include <QString>
#include <QStringList>

class QWidget;
class QProgressDialog;

class ExportImportManager : public QObject {
    Q_OBJECT

public:
    enum ExportFormat {
        PDF,
        HTML,
        Markdown,
        PlainText,
        JSON,
        AppleNotes,
        Evernote
    };

    enum ImportFormat {
        JSONFormat,
        AppleNotesFormat,
        EvernoteFormat,
        MarkdownFormat,
        PlainTextFormat
    };

    explicit ExportImportManager(QWidget *parent = nullptr);

    // Export functions
    bool exportNote(const QString &title, const QString &content, 
                   const QString &filePath, ExportFormat format);
    bool exportAllNotes(const QString &directoryPath, ExportFormat format);
    bool exportFolder(const QString &folderName, const QString &directoryPath, 
                     ExportFormat format);
    
    // Import functions
    bool importNotes(const QString &filePath, ImportFormat format);
    bool importFromAppleNotes(const QString &filePath);
    bool importFromEvernote(const QString &filePath);
    bool importMarkdownFiles(const QStringList &filePaths);
    
    // Batch operations
    bool batchExport(const QStringList &noteIds, const QString &directoryPath, 
                    ExportFormat format);
    
    // Utility functions
    QString getFileExtension(ExportFormat format);
    QString getFormatName(ExportFormat format);
    QStringList getSupportedExportFormats();
    QStringList getSupportedImportFormats();

signals:
    void exportProgress(int current, int total);
    void importProgress(int current, int total);
    void exportCompleted(const QString &message);
    void importCompleted(const QString &message);
    void errorOccurred(const QString &error);

private slots:
    void onExportProgress(int value);
    void onImportProgress(int value);

private:
    // Export helpers
    QString convertToHTML(const QString &content);
    QString convertToMarkdown(const QString &content);
    QString convertToPlainText(const QString &content);
    bool generatePDF(const QString &title, const QString &content, const QString &filePath);
    QString generateJSON(const QString &title, const QString &content, 
                        const QDateTime &date, const QString &folder);
    
    // Import helpers
    QList<QVariantMap> parseJSONFile(const QString &filePath);
    QList<QVariantMap> parseAppleNotesFile(const QString &filePath);
    QList<QVariantMap> parseEvernoteFile(const QString &filePath);
    QList<QVariantMap> parseMarkdownFile(const QString &filePath);
    
    // Utility helpers
    QString sanitizeFileName(const QString &fileName);
    QString extractTitle(const QString &content);
    bool createDirectory(const QString &path);
    void showProgressDialog(const QString &title, int maximum);
    void hideProgressDialog();
    
    QWidget *m_parent;
    QProgressDialog *m_progressDialog;
    int m_currentProgress;
    int m_totalProgress;
};
