#pragma once
#include <QObject>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QStringList>
#include <QDateTime>

class FolderManager : public QObject {
    Q_OBJECT

public:
    explicit FolderManager(QTreeWidget *folderTree, QObject *parent = nullptr);
    
    // Folder operations
    void createFolder(const QString &name, QTreeWidgetItem *parent = nullptr);
    void deleteFolder(QTreeWidgetItem *folder);
    void renameFolder(QTreeWidgetItem *folder, const QString &newName);
    void moveFolder(QTreeWidgetItem *folder, QTreeWidgetItem *newParent);
    
    // Smart folders
    void createSmartFolder(const QString &name, const QString &criteria);
    void updateSmartFolders();
    
    // Folder properties
    void setFolderColor(QTreeWidgetItem *folder, const QColor &color);
    void setFolderIcon(QTreeWidgetItem *folder, const QString &iconName);
    
    // Data management
    void loadFolders();
    void saveFolders();
    QStringList getAllFolderPaths();
    QString getFolderPath(QTreeWidgetItem *folder);
    
    // Folder statistics
    int getNoteCount(QTreeWidgetItem *folder);
    QDateTime getLastModified(QTreeWidgetItem *folder);

signals:
    void folderCreated(const QString &folderPath);
    void folderDeleted(const QString &folderPath);
    void folderRenamed(const QString &oldPath, const QString &newPath);

private slots:
    void onFolderContextMenu(const QPoint &pos);
    void onFolderDoubleClicked(QTreeWidgetItem *item, int column);

private:
    void setupDefaultFolders();
    void setupSmartFolders();
    void updateFolderCounts();
    QTreeWidgetItem* findFolderByPath(const QString &path);
    QString generateFolderPath(QTreeWidgetItem *folder);
    
    QTreeWidget *m_folderTree;
    QTreeWidgetItem *m_smartFoldersRoot;
    QTreeWidgetItem *m_userFoldersRoot;
};
