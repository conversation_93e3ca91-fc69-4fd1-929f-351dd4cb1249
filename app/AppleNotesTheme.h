#pragma once
#include <QString>
#include <QWidget>

class AppleNotesTheme {
public:
    static QString getLightThemeStyleSheet();
    static QString getDarkThemeStyleSheet();
    static void applyAppleNotesStyle(QWidget* widget, bool isDark = false);
    
    // Apple Notes color constants
    struct Colors {
        // Light theme colors
        static const QString LIGHT_BACKGROUND;
        static const QString LIGHT_SIDEBAR_BACKGROUND;
        static const QString LIGHT_NOTE_LIST_BACKGROUND;
        static const QString LIGHT_EDITOR_BACKGROUND;
        static const QString LIGHT_TEXT_COLOR;
        static const QString LIGHT_SECONDARY_TEXT;
        static const QString LIGHT_BORDER_COLOR;
        static const QString LIGHT_SELECTION_COLOR;
        static const QString LIGHT_HOVER_COLOR;
        
        // Dark theme colors
        static const QString DARK_BACKGROUND;
        static const QString DARK_SIDEBAR_BACKGROUND;
        static const QString DARK_NOTE_LIST_BACKGROUND;
        static const QString DARK_EDITOR_BACKGROUND;
        static const QString DARK_TEXT_COLOR;
        static const QString DARK_SECONDARY_TEXT;
        static const QString DARK_BORDER_COLOR;
        static const QString DARK_SELECTION_COLOR;
        static const QString DARK_HOVER_COLOR;
        
        // Accent colors
        static const QString YELLOW_ACCENT;
        static const QString BLUE_ACCENT;
        static const QString GREEN_ACCENT;
        static const QString RED_ACCENT;
    };
    
    struct Fonts {
        static const QString PRIMARY_FONT_FAMILY;
        static const QString MONOSPACE_FONT_FAMILY;
        static const int TITLE_FONT_SIZE;
        static const int BODY_FONT_SIZE;
        static const int SMALL_FONT_SIZE;
    };
    
    struct Spacing {
        static const int SIDEBAR_WIDTH;
        static const int NOTE_LIST_WIDTH;
        static const int PADDING_SMALL;
        static const int PADDING_MEDIUM;
        static const int PADDING_LARGE;
        static const int BORDER_RADIUS;
    };
};
