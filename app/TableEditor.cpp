#include "TableEditor.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QSpinBox>
#include <QTableWidget>
#include <QPushButton>
#include <QComboBox>
#include <QColorDialog>
#include <QTextEdit>
#include <QLabel>
#include <QGroupBox>
#include <QTextCursor>
#include <QTextTableFormat>
#include <QTextTableCellFormat>
#include <QHeaderView>

TableEditor::TableEditor(QTextTable *table, QWidget *parent)
    : QDialog(parent), m_table(table), m_borderColor(Qt::black), m_cellBackgroundColor(Qt::white) {
    
    setWindowTitle("Table Editor");
    setModal(true);
    resize(600, 500);
    
    setupUI();
    
    if (m_table) {
        setTableProperties(m_table);
    }
}

QTextTable* TableEditor::createTable(QTextEdit *editor, int rows, int columns) {
    if (!editor) return nullptr;
    
    QTextCursor cursor = editor->textCursor();
    
    QTextTableFormat tableFormat;
    tableFormat.setBorderStyle(QTextFrameFormat::BorderStyle_Solid);
    tableFormat.setBorderWidth(1);
    tableFormat.setCellPadding(4);
    tableFormat.setCellSpacing(0);
    tableFormat.setAlignment(Qt::AlignLeft);
    
    QTextTable *table = cursor.insertTable(rows, columns, tableFormat);
    return table;
}

void TableEditor::setupUI() {
    auto *mainLayout = new QVBoxLayout(this);
    
    // Table controls
    setupTableControls();
    
    // Style controls
    setupStyleControls();
    
    // Preview area
    auto *previewGroup = new QGroupBox("Preview");
    auto *previewLayout = new QVBoxLayout(previewGroup);
    
    m_previewTable = new QTableWidget(3, 3);
    m_previewTable->setMaximumHeight(200);
    m_previewTable->horizontalHeader()->setStretchLastSection(true);
    m_previewTable->verticalHeader()->setVisible(false);
    m_previewTable->setAlternatingRowColors(true);
    
    previewLayout->addWidget(m_previewTable);
    
    // Dialog buttons
    auto *buttonLayout = new QHBoxLayout;
    auto *okButton = new QPushButton("OK");
    auto *cancelButton = new QPushButton("Cancel");
    
    connect(okButton, &QPushButton::clicked, this, &QDialog::accept);
    connect(cancelButton, &QPushButton::clicked, this, &QDialog::reject);
    
    buttonLayout->addStretch();
    buttonLayout->addWidget(okButton);
    buttonLayout->addWidget(cancelButton);
    
    mainLayout->addWidget(previewGroup);
    mainLayout->addLayout(buttonLayout);
    
    updateTablePreview();
}

void TableEditor::setupTableControls() {
    auto *tableGroup = new QGroupBox("Table Structure");
    auto *layout = new QGridLayout(tableGroup);
    
    // Row controls
    layout->addWidget(new QLabel("Rows:"), 0, 0);
    m_rowsSpinBox = new QSpinBox;
    m_rowsSpinBox->setRange(1, 20);
    m_rowsSpinBox->setValue(3);
    layout->addWidget(m_rowsSpinBox, 0, 1);
    
    m_insertRowAboveBtn = new QPushButton("Insert Row Above");
    m_insertRowBelowBtn = new QPushButton("Insert Row Below");
    m_deleteRowBtn = new QPushButton("Delete Row");
    
    connect(m_insertRowAboveBtn, &QPushButton::clicked, this, &TableEditor::insertRowAbove);
    connect(m_insertRowBelowBtn, &QPushButton::clicked, this, &TableEditor::insertRowBelow);
    connect(m_deleteRowBtn, &QPushButton::clicked, this, &TableEditor::deleteRow);
    
    layout->addWidget(m_insertRowAboveBtn, 1, 0);
    layout->addWidget(m_insertRowBelowBtn, 1, 1);
    layout->addWidget(m_deleteRowBtn, 1, 2);
    
    // Column controls
    layout->addWidget(new QLabel("Columns:"), 2, 0);
    m_columnsSpinBox = new QSpinBox;
    m_columnsSpinBox->setRange(1, 10);
    m_columnsSpinBox->setValue(3);
    layout->addWidget(m_columnsSpinBox, 2, 1);
    
    m_insertColumnLeftBtn = new QPushButton("Insert Column Left");
    m_insertColumnRightBtn = new QPushButton("Insert Column Right");
    m_deleteColumnBtn = new QPushButton("Delete Column");
    
    connect(m_insertColumnLeftBtn, &QPushButton::clicked, this, &TableEditor::insertColumnLeft);
    connect(m_insertColumnRightBtn, &QPushButton::clicked, this, &TableEditor::insertColumnRight);
    connect(m_deleteColumnBtn, &QPushButton::clicked, this, &TableEditor::deleteColumn);
    
    layout->addWidget(m_insertColumnLeftBtn, 3, 0);
    layout->addWidget(m_insertColumnRightBtn, 3, 1);
    layout->addWidget(m_deleteColumnBtn, 3, 2);
    
    // Cell operations
    m_mergeCellsBtn = new QPushButton("Merge Cells");
    m_splitCellBtn = new QPushButton("Split Cell");
    
    connect(m_mergeCellsBtn, &QPushButton::clicked, this, &TableEditor::mergeCells);
    connect(m_splitCellBtn, &QPushButton::clicked, this, &TableEditor::splitCell);
    
    layout->addWidget(m_mergeCellsBtn, 4, 0);
    layout->addWidget(m_splitCellBtn, 4, 1);
    
    static_cast<QVBoxLayout*>(this->layout())->insertWidget(0, tableGroup);
}

void TableEditor::setupStyleControls() {
    auto *styleGroup = new QGroupBox("Table Style");
    auto *layout = new QGridLayout(styleGroup);
    
    // Border controls
    layout->addWidget(new QLabel("Border Width:"), 0, 0);
    m_borderWidthSpinBox = new QSpinBox;
    m_borderWidthSpinBox->setRange(0, 10);
    m_borderWidthSpinBox->setValue(1);
    layout->addWidget(m_borderWidthSpinBox, 0, 1);
    
    m_borderColorBtn = new QPushButton("Border Color");
    m_borderColorBtn->setStyleSheet(QString("background-color: %1").arg(m_borderColor.name()));
    connect(m_borderColorBtn, &QPushButton::clicked, this, &TableEditor::setTableBorderColor);
    layout->addWidget(m_borderColorBtn, 0, 2);
    
    // Background color
    m_cellBackgroundBtn = new QPushButton("Cell Background");
    m_cellBackgroundBtn->setStyleSheet(QString("background-color: %1").arg(m_cellBackgroundColor.name()));
    connect(m_cellBackgroundBtn, &QPushButton::clicked, this, &TableEditor::setCellBackgroundColor);
    layout->addWidget(m_cellBackgroundBtn, 1, 0);
    
    // Alignment
    layout->addWidget(new QLabel("Alignment:"), 1, 1);
    m_alignmentCombo = new QComboBox;
    m_alignmentCombo->addItems({"Left", "Center", "Right"});
    layout->addWidget(m_alignmentCombo, 1, 2);
    
    // Style presets
    layout->addWidget(new QLabel("Style:"), 2, 0);
    m_styleCombo = new QComboBox;
    m_styleCombo->addItems({"Default", "Minimal", "Grid", "Striped"});
    connect(m_styleCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &TableEditor::applyTableStyle);
    layout->addWidget(m_styleCombo, 2, 1, 1, 2);
    
    static_cast<QVBoxLayout*>(this->layout())->insertWidget(1, styleGroup);
}

void TableEditor::insertRowAbove() {
    int currentRow = m_previewTable->currentRow();
    if (currentRow >= 0) {
        m_previewTable->insertRow(currentRow);
        updateTablePreview();
    }
}

void TableEditor::insertRowBelow() {
    int currentRow = m_previewTable->currentRow();
    if (currentRow >= 0) {
        m_previewTable->insertRow(currentRow + 1);
    } else {
        m_previewTable->insertRow(m_previewTable->rowCount());
    }
    updateTablePreview();
}

void TableEditor::deleteRow() {
    int currentRow = m_previewTable->currentRow();
    if (currentRow >= 0 && m_previewTable->rowCount() > 1) {
        m_previewTable->removeRow(currentRow);
        updateTablePreview();
    }
}

void TableEditor::insertColumnLeft() {
    int currentColumn = m_previewTable->currentColumn();
    if (currentColumn >= 0) {
        m_previewTable->insertColumn(currentColumn);
        updateTablePreview();
    }
}

void TableEditor::insertColumnRight() {
    int currentColumn = m_previewTable->currentColumn();
    if (currentColumn >= 0) {
        m_previewTable->insertColumn(currentColumn + 1);
    } else {
        m_previewTable->insertColumn(m_previewTable->columnCount());
    }
    updateTablePreview();
}

void TableEditor::deleteColumn() {
    int currentColumn = m_previewTable->currentColumn();
    if (currentColumn >= 0 && m_previewTable->columnCount() > 1) {
        m_previewTable->removeColumn(currentColumn);
        updateTablePreview();
    }
}

void TableEditor::mergeCells() {
    // Placeholder for merge cells functionality
}

void TableEditor::splitCell() {
    // Placeholder for split cell functionality
}

void TableEditor::setTableBorderColor() {
    QColor color = QColorDialog::getColor(m_borderColor, this, "Choose Border Color");
    if (color.isValid()) {
        m_borderColor = color;
        m_borderColorBtn->setStyleSheet(QString("background-color: %1").arg(color.name()));
        updateTablePreview();
    }
}

void TableEditor::setCellBackgroundColor() {
    QColor color = QColorDialog::getColor(m_cellBackgroundColor, this, "Choose Background Color");
    if (color.isValid()) {
        m_cellBackgroundColor = color;
        m_cellBackgroundBtn->setStyleSheet(QString("background-color: %1").arg(color.name()));
        updateTablePreview();
    }
}

void TableEditor::setTableAlignment() {
    updateTablePreview();
}

void TableEditor::applyTableStyle() {
    QString style = m_styleCombo->currentText();
    
    if (style == "Minimal") {
        m_borderWidthSpinBox->setValue(0);
        m_borderColor = Qt::lightGray;
    } else if (style == "Grid") {
        m_borderWidthSpinBox->setValue(1);
        m_borderColor = Qt::black;
    } else if (style == "Striped") {
        m_borderWidthSpinBox->setValue(0);
        m_previewTable->setAlternatingRowColors(true);
    }
    
    updateTablePreview();
}

void TableEditor::updateTablePreview() {
    // Update the preview table appearance
    QString styleSheet = QString(
        "QTableWidget { "
        "border: %1px solid %2; "
        "background-color: %3; "
        "}"
    ).arg(m_borderWidthSpinBox->value())
     .arg(m_borderColor.name())
     .arg(m_cellBackgroundColor.name());
    
    m_previewTable->setStyleSheet(styleSheet);
}

void TableEditor::setTableProperties(QTextTable *table) {
    if (!table) return;
    
    m_rowsSpinBox->setValue(table->rows());
    m_columnsSpinBox->setValue(table->columns());
    
    QTextTableFormat format = table->format();
    m_borderWidthSpinBox->setValue(format.borderWidth());
    
    updateTablePreview();
}

void TableEditor::onCellSelectionChanged() {
    updateControlStates();
}

void TableEditor::updateControlStates() {
    // Enable/disable controls based on selection
    bool hasSelection = !m_previewTable->selectedItems().isEmpty();
    m_mergeCellsBtn->setEnabled(hasSelection);
    m_splitCellBtn->setEnabled(hasSelection);
}
