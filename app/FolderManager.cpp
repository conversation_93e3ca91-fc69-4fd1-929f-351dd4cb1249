#include "FolderManager.h"
#include <QMenu>
#include <QInputDialog>
#include <QMessageBox>
#include <QColorDialog>
#include <QSettings>
#include <QApplication>

FolderManager::FolderManager(QTreeWidget *folderTree, QObject *parent)
    : QObject(parent), m_folderTree(folderTree) {
    
    // Setup context menu
    m_folderTree->setContextMenuPolicy(Qt::CustomContextMenu);
    connect(m_folderTree, &QTreeWidget::customContextMenuRequested,
            this, &FolderManager::onFolderContextMenu);
    connect(m_folderTree, &QTreeWidget::itemDoubleClicked,
            this, &FolderManager::onFolderDoubleClicked);
    
    setupDefaultFolders();
    setupSmartFolders();
    loadFolders();
}

void FolderManager::setupDefaultFolders() {
    m_folderTree->clear();
    
    // Default system folders
    auto *allNotesItem = new QTreeWidgetItem(m_folderTree, QStringList("📒 All Notes"));
    allNotesItem->setData(0, Qt::UserRole, "system");
    allNotesItem->setData(0, Qt::UserRole + 1, "all_notes");
    
    auto *tasksItem = new QTreeWidgetItem(m_folderTree, QStringList("📋 Tasks"));
    tasksItem->setData(0, Qt::UserRole, "system");
    tasksItem->setData(0, Qt::UserRole + 1, "tasks");
    
    auto *trashItem = new QTreeWidgetItem(m_folderTree, QStringList("🗑️ Recently Deleted"));
    trashItem->setData(0, Qt::UserRole, "system");
    trashItem->setData(0, Qt::UserRole + 1, "trash");
    
    // Smart folders section
    m_smartFoldersRoot = new QTreeWidgetItem(m_folderTree, QStringList("🔍 Smart Folders"));
    m_smartFoldersRoot->setData(0, Qt::UserRole, "smart_root");
    m_smartFoldersRoot->setExpanded(true);
    
    // User folders section
    m_userFoldersRoot = new QTreeWidgetItem(m_folderTree, QStringList("📁 My Folders"));
    m_userFoldersRoot->setData(0, Qt::UserRole, "user_root");
    m_userFoldersRoot->setExpanded(true);
    
    m_folderTree->setCurrentItem(allNotesItem);
}

void FolderManager::setupSmartFolders() {
    // Recent notes
    auto *recentItem = new QTreeWidgetItem(m_smartFoldersRoot, QStringList("🕒 Recent"));
    recentItem->setData(0, Qt::UserRole, "smart");
    recentItem->setData(0, Qt::UserRole + 1, "recent");
    
    // Favorites
    auto *favoritesItem = new QTreeWidgetItem(m_smartFoldersRoot, QStringList("⭐ Favorites"));
    favoritesItem->setData(0, Qt::UserRole, "smart");
    favoritesItem->setData(0, Qt::UserRole + 1, "favorites");
    
    // Notes with attachments
    auto *attachmentsItem = new QTreeWidgetItem(m_smartFoldersRoot, QStringList("📎 With Attachments"));
    attachmentsItem->setData(0, Qt::UserRole, "smart");
    attachmentsItem->setData(0, Qt::UserRole + 1, "attachments");
    
    // Long notes
    auto *longNotesItem = new QTreeWidgetItem(m_smartFoldersRoot, QStringList("📄 Long Notes"));
    longNotesItem->setData(0, Qt::UserRole, "smart");
    longNotesItem->setData(0, Qt::UserRole + 1, "long_notes");
}

void FolderManager::createFolder(const QString &name, QTreeWidgetItem *parent) {
    if (name.isEmpty()) return;
    
    QTreeWidgetItem *parentItem = parent ? parent : m_userFoldersRoot;
    auto *newFolder = new QTreeWidgetItem(parentItem, QStringList("📁 " + name));
    newFolder->setData(0, Qt::UserRole, "user");
    newFolder->setData(0, Qt::UserRole + 1, name);
    newFolder->setData(0, Qt::UserRole + 2, QDateTime::currentDateTime());
    
    parentItem->setExpanded(true);
    m_folderTree->setCurrentItem(newFolder);
    
    saveFolders();
    emit folderCreated(getFolderPath(newFolder));
}

void FolderManager::deleteFolder(QTreeWidgetItem *folder) {
    if (!folder || folder->data(0, Qt::UserRole).toString() == "system") return;
    
    QString folderName = folder->text(0);
    int ret = QMessageBox::question(m_folderTree, "Delete Folder",
                                   QString("Are you sure you want to delete '%1'?\n"
                                          "All notes in this folder will be moved to 'All Notes'.")
                                   .arg(folderName),
                                   QMessageBox::Yes | QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        QString folderPath = getFolderPath(folder);
        delete folder;
        saveFolders();
        emit folderDeleted(folderPath);
    }
}

void FolderManager::renameFolder(QTreeWidgetItem *folder, const QString &newName) {
    if (!folder || newName.isEmpty() || folder->data(0, Qt::UserRole).toString() == "system") return;
    
    QString oldPath = getFolderPath(folder);
    QString oldText = folder->text(0);
    QString prefix = oldText.left(2); // Keep emoji prefix
    
    folder->setText(0, prefix + " " + newName);
    folder->setData(0, Qt::UserRole + 1, newName);
    
    saveFolders();
    emit folderRenamed(oldPath, getFolderPath(folder));
}

void FolderManager::setFolderColor(QTreeWidgetItem *folder, const QColor &color) {
    if (!folder) return;
    
    folder->setData(0, Qt::UserRole + 3, color);
    folder->setForeground(0, QBrush(color));
    saveFolders();
}

void FolderManager::setFolderIcon(QTreeWidgetItem *folder, const QString &iconName) {
    if (!folder) return;
    
    QString currentText = folder->text(0);
    QString name = currentText.mid(2).trimmed(); // Remove current emoji
    folder->setText(0, iconName + " " + name);
    folder->setData(0, Qt::UserRole + 4, iconName);
    saveFolders();
}

void FolderManager::onFolderContextMenu(const QPoint &pos) {
    QTreeWidgetItem *item = m_folderTree->itemAt(pos);
    if (!item) return;
    
    QMenu menu;
    QString itemType = item->data(0, Qt::UserRole).toString();
    
    if (itemType == "user" || itemType == "user_root") {
        menu.addAction("📁 New Folder", [this, item]() {
            bool ok;
            QString name = QInputDialog::getText(m_folderTree, "New Folder", 
                                               "Folder name:", QLineEdit::Normal, "", &ok);
            if (ok && !name.isEmpty()) {
                createFolder(name, itemType == "user" ? item : nullptr);
            }
        });
        
        if (itemType == "user") {
            menu.addSeparator();
            menu.addAction("✏️ Rename", [this, item]() {
                bool ok;
                QString currentName = item->data(0, Qt::UserRole + 1).toString();
                QString newName = QInputDialog::getText(m_folderTree, "Rename Folder",
                                                       "New name:", QLineEdit::Normal, currentName, &ok);
                if (ok && !newName.isEmpty()) {
                    renameFolder(item, newName);
                }
            });
            
            menu.addAction("🎨 Change Color", [this, item]() {
                QColor currentColor = item->data(0, Qt::UserRole + 3).value<QColor>();
                QColor color = QColorDialog::getColor(currentColor, m_folderTree, "Choose Folder Color");
                if (color.isValid()) {
                    setFolderColor(item, color);
                }
            });
            
            menu.addSeparator();
            menu.addAction("🗑️ Delete", [this, item]() {
                deleteFolder(item);
            });
        }
    }
    
    if (!menu.isEmpty()) {
        menu.exec(m_folderTree->mapToGlobal(pos));
    }
}

void FolderManager::onFolderDoubleClicked(QTreeWidgetItem *item, int column) {
    Q_UNUSED(column)
    if (!item) return;
    
    QString itemType = item->data(0, Qt::UserRole).toString();
    if (itemType == "user") {
        // Allow inline editing for user folders
        m_folderTree->editItem(item, 0);
    }
}

QString FolderManager::getFolderPath(QTreeWidgetItem *folder) {
    if (!folder) return QString();
    
    QStringList path;
    QTreeWidgetItem *current = folder;
    
    while (current && current->parent()) {
        QString name = current->data(0, Qt::UserRole + 1).toString();
        if (!name.isEmpty()) {
            path.prepend(name);
        }
        current = current->parent();
    }
    
    return path.join("/");
}

void FolderManager::loadFolders() {
    QSettings settings("KNoteDo", "Folders");
    
    // Load user folders
    int count = settings.beginReadArray("userFolders");
    for (int i = 0; i < count; ++i) {
        settings.setArrayIndex(i);
        QString name = settings.value("name").toString();
        QString parentPath = settings.value("parentPath").toString();
        QColor color = settings.value("color").value<QColor>();
        QString icon = settings.value("icon", "📁").toString();
        
        QTreeWidgetItem *parent = parentPath.isEmpty() ? m_userFoldersRoot : findFolderByPath(parentPath);
        if (!parent) parent = m_userFoldersRoot;
        
        auto *folder = new QTreeWidgetItem(parent, QStringList(icon + " " + name));
        folder->setData(0, Qt::UserRole, "user");
        folder->setData(0, Qt::UserRole + 1, name);
        folder->setData(0, Qt::UserRole + 3, color);
        folder->setData(0, Qt::UserRole + 4, icon);
        
        if (color.isValid()) {
            folder->setForeground(0, QBrush(color));
        }
    }
    settings.endArray();
    
    updateFolderCounts();
}

void FolderManager::saveFolders() {
    QSettings settings("KNoteDo", "Folders");
    
    // Save user folders
    QList<QTreeWidgetItem*> userFolders;
    std::function<void(QTreeWidgetItem*)> collectFolders = [&](QTreeWidgetItem *parent) {
        for (int i = 0; i < parent->childCount(); ++i) {
            QTreeWidgetItem *child = parent->child(i);
            if (child->data(0, Qt::UserRole).toString() == "user") {
                userFolders.append(child);
            }
            collectFolders(child);
        }
    };
    collectFolders(m_userFoldersRoot);
    
    settings.beginWriteArray("userFolders");
    for (int i = 0; i < userFolders.size(); ++i) {
        settings.setArrayIndex(i);
        QTreeWidgetItem *folder = userFolders[i];
        settings.setValue("name", folder->data(0, Qt::UserRole + 1).toString());
        settings.setValue("parentPath", getFolderPath(folder->parent()));
        settings.setValue("color", folder->data(0, Qt::UserRole + 3));
        settings.setValue("icon", folder->data(0, Qt::UserRole + 4).toString());
    }
    settings.endArray();
}

QTreeWidgetItem* FolderManager::findFolderByPath(const QString &path) {
    // Implementation to find folder by path
    // This is a simplified version
    return nullptr;
}

void FolderManager::updateFolderCounts() {
    // Update folder counts - this would integrate with the note system
    // For now, this is a placeholder
}

int FolderManager::getNoteCount(QTreeWidgetItem *folder) {
    // Return note count for folder
    return 0;
}

QDateTime FolderManager::getLastModified(QTreeWidgetItem *folder) {
    return folder->data(0, Qt::UserRole + 2).toDateTime();
}
