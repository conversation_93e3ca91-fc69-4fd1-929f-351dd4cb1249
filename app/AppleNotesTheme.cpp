#include "AppleNotesTheme.h"
#include <QApplication>
#include <QWidget>

// Light theme colors (matching Apple Notes)
const QString AppleNotesTheme::Colors::LIGHT_BACKGROUND = "#ffffff";
const QString AppleNotesTheme::Colors::LIGHT_SIDEBAR_BACKGROUND = "#f7f7f7";
const QString AppleNotesTheme::Colors::LIGHT_NOTE_LIST_BACKGROUND = "#ffffff";
const QString AppleNotesTheme::Colors::LIGHT_EDITOR_BACKGROUND = "#ffffff";
const QString AppleNotesTheme::Colors::LIGHT_TEXT_COLOR = "#000000";
const QString AppleNotesTheme::Colors::LIGHT_SECONDARY_TEXT = "#6d6d6d";
const QString AppleNotesTheme::Colors::LIGHT_BORDER_COLOR = "#e5e5e5";
const QString AppleNotesTheme::Colors::LIGHT_SELECTION_COLOR = "#007aff";
const QString AppleNotesTheme::Colors::LIGHT_HOVER_COLOR = "#f0f0f0";

// Dark theme colors (matching Apple Notes dark mode)
const QString AppleNotesTheme::Colors::DARK_BACKGROUND = "#1c1c1e";
const QString AppleNotesTheme::Colors::DARK_SIDEBAR_BACKGROUND = "#2c2c2e";
const QString AppleNotesTheme::Colors::DARK_NOTE_LIST_BACKGROUND = "#1c1c1e";
const QString AppleNotesTheme::Colors::DARK_EDITOR_BACKGROUND = "#1c1c1e";
const QString AppleNotesTheme::Colors::DARK_TEXT_COLOR = "#ffffff";
const QString AppleNotesTheme::Colors::DARK_SECONDARY_TEXT = "#8e8e93";
const QString AppleNotesTheme::Colors::DARK_BORDER_COLOR = "#38383a";
const QString AppleNotesTheme::Colors::DARK_SELECTION_COLOR = "#0a84ff";
const QString AppleNotesTheme::Colors::DARK_HOVER_COLOR = "#3a3a3c";

// Accent colors
const QString AppleNotesTheme::Colors::YELLOW_ACCENT = "#ffcc02";
const QString AppleNotesTheme::Colors::BLUE_ACCENT = "#007aff";
const QString AppleNotesTheme::Colors::GREEN_ACCENT = "#34c759";
const QString AppleNotesTheme::Colors::RED_ACCENT = "#ff3b30";

// Fonts
const QString AppleNotesTheme::Fonts::PRIMARY_FONT_FAMILY = "SF Pro Text, -apple-system, system-ui, sans-serif";
const QString AppleNotesTheme::Fonts::MONOSPACE_FONT_FAMILY = "SF Mono, Monaco, monospace";
const int AppleNotesTheme::Fonts::TITLE_FONT_SIZE = 17;
const int AppleNotesTheme::Fonts::BODY_FONT_SIZE = 15;
const int AppleNotesTheme::Fonts::SMALL_FONT_SIZE = 13;

// Spacing
const int AppleNotesTheme::Spacing::SIDEBAR_WIDTH = 240;
const int AppleNotesTheme::Spacing::NOTE_LIST_WIDTH = 320;
const int AppleNotesTheme::Spacing::PADDING_SMALL = 8;
const int AppleNotesTheme::Spacing::PADDING_MEDIUM = 16;
const int AppleNotesTheme::Spacing::PADDING_LARGE = 24;
const int AppleNotesTheme::Spacing::BORDER_RADIUS = 8;

QString AppleNotesTheme::getLightThemeStyleSheet() {
    return QString(R"(
        /* Main Window */
        QMainWindow {
            background-color: %1;
            color: %2;
        }
        
        /* Sidebar (Folder List) */
        QTreeWidget {
            background-color: %3;
            border: none;
            border-right: 1px solid %4;
            color: %2;
            font-size: %5px;
            outline: none;
            selection-background-color: %6;
            selection-color: white;
        }
        
        QTreeWidget::item {
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            margin: 2px 8px;
        }
        
        QTreeWidget::item:hover {
            background-color: %7;
        }
        
        QTreeWidget::item:selected {
            background-color: %6;
            color: white;
        }
        
        /* Notes List */
        QListWidget {
            background-color: %8;
            border: none;
            border-right: 1px solid %4;
            color: %2;
            font-size: %5px;
            outline: none;
            selection-background-color: %6;
            selection-color: white;
        }
        
        QListWidget::item {
            padding: 12px 16px;
            border-bottom: 1px solid %4;
            background-color: transparent;
        }
        
        QListWidget::item:hover {
            background-color: %7;
        }
        
        QListWidget::item:selected {
            background-color: %6;
            color: white;
        }
        
        /* Search Box */
        QLineEdit {
            background-color: %7;
            border: 1px solid %4;
            border-radius: 8px;
            padding: 8px 12px;
            color: %2;
            font-size: %5px;
            margin: 8px;
        }
        
        QLineEdit:focus {
            border: 2px solid %6;
            background-color: %1;
        }
        
        /* Text Editor */
        QTextEdit {
            background-color: %9;
            border: none;
            color: %2;
            font-size: 16px;
            font-family: %10;
            line-height: 1.4;
            padding: 24px;
            selection-background-color: %6;
            selection-color: white;
        }
        
        /* Toolbar */
        QToolBar {
            background-color: %1;
            border: none;
            border-bottom: 1px solid %4;
            spacing: 4px;
            padding: 8px;
        }
        
        QToolBar QToolButton {
            background-color: transparent;
            border: 1px solid transparent;
            border-radius: 6px;
            padding: 6px 12px;
            color: %2;
            font-size: %5px;
        }
        
        QToolBar QToolButton:hover {
            background-color: %7;
            border-color: %4;
        }
        
        QToolBar QToolButton:pressed,
        QToolBar QToolButton:checked {
            background-color: %6;
            color: white;
        }
        
        /* Buttons */
        QPushButton {
            background-color: %6;
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            color: white;
            font-size: %5px;
            font-weight: 500;
        }
        
        QPushButton:hover {
            background-color: #0056b3;
        }
        
        QPushButton:pressed {
            background-color: #004494;
        }
        
        /* Splitter */
        QSplitter::handle {
            background-color: %4;
            width: 1px;
            height: 1px;
        }
        
        /* Labels */
        QLabel {
            color: %11;
            font-size: %12px;
        }
    )").arg(Colors::LIGHT_BACKGROUND)
       .arg(Colors::LIGHT_TEXT_COLOR)
       .arg(Colors::LIGHT_SIDEBAR_BACKGROUND)
       .arg(Colors::LIGHT_BORDER_COLOR)
       .arg(Fonts::BODY_FONT_SIZE)
       .arg(Colors::LIGHT_SELECTION_COLOR)
       .arg(Colors::LIGHT_HOVER_COLOR)
       .arg(Colors::LIGHT_NOTE_LIST_BACKGROUND)
       .arg(Colors::LIGHT_EDITOR_BACKGROUND)
       .arg(Fonts::PRIMARY_FONT_FAMILY)
       .arg(Colors::LIGHT_SECONDARY_TEXT)
       .arg(Fonts::SMALL_FONT_SIZE);
}

QString AppleNotesTheme::getDarkThemeStyleSheet() {
    return QString(R"(
        /* Main Window */
        QMainWindow {
            background-color: %1;
            color: %2;
        }
        
        /* Sidebar (Folder List) */
        QTreeWidget {
            background-color: %3;
            border: none;
            border-right: 1px solid %4;
            color: %2;
            font-size: %5px;
            outline: none;
            selection-background-color: %6;
            selection-color: white;
        }
        
        QTreeWidget::item {
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            margin: 2px 8px;
        }
        
        QTreeWidget::item:hover {
            background-color: %7;
        }
        
        QTreeWidget::item:selected {
            background-color: %6;
            color: white;
        }
        
        /* Notes List */
        QListWidget {
            background-color: %8;
            border: none;
            border-right: 1px solid %4;
            color: %2;
            font-size: %5px;
            outline: none;
            selection-background-color: %6;
            selection-color: white;
        }
        
        QListWidget::item {
            padding: 12px 16px;
            border-bottom: 1px solid %4;
            background-color: transparent;
        }
        
        QListWidget::item:hover {
            background-color: %7;
        }
        
        QListWidget::item:selected {
            background-color: %6;
            color: white;
        }
        
        /* Search Box */
        QLineEdit {
            background-color: %7;
            border: 1px solid %4;
            border-radius: 8px;
            padding: 8px 12px;
            color: %2;
            font-size: %5px;
            margin: 8px;
        }
        
        QLineEdit:focus {
            border: 2px solid %6;
            background-color: %1;
        }
        
        /* Text Editor */
        QTextEdit {
            background-color: %9;
            border: none;
            color: %2;
            font-size: 16px;
            font-family: %10;
            line-height: 1.4;
            padding: 24px;
            selection-background-color: %6;
            selection-color: white;
        }
        
        /* Toolbar */
        QToolBar {
            background-color: %1;
            border: none;
            border-bottom: 1px solid %4;
            spacing: 4px;
            padding: 8px;
        }
        
        QToolBar QToolButton {
            background-color: transparent;
            border: 1px solid transparent;
            border-radius: 6px;
            padding: 6px 12px;
            color: %2;
            font-size: %5px;
        }
        
        QToolBar QToolButton:hover {
            background-color: %7;
            border-color: %4;
        }
        
        QToolBar QToolButton:pressed,
        QToolBar QToolButton:checked {
            background-color: %6;
            color: white;
        }
        
        /* Buttons */
        QPushButton {
            background-color: %6;
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            color: white;
            font-size: %5px;
            font-weight: 500;
        }
        
        QPushButton:hover {
            background-color: #1a8cff;
        }
        
        QPushButton:pressed {
            background-color: #0066cc;
        }
        
        /* Splitter */
        QSplitter::handle {
            background-color: %4;
            width: 1px;
            height: 1px;
        }
        
        /* Labels */
        QLabel {
            color: %11;
            font-size: %12px;
        }
    )").arg(Colors::DARK_BACKGROUND)
       .arg(Colors::DARK_TEXT_COLOR)
       .arg(Colors::DARK_SIDEBAR_BACKGROUND)
       .arg(Colors::DARK_BORDER_COLOR)
       .arg(Fonts::BODY_FONT_SIZE)
       .arg(Colors::DARK_SELECTION_COLOR)
       .arg(Colors::DARK_HOVER_COLOR)
       .arg(Colors::DARK_NOTE_LIST_BACKGROUND)
       .arg(Colors::DARK_EDITOR_BACKGROUND)
       .arg(Fonts::PRIMARY_FONT_FAMILY)
       .arg(Colors::DARK_SECONDARY_TEXT)
       .arg(Fonts::SMALL_FONT_SIZE);
}

void AppleNotesTheme::applyAppleNotesStyle(QWidget* widget, bool isDark) {
    if (isDark) {
        widget->setStyleSheet(getDarkThemeStyleSheet());
    } else {
        widget->setStyleSheet(getLightThemeStyleSheet());
    }
}
