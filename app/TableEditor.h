#pragma once
#include <QDialog>
#include <QTextTable>

class QSpinBox;
class QTableWidget;
class QPushButton;
class QComboBox;
class QColorDialog;
class QTextEdit;

class TableEditor : public QDialog {
    Q_OBJECT

public:
    explicit TableEditor(QTextTable *table = nullptr, QWidget *parent = nullptr);
    
    // Table creation
    static QTextTable* createTable(QTextEdit *editor, int rows, int columns);
    
    // Table properties
    void setTableProperties(QTextTable *table);
    QTextTable* getEditedTable() const { return m_table; }

private slots:
    void insertRowAbove();
    void insertRowBelow();
    void deleteRow();
    void insertColumnLeft();
    void insertColumnRight();
    void deleteColumn();
    void mergeCells();
    void splitCell();
    void setTableBorderWidth();
    void setTableBorderColor();
    void setCellBackgroundColor();
    void setTableAlignment();
    void applyTableStyle();
    void onCellSelectionChanged();

private:
    void setupUI();
    void setupTableControls();
    void setupStyleControls();
    void updateTablePreview();
    void updateControlStates();
    
    // Table editing helpers
    void insertRow(int position);
    void insertColumn(int position);
    void removeRow(int row);
    void removeColumn(int column);
    QTextTableCell getCurrentCell();
    QList<QTextTableCell> getSelectedCells();
    
    QTextTable *m_table;
    QTableWidget *m_previewTable;
    
    // Controls
    QSpinBox *m_rowsSpinBox;
    QSpinBox *m_columnsSpinBox;
    QPushButton *m_insertRowAboveBtn;
    QPushButton *m_insertRowBelowBtn;
    QPushButton *m_deleteRowBtn;
    QPushButton *m_insertColumnLeftBtn;
    QPushButton *m_insertColumnRightBtn;
    QPushButton *m_deleteColumnBtn;
    QPushButton *m_mergeCellsBtn;
    QPushButton *m_splitCellBtn;
    
    // Style controls
    QSpinBox *m_borderWidthSpinBox;
    QPushButton *m_borderColorBtn;
    QPushButton *m_cellBackgroundBtn;
    QComboBox *m_alignmentCombo;
    QComboBox *m_styleCombo;
    
    QColor m_borderColor;
    QColor m_cellBackgroundColor;
};
