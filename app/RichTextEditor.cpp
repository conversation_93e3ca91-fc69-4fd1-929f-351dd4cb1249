#include "RichTextEditor.h"
#include <QAction>
#include <QToolBar>
#include <QFontComboBox>
#include <QSpinBox>
#include <QColorDialog>
#include <QTextList>
#include <QTextCursor>
#include <QTextCharFormat>
#include <QTextBlockFormat>
#include <QApplication>
#include <QIcon>
#include <QFileDialog>
#include <QInputDialog>
#include <QTextTable>
#include <QTextTableFormat>
#include <QTextImageFormat>
#include <QKeyEvent>
#include <QMimeData>
#include <QDir>
#include <QTemporaryDir>

RichTextEditor::RichTextEditor(QWidget *parent)
    : QTextEdit(parent), simpleFormat(false), autoFormatting(true) {
    setupActions();

    connect(this, &QTextEdit::currentCharFormatChanged,
            this, &RichTextEditor::currentCharFormatChanged);
    connect(this, &QTextEdit::cursorPositionChanged,
            this, &RichTextEditor::cursorPositionChanged);
    connect(this, &QTextEdit::textChanged,
            this, &RichTextEditor::onTextChanged);

    // Set Apple Notes-like default font and styling
    QFont defaultFont("SF Pro Text", 16);
    if (!defaultFont.exactMatch()) {
        defaultFont = QFont("system-ui", 16);
    }
    setFont(defaultFont);
    setFontPointSize(16);

    // Enable rich text and set line spacing
    setAcceptRichText(true);
    document()->setDefaultStyleSheet("p { line-height: 1.4; margin: 8px 0; }");
}

void RichTextEditor::setupActions() {
    boldAction = new QAction("B", this);
    boldAction->setCheckable(true);
    boldAction->setShortcut(QKeySequence::Bold);
    boldAction->setToolTip("Bold (Ctrl+B)");
    connect(boldAction, &QAction::toggled, this, &RichTextEditor::setBold);

    italicAction = new QAction("I", this);
    italicAction->setCheckable(true);
    italicAction->setShortcut(QKeySequence::Italic);
    italicAction->setToolTip("Italic (Ctrl+I)");
    connect(italicAction, &QAction::toggled, this, &RichTextEditor::setItalic);

    underlineAction = new QAction("U", this);
    underlineAction->setCheckable(true);
    underlineAction->setShortcut(QKeySequence::Underline);
    underlineAction->setToolTip("Underline (Ctrl+U)");
    connect(underlineAction, &QAction::toggled, this, &RichTextEditor::setUnderline);

    bulletListAction = new QAction("• List", this);
    bulletListAction->setToolTip("Bullet List");
    connect(bulletListAction, &QAction::triggered, this, &RichTextEditor::insertBulletList);

    numberedListAction = new QAction("1. List", this);
    numberedListAction->setToolTip("Numbered List");
    connect(numberedListAction, &QAction::triggered, this, &RichTextEditor::insertNumberedList);

    alignLeftAction = new QAction("⬅", this);
    alignLeftAction->setCheckable(true);
    alignLeftAction->setToolTip("Align Left");
    connect(alignLeftAction, &QAction::triggered, this, &RichTextEditor::alignLeft);

    alignCenterAction = new QAction("⬌", this);
    alignCenterAction->setCheckable(true);
    alignCenterAction->setToolTip("Align Center");
    connect(alignCenterAction, &QAction::triggered, this, &RichTextEditor::alignCenter);

    alignRightAction = new QAction("➡", this);
    alignRightAction->setCheckable(true);
    alignRightAction->setToolTip("Align Right");
    connect(alignRightAction, &QAction::triggered, this, &RichTextEditor::alignRight);

    textColorAction = new QAction("A", this);
    textColorAction->setToolTip("Text Color");
    connect(textColorAction, &QAction::triggered, this, &RichTextEditor::setTextColor);

    fontComboBox = new QFontComboBox;
    fontComboBox->setCurrentFont(QFont("System"));
    connect(fontComboBox, &QFontComboBox::currentFontChanged,
            this, [this](const QFont &font) {
                setFontFamily(font.family());
            });

    fontSizeSpinBox = new QSpinBox;
    fontSizeSpinBox->setRange(8, 72);
    fontSizeSpinBox->setValue(14);
    fontSizeSpinBox->setSuffix(" pt");
    connect(fontSizeSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &RichTextEditor::setFontSize);
}

QToolBar* RichTextEditor::createToolBar() {
    QToolBar *toolBar = new QToolBar("Formatting");
    
    if (!simpleFormat) {
        toolBar->addWidget(fontComboBox);
        toolBar->addWidget(fontSizeSpinBox);
        toolBar->addSeparator();
    }
    
    toolBar->addAction(boldAction);
    toolBar->addAction(italicAction);
    toolBar->addAction(underlineAction);
    
    if (!simpleFormat) {
        toolBar->addSeparator();
        toolBar->addAction(textColorAction);
        toolBar->addSeparator();
        toolBar->addAction(bulletListAction);
        toolBar->addAction(numberedListAction);
        toolBar->addSeparator();
        toolBar->addAction(alignLeftAction);
        toolBar->addAction(alignCenterAction);
        toolBar->addAction(alignRightAction);
    }
    
    return toolBar;
}

void RichTextEditor::setSimpleFormat(bool simple) {
    simpleFormat = simple;
}

void RichTextEditor::setBold(bool bold) {
    QTextCharFormat format;
    format.setFontWeight(bold ? QFont::Bold : QFont::Normal);
    mergeCurrentCharFormat(format);
}

void RichTextEditor::setItalic(bool italic) {
    QTextCharFormat format;
    format.setFontItalic(italic);
    mergeCurrentCharFormat(format);
}

void RichTextEditor::setUnderline(bool underline) {
    QTextCharFormat format;
    format.setFontUnderline(underline);
    mergeCurrentCharFormat(format);
}

void RichTextEditor::setFontFamily(const QString &family) {
    QTextCharFormat format;
    format.setFontFamilies({family});
    mergeCurrentCharFormat(format);
}

void RichTextEditor::setFontSize(int size) {
    QTextCharFormat format;
    format.setFontPointSize(size);
    mergeCurrentCharFormat(format);
}

void RichTextEditor::setTextColor() {
    QColor color = QColorDialog::getColor(textColor(), this, "Select Text Color");
    if (color.isValid()) {
        QTextCharFormat format;
        format.setForeground(color);
        mergeCurrentCharFormat(format);
    }
}

void RichTextEditor::setTextBackgroundColor() {
    QColor color = QColorDialog::getColor(Qt::white, this, "Select Background Color");
    if (color.isValid()) {
        QTextCharFormat format;
        format.setBackground(color);
        mergeCurrentCharFormat(format);
    }
}

void RichTextEditor::setStrikethrough(bool strikethrough) {
    QTextCharFormat format;
    format.setFontStrikeOut(strikethrough);
    mergeCurrentCharFormat(format);
}

void RichTextEditor::insertCheckboxList() {
    QTextCursor cursor = textCursor();
    cursor.insertText("☐ ");
}

void RichTextEditor::insertTable() {
    QTextCursor cursor = textCursor();
    QTextTableFormat tableFormat;
    tableFormat.setBorder(1);
    tableFormat.setCellPadding(4);
    tableFormat.setCellSpacing(0);
    cursor.insertTable(3, 3, tableFormat);
}

void RichTextEditor::insertImageDialog() {
    QString fileName = QFileDialog::getOpenFileName(this, "Insert Image",
                                                   QString(),
                                                   "Images (*.png *.jpg *.jpeg *.gif *.bmp)");
    if (!fileName.isEmpty()) {
        insertImage(fileName);
    }
}

void RichTextEditor::insertLinkDialog() {
    bool ok;
    QString url = QInputDialog::getText(this, "Insert Link", "URL:", QLineEdit::Normal, "", &ok);
    if (ok && !url.isEmpty()) {
        QString text = QInputDialog::getText(this, "Insert Link", "Link Text:", QLineEdit::Normal, url, &ok);
        if (ok) {
            insertLink(url, text.isEmpty() ? url : text);
        }
    }
}

void RichTextEditor::toggleHeading1() {
    setHeadingLevel(1);
}

void RichTextEditor::toggleHeading2() {
    setHeadingLevel(2);
}

void RichTextEditor::toggleHeading3() {
    setHeadingLevel(3);
}

void RichTextEditor::toggleCodeBlock() {
    insertCodeBlock();
}

void RichTextEditor::toggleQuote() {
    insertQuote();
}

void RichTextEditor::clearFormatting() {
    QTextCursor cursor = textCursor();
    QTextCharFormat format;
    cursor.setCharFormat(format);
    setTextCursor(cursor);
}

void RichTextEditor::insertTable(int rows, int cols) {
    QTextCursor cursor = textCursor();
    QTextTableFormat tableFormat;
    tableFormat.setBorder(1);
    tableFormat.setCellPadding(4);
    tableFormat.setCellSpacing(0);
    cursor.insertTable(rows, cols, tableFormat);
}

void RichTextEditor::insertImage(const QString &imagePath) {
    QTextCursor cursor = textCursor();
    QTextImageFormat imageFormat;
    imageFormat.setName(imagePath);
    imageFormat.setWidth(300); // Default width
    cursor.insertImage(imageFormat);
}

void RichTextEditor::insertLink(const QString &url, const QString &text) {
    QTextCursor cursor = textCursor();
    QTextCharFormat linkFormat;
    linkFormat.setAnchor(true);
    linkFormat.setAnchorHref(url);
    linkFormat.setForeground(QColor(0, 122, 255)); // Apple blue
    linkFormat.setUnderlineStyle(QTextCharFormat::SingleUnderline);

    cursor.insertText(text, linkFormat);
}

void RichTextEditor::setHeadingLevel(int level) {
    QTextCursor cursor = textCursor();
    QTextBlockFormat blockFormat = cursor.blockFormat();
    QTextCharFormat charFormat = cursor.charFormat();

    // Reset to normal first
    blockFormat.setHeadingLevel(0);
    charFormat.setFontWeight(QFont::Normal);
    charFormat.setFontPointSize(16);

    if (level > 0) {
        blockFormat.setHeadingLevel(level);
        charFormat.setFontWeight(QFont::Bold);

        switch (level) {
        case 1:
            charFormat.setFontPointSize(24);
            break;
        case 2:
            charFormat.setFontPointSize(20);
            break;
        case 3:
            charFormat.setFontPointSize(18);
            break;
        }
    }

    cursor.setBlockFormat(blockFormat);
    cursor.setCharFormat(charFormat);
    setTextCursor(cursor);
}

void RichTextEditor::insertCheckbox() {
    QTextCursor cursor = textCursor();
    cursor.insertText("☐ ");
}

void RichTextEditor::insertHorizontalRule() {
    QTextCursor cursor = textCursor();
    cursor.insertText("\n" + QString(50, '─') + "\n");
}

void RichTextEditor::increaseIndent() {
    QTextCursor cursor = textCursor();
    QTextBlockFormat format = cursor.blockFormat();
    format.setIndent(format.indent() + 1);
    cursor.setBlockFormat(format);
}

void RichTextEditor::decreaseIndent() {
    QTextCursor cursor = textCursor();
    QTextBlockFormat format = cursor.blockFormat();
    if (format.indent() > 0) {
        format.setIndent(format.indent() - 1);
        cursor.setBlockFormat(format);
    }
}

void RichTextEditor::insertCodeBlock() {
    QTextCursor cursor = textCursor();
    QTextCharFormat format;
    format.setFontFamily("Monaco, Consolas, monospace");
    format.setBackground(QColor(245, 245, 245));
    format.setForeground(QColor(51, 51, 51));

    cursor.insertText("```\n", format);
    cursor.insertText("Code here\n", format);
    cursor.insertText("```\n", format);
}

void RichTextEditor::insertQuote() {
    QTextCursor cursor = textCursor();
    QTextBlockFormat blockFormat = cursor.blockFormat();
    blockFormat.setLeftMargin(20);
    blockFormat.setBackground(QColor(248, 248, 248));

    QTextCharFormat charFormat;
    charFormat.setForeground(QColor(102, 102, 102));
    charFormat.setFontItalic(true);

    cursor.setBlockFormat(blockFormat);
    cursor.insertText("> ", charFormat);
}

void RichTextEditor::onTextChanged() {
    if (autoFormatting) {
        handleSmartFormatting();
    }
}

void RichTextEditor::keyPressEvent(QKeyEvent *event) {
    // Handle special key combinations for Apple Notes-like behavior
    if (event->key() == Qt::Key_Return || event->key() == Qt::Key_Enter) {
        QTextCursor cursor = textCursor();
        QString currentLine = cursor.block().text();

        // Auto-continue lists
        if (currentLine.startsWith("• ") || currentLine.startsWith("- ")) {
            QTextEdit::keyPressEvent(event);
            cursor = textCursor();
            cursor.insertText("• ");
            return;
        } else if (currentLine.startsWith("☐ ")) {
            QTextEdit::keyPressEvent(event);
            cursor = textCursor();
            cursor.insertText("☐ ");
            return;
        }
    }

    QTextEdit::keyPressEvent(event);
}

void RichTextEditor::insertFromMimeData(const QMimeData *source) {
    // Handle drag and drop of images
    if (source->hasImage()) {
        QImage image = qvariant_cast<QImage>(source->imageData());
        QTextCursor cursor = textCursor();

        // Save image to temp location and insert
        QString tempPath = QDir::temp().filePath("knote_image.png");
        if (image.save(tempPath)) {
            insertImage(tempPath);
        }
        return;
    }

    QTextEdit::insertFromMimeData(source);
}

void RichTextEditor::handleSmartFormatting() {
    QTextCursor cursor = textCursor();
    QString text = cursor.block().text();

    // Auto-format headings
    if (text.startsWith("# ")) {
        cursor.select(QTextCursor::BlockUnderCursor);
        cursor.removeSelectedText();
        cursor.insertText(text.mid(2));
        setHeadingLevel(1);
    } else if (text.startsWith("## ")) {
        cursor.select(QTextCursor::BlockUnderCursor);
        cursor.removeSelectedText();
        cursor.insertText(text.mid(3));
        setHeadingLevel(2);
    } else if (text.startsWith("### ")) {
        cursor.select(QTextCursor::BlockUnderCursor);
        cursor.removeSelectedText();
        cursor.insertText(text.mid(4));
        setHeadingLevel(3);
    }
}

void RichTextEditor::insertBulletList() {
    QTextCursor cursor = textCursor();
    QTextListFormat listFormat;
    listFormat.setStyle(QTextListFormat::ListDisc);
    cursor.insertList(listFormat);
}

void RichTextEditor::insertNumberedList() {
    QTextCursor cursor = textCursor();
    QTextListFormat listFormat;
    listFormat.setStyle(QTextListFormat::ListDecimal);
    cursor.insertList(listFormat);
}

void RichTextEditor::alignLeft() {
    setAlignment(Qt::AlignLeft);
    updateActions();
}

void RichTextEditor::alignCenter() {
    setAlignment(Qt::AlignCenter);
    updateActions();
}

void RichTextEditor::alignRight() {
    setAlignment(Qt::AlignRight);
    updateActions();
}

void RichTextEditor::alignJustify() {
    setAlignment(Qt::AlignJustify);
    updateActions();
}

void RichTextEditor::currentCharFormatChanged(const QTextCharFormat &format) {
    boldAction->setChecked(format.fontWeight() == QFont::Bold);
    italicAction->setChecked(format.fontItalic());
    underlineAction->setChecked(format.fontUnderline());
    
    if (!simpleFormat) {
        fontComboBox->setCurrentFont(format.font());
        fontSizeSpinBox->setValue(format.fontPointSize());
    }
}

void RichTextEditor::cursorPositionChanged() {
    updateActions();
}

void RichTextEditor::updateActions() {
    Qt::Alignment align = alignment();
    alignLeftAction->setChecked(align & Qt::AlignLeft);
    alignCenterAction->setChecked(align & Qt::AlignCenter);
    alignRightAction->setChecked(align & Qt::AlignRight);
}
