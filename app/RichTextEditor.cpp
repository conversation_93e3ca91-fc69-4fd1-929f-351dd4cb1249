#include "RichTextEditor.h"
#include <QAction>
#include <QToolBar>
#include <QFontComboBox>
#include <QSpinBox>
#include <QColorDialog>
#include <QTextList>
#include <QTextCursor>
#include <QTextCharFormat>
#include <QTextBlockFormat>
#include <QApplication>
#include <QIcon>

RichTextEditor::RichTextEditor(QWidget *parent)
    : QTextEdit(parent), simpleFormat(false) {
    setupActions();
    
    connect(this, &QTextEdit::currentCharFormatChanged,
            this, &RichTextEditor::currentCharFormatChanged);
    connect(this, &QTextEdit::cursorPositionChanged,
            this, &RichTextEditor::cursorPositionChanged);
    
    // Set default font
    QFont font("System", 14);
    setFont(font);
    setFontPointSize(14);
}

void RichTextEditor::setupActions() {
    boldAction = new QAction("B", this);
    boldAction->setCheckable(true);
    boldAction->setShortcut(QKeySequence::Bold);
    boldAction->setToolTip("Bold (Ctrl+B)");
    connect(boldAction, &QAction::toggled, this, &RichTextEditor::setBold);

    italicAction = new QAction("I", this);
    italicAction->setCheckable(true);
    italicAction->setShortcut(QKeySequence::Italic);
    italicAction->setToolTip("Italic (Ctrl+I)");
    connect(italicAction, &QAction::toggled, this, &RichTextEditor::setItalic);

    underlineAction = new QAction("U", this);
    underlineAction->setCheckable(true);
    underlineAction->setShortcut(QKeySequence::Underline);
    underlineAction->setToolTip("Underline (Ctrl+U)");
    connect(underlineAction, &QAction::toggled, this, &RichTextEditor::setUnderline);

    bulletListAction = new QAction("• List", this);
    bulletListAction->setToolTip("Bullet List");
    connect(bulletListAction, &QAction::triggered, this, &RichTextEditor::insertBulletList);

    numberedListAction = new QAction("1. List", this);
    numberedListAction->setToolTip("Numbered List");
    connect(numberedListAction, &QAction::triggered, this, &RichTextEditor::insertNumberedList);

    alignLeftAction = new QAction("⬅", this);
    alignLeftAction->setCheckable(true);
    alignLeftAction->setToolTip("Align Left");
    connect(alignLeftAction, &QAction::triggered, this, &RichTextEditor::alignLeft);

    alignCenterAction = new QAction("⬌", this);
    alignCenterAction->setCheckable(true);
    alignCenterAction->setToolTip("Align Center");
    connect(alignCenterAction, &QAction::triggered, this, &RichTextEditor::alignCenter);

    alignRightAction = new QAction("➡", this);
    alignRightAction->setCheckable(true);
    alignRightAction->setToolTip("Align Right");
    connect(alignRightAction, &QAction::triggered, this, &RichTextEditor::alignRight);

    textColorAction = new QAction("A", this);
    textColorAction->setToolTip("Text Color");
    connect(textColorAction, &QAction::triggered, this, &RichTextEditor::setTextColor);

    fontComboBox = new QFontComboBox;
    fontComboBox->setCurrentFont(QFont("System"));
    connect(fontComboBox, &QFontComboBox::currentFontChanged,
            this, [this](const QFont &font) {
                setFontFamily(font.family());
            });

    fontSizeSpinBox = new QSpinBox;
    fontSizeSpinBox->setRange(8, 72);
    fontSizeSpinBox->setValue(14);
    fontSizeSpinBox->setSuffix(" pt");
    connect(fontSizeSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &RichTextEditor::setFontSize);
}

QToolBar* RichTextEditor::createToolBar() {
    QToolBar *toolBar = new QToolBar("Formatting");
    
    if (!simpleFormat) {
        toolBar->addWidget(fontComboBox);
        toolBar->addWidget(fontSizeSpinBox);
        toolBar->addSeparator();
    }
    
    toolBar->addAction(boldAction);
    toolBar->addAction(italicAction);
    toolBar->addAction(underlineAction);
    
    if (!simpleFormat) {
        toolBar->addSeparator();
        toolBar->addAction(textColorAction);
        toolBar->addSeparator();
        toolBar->addAction(bulletListAction);
        toolBar->addAction(numberedListAction);
        toolBar->addSeparator();
        toolBar->addAction(alignLeftAction);
        toolBar->addAction(alignCenterAction);
        toolBar->addAction(alignRightAction);
    }
    
    return toolBar;
}

void RichTextEditor::setSimpleFormat(bool simple) {
    simpleFormat = simple;
}

void RichTextEditor::setBold(bool bold) {
    QTextCharFormat format;
    format.setFontWeight(bold ? QFont::Bold : QFont::Normal);
    mergeCurrentCharFormat(format);
}

void RichTextEditor::setItalic(bool italic) {
    QTextCharFormat format;
    format.setFontItalic(italic);
    mergeCurrentCharFormat(format);
}

void RichTextEditor::setUnderline(bool underline) {
    QTextCharFormat format;
    format.setFontUnderline(underline);
    mergeCurrentCharFormat(format);
}

void RichTextEditor::setFontFamily(const QString &family) {
    QTextCharFormat format;
    format.setFontFamilies({family});
    mergeCurrentCharFormat(format);
}

void RichTextEditor::setFontSize(int size) {
    QTextCharFormat format;
    format.setFontPointSize(size);
    mergeCurrentCharFormat(format);
}

void RichTextEditor::setTextColor() {
    QColor color = QColorDialog::getColor(textColor(), this, "Select Text Color");
    if (color.isValid()) {
        QTextCharFormat format;
        format.setForeground(color);
        mergeCurrentCharFormat(format);
    }
}

void RichTextEditor::setTextBackgroundColor() {
    QColor color = QColorDialog::getColor(Qt::white, this, "Select Background Color");
    if (color.isValid()) {
        QTextCharFormat format;
        format.setBackground(color);
        mergeCurrentCharFormat(format);
    }
}

void RichTextEditor::insertBulletList() {
    QTextCursor cursor = textCursor();
    QTextListFormat listFormat;
    listFormat.setStyle(QTextListFormat::ListDisc);
    cursor.insertList(listFormat);
}

void RichTextEditor::insertNumberedList() {
    QTextCursor cursor = textCursor();
    QTextListFormat listFormat;
    listFormat.setStyle(QTextListFormat::ListDecimal);
    cursor.insertList(listFormat);
}

void RichTextEditor::alignLeft() {
    setAlignment(Qt::AlignLeft);
    updateActions();
}

void RichTextEditor::alignCenter() {
    setAlignment(Qt::AlignCenter);
    updateActions();
}

void RichTextEditor::alignRight() {
    setAlignment(Qt::AlignRight);
    updateActions();
}

void RichTextEditor::alignJustify() {
    setAlignment(Qt::AlignJustify);
    updateActions();
}

void RichTextEditor::currentCharFormatChanged(const QTextCharFormat &format) {
    boldAction->setChecked(format.fontWeight() == QFont::Bold);
    italicAction->setChecked(format.fontItalic());
    underlineAction->setChecked(format.fontUnderline());
    
    if (!simpleFormat) {
        fontComboBox->setCurrentFont(format.font());
        fontSizeSpinBox->setValue(format.fontPointSize());
    }
}

void RichTextEditor::cursorPositionChanged() {
    updateActions();
}

void RichTextEditor::updateActions() {
    Qt::Alignment align = alignment();
    alignLeftAction->setChecked(align & Qt::AlignLeft);
    alignCenterAction->setChecked(align & Qt::AlignCenter);
    alignRightAction->setChecked(align & Qt::AlignRight);
}
