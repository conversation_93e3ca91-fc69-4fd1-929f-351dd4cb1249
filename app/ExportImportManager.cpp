#include "ExportImportManager.h"
#include <QWidget>
#include <QProgressDialog>
#include <QFileDialog>
#include <QMessageBox>
#include <QTextDocument>
#include <QPrinter>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDir>
#include <QTextStream>
#include <QApplication>
#include <QRegularExpression>
#include <QFileInfo>

ExportImportManager::ExportImportManager(QWidget *parent)
    : QObject(parent), m_parent(parent), m_progressDialog(nullptr),
      m_currentProgress(0), m_totalProgress(0) {
}

bool ExportImportManager::exportNote(const QString &title, const QString &content,
                                    const QString &filePath, ExportFormat format) {
    try {
        switch (format) {
        case PDF:
            return generatePDF(title, content, filePath);
        case HTML:
            return saveToFile(filePath, convertToHTML(content));
        case Markdown:
            return saveToFile(filePath, convertToMarkdown(content));
        case PlainText:
            return saveToFile(filePath, convertToPlainText(content));
        case JSON:
            return saveToFile(filePath, generateJSON(title, content, QDateTime::currentDateTime(), ""));
        default:
            emit errorOccurred("Unsupported export format");
            return false;
        }
    } catch (const std::exception &e) {
        emit errorOccurred(QString("Export failed: %1").arg(e.what()));
        return false;
    }
}

bool ExportImportManager::generatePDF(const QString &title, const QString &content, const QString &filePath) {
    QTextDocument document;
    document.setHtml(QString("<h1>%1</h1>%2").arg(title, content));
    
    QPrinter printer(QPrinter::HighResolution);
    printer.setOutputFormat(QPrinter::PdfFormat);
    printer.setOutputFileName(filePath);
    printer.setPageMargins(QMarginsF(15, 15, 15, 15), QPageLayout::Millimeter);
    
    document.print(&printer);
    return true;
}

QString ExportImportManager::convertToHTML(const QString &content) {
    return QString("<!DOCTYPE html>\n<html>\n<head>\n<meta charset=\"UTF-8\">\n"
                  "<title>Exported Note</title>\n</head>\n<body>\n%1\n</body>\n</html>").arg(content);
}

QString ExportImportManager::convertToMarkdown(const QString &content) {
    // Basic HTML to Markdown conversion
    QString markdown = content;
    markdown.replace(QRegularExpression("<h1[^>]*>(.*?)</h1>"), "# \\1\n");
    markdown.replace(QRegularExpression("<h2[^>]*>(.*?)</h2>"), "## \\1\n");
    markdown.replace(QRegularExpression("<h3[^>]*>(.*?)</h3>"), "### \\1\n");
    markdown.replace(QRegularExpression("<b[^>]*>(.*?)</b>"), "**\\1**");
    markdown.replace(QRegularExpression("<strong[^>]*>(.*?)</strong>"), "**\\1**");
    markdown.replace(QRegularExpression("<i[^>]*>(.*?)</i>"), "*\\1*");
    markdown.replace(QRegularExpression("<em[^>]*>(.*?)</em>"), "*\\1*");
    markdown.replace(QRegularExpression("<br[^>]*>"), "\n");
    markdown.replace(QRegularExpression("<p[^>]*>"), "");
    markdown.replace("</p>", "\n\n");
    markdown.replace(QRegularExpression("<[^>]*>"), ""); // Remove remaining HTML tags
    return markdown;
}

QString ExportImportManager::convertToPlainText(const QString &content) {
    QTextDocument doc;
    doc.setHtml(content);
    return doc.toPlainText();
}

QString ExportImportManager::generateJSON(const QString &title, const QString &content,
                                        const QDateTime &date, const QString &folder) {
    QJsonObject noteObject;
    noteObject["title"] = title;
    noteObject["content"] = content;
    noteObject["date"] = date.toString(Qt::ISODate);
    noteObject["folder"] = folder;
    noteObject["version"] = "1.0";
    
    QJsonDocument doc(noteObject);
    return doc.toJson();
}

bool ExportImportManager::saveToFile(const QString &filePath, const QString &content) {
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit errorOccurred(QString("Cannot write to file: %1").arg(filePath));
        return false;
    }
    
    QTextStream out(&file);
    out.setEncoding(QStringConverter::Utf8);
    out << content;
    return true;
}

bool ExportImportManager::exportAllNotes(const QString &directoryPath, ExportFormat format) {
    // This would integrate with the main application's note storage
    // For now, this is a placeholder implementation
    emit exportCompleted("Export completed successfully");
    return true;
}

QString ExportImportManager::getFileExtension(ExportFormat format) {
    switch (format) {
    case PDF: return ".pdf";
    case HTML: return ".html";
    case Markdown: return ".md";
    case PlainText: return ".txt";
    case JSON: return ".json";
    default: return ".txt";
    }
}

QString ExportImportManager::getFormatName(ExportFormat format) {
    switch (format) {
    case PDF: return "PDF Document";
    case HTML: return "HTML Document";
    case Markdown: return "Markdown";
    case PlainText: return "Plain Text";
    case JSON: return "JSON";
    default: return "Unknown";
    }
}

QStringList ExportImportManager::getSupportedExportFormats() {
    return {"PDF Document", "HTML Document", "Markdown", "Plain Text", "JSON"};
}

QStringList ExportImportManager::getSupportedImportFormats() {
    return {"JSON", "Markdown", "Plain Text"};
}

bool ExportImportManager::importNotes(const QString &filePath, ImportFormat format) {
    try {
        QList<QVariantMap> notes;
        
        switch (format) {
        case JSONFormat:
            notes = parseJSONFile(filePath);
            break;
        case MarkdownFormat:
            notes = parseMarkdownFile(filePath);
            break;
        case PlainTextFormat:
            notes = parseMarkdownFile(filePath); // Treat as markdown
            break;
        default:
            emit errorOccurred("Unsupported import format");
            return false;
        }
        
        if (notes.isEmpty()) {
            emit errorOccurred("No notes found in the file");
            return false;
        }
        
        // Here you would integrate with the main application to save the notes
        emit importCompleted(QString("Successfully imported %1 notes").arg(notes.size()));
        return true;
        
    } catch (const std::exception &e) {
        emit errorOccurred(QString("Import failed: %1").arg(e.what()));
        return false;
    }
}

QList<QVariantMap> ExportImportManager::parseJSONFile(const QString &filePath) {
    QList<QVariantMap> notes;
    
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        throw std::runtime_error("Cannot read file");
    }
    
    QByteArray data = file.readAll();
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    
    if (error.error != QJsonParseError::NoError) {
        throw std::runtime_error("Invalid JSON format");
    }
    
    if (doc.isArray()) {
        QJsonArray array = doc.array();
        for (const QJsonValue &value : array) {
            if (value.isObject()) {
                QVariantMap note = value.toObject().toVariantMap();
                notes.append(note);
            }
        }
    } else if (doc.isObject()) {
        QVariantMap note = doc.object().toVariantMap();
        notes.append(note);
    }
    
    return notes;
}

QList<QVariantMap> ExportImportManager::parseMarkdownFile(const QString &filePath) {
    QList<QVariantMap> notes;
    
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        throw std::runtime_error("Cannot read file");
    }
    
    QTextStream in(&file);
    QString content = in.readAll();
    
    // Extract title from first line or filename
    QString title = extractTitle(content);
    if (title.isEmpty()) {
        QFileInfo fileInfo(filePath);
        title = fileInfo.baseName();
    }
    
    QVariantMap note;
    note["title"] = title;
    note["content"] = content;
    note["date"] = QDateTime::currentDateTime();
    note["folder"] = "Imported";
    
    notes.append(note);
    return notes;
}

QString ExportImportManager::extractTitle(const QString &content) {
    QStringList lines = content.split('\n');
    for (const QString &line : lines) {
        QString trimmed = line.trimmed();
        if (trimmed.startsWith("# ")) {
            return trimmed.mid(2).trimmed();
        }
        if (!trimmed.isEmpty() && !trimmed.startsWith("#")) {
            return trimmed.left(50); // First 50 characters as title
        }
    }
    return QString();
}

void ExportImportManager::showProgressDialog(const QString &title, int maximum) {
    if (!m_progressDialog) {
        m_progressDialog = new QProgressDialog(m_parent);
        m_progressDialog->setWindowModality(Qt::WindowModal);
    }
    
    m_progressDialog->setLabelText(title);
    m_progressDialog->setMaximum(maximum);
    m_progressDialog->setValue(0);
    m_progressDialog->show();
}

void ExportImportManager::hideProgressDialog() {
    if (m_progressDialog) {
        m_progressDialog->hide();
    }
}
