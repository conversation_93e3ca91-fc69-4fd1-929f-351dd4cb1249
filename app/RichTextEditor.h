#pragma once
#include <QTextEdit>
#include <QToolBar>

class QAction;
class QFontComboBox;
class QSpinBox;
class QColorDialog;

class RichTextEditor : public QTextEdit {
    Q_OBJECT

public:
    explicit RichTextEditor(QWidget *parent = nullptr);

    QToolBar* createToolBar();
    void setSimpleFormat(bool simple);
    void insertTable(int rows, int cols);
    void insertImage(const QString &imagePath);
    void insertLink(const QString &url, const QString &text);
    void setHeadingLevel(int level);
    void insertCheckbox();
    void insertHorizontalRule();
    void increaseIndent();
    void decreaseIndent();
    void insertCodeBlock();
    void insertQuote();

public slots:
    void setBold(bool bold);
    void setItalic(bool italic);
    void setUnderline(bool underline);
    void setStrikethrough(bool strikethrough);
    void setFontFamily(const QString &family);
    void setFontSize(int size);
    void setTextColor();
    void setTextBackgroundColor();
    void insertBulletList();
    void insertNumberedList();
    void insertCheckboxList();
    void alignLeft();
    void alignCenter();
    void alignRight();
    void alignJustify();
    void insertTable();
    void insertImageDialog();
    void insertLinkDialog();
    void toggleHeading1();
    void toggleHeading2();
    void toggleHeading3();
    void toggleCodeBlock();
    void toggleQuote();
    void clearFormatting();

private slots:
    void currentCharFormatChanged(const QTextCharFormat &format);
    void cursorPositionChanged();
    void onTextChanged();

protected:
    void keyPressEvent(QKeyEvent *event) override;
    void insertFromMimeData(const QMimeData *source) override;

private:
    void setupActions();
    void updateActions();
    void applyHeading(int level);
    void insertListItem(QTextListFormat::Style style);
    void handleSmartFormatting();

    // Formatting actions
    QAction *boldAction;
    QAction *italicAction;
    QAction *underlineAction;
    QAction *strikethroughAction;
    QAction *bulletListAction;
    QAction *numberedListAction;
    QAction *checkboxListAction;
    QAction *alignLeftAction;
    QAction *alignCenterAction;
    QAction *alignRightAction;
    QAction *alignJustifyAction;
    QAction *textColorAction;
    QAction *backgroundColorAction;
    QAction *insertTableAction;
    QAction *insertImageAction;
    QAction *insertLinkAction;
    QAction *heading1Action;
    QAction *heading2Action;
    QAction *heading3Action;
    QAction *codeBlockAction;
    QAction *quoteAction;
    QAction *clearFormatAction;
    QAction *indentAction;
    QAction *outdentAction;

    QFontComboBox *fontComboBox;
    QSpinBox *fontSizeSpinBox;

    bool simpleFormat;
    bool autoFormatting;
};
