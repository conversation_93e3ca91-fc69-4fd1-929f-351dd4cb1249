#pragma once
#include <QTextEdit>
#include <QToolBar>

class QAction;
class QFontComboBox;
class QSpinBox;
class QColorDialog;

class RichTextEditor : public QTextEdit {
    Q_OBJECT

public:
    explicit RichTextEditor(QWidget *parent = nullptr);
    
    QToolBar* createToolBar();
    void setSimpleFormat(bool simple);

public slots:
    void setBold(bool bold);
    void setItalic(bool italic);
    void setUnderline(bool underline);
    void setFontFamily(const QString &family);
    void setFontSize(int size);
    void setTextColor();
    void setTextBackgroundColor();
    void insertBulletList();
    void insertNumberedList();
    void alignLeft();
    void alignCenter();
    void alignRight();
    void alignJustify();

private slots:
    void currentCharFormatChanged(const QTextCharFormat &format);
    void cursorPositionChanged();

private:
    void setupActions();
    void updateActions();

    QAction *boldAction;
    QAction *italicAction;
    QAction *underlineAction;
    QAction *bulletListAction;
    QAction *numberedListAction;
    QAction *alignLeftAction;
    QAction *alignCenterAction;
    QAction *alignRightAction;
    QAction *alignJustifyAction;
    QAction *textColorAction;
    QAction *backgroundColorAction;
    
    QFontComboBox *fontComboBox;
    QSpinBox *fontSizeSpinBox;
    
    bool simpleFormat;
};
