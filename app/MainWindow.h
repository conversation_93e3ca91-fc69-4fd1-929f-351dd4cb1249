#pragma once
#include <QMainWindow>
#include <QListWidgetItem>
#include <QTimer>

class QTabWidget;
class QListWidget;
class QTextEdit;
class QPushButton;
class QLineEdit;
class QSplitter;
class QStackedWidget;
class QLabel;
class QToolBar;
class QAction;
class QLineEdit;
class QTreeWidget;
class QTreeWidgetItem;
class QMenuBar;
class QMenu;
class RichTextEditor;
class SettingsDialog;
class FolderManager;
class ExportImportManager;

class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    MainWindow();

private slots:
    void addNote();
    void deleteNote();
    void saveCurrentNote();
    void onNoteSelectionChanged();
    void onSearchTextChanged();
    void onNoteDoubleClicked(QListWidgetItem* item);
    void showNotePreview();
    void updateNotePreview(QListWidgetItem* item);
    void highlightSearchResults(const QString &searchText);
    void showRecentSearches();
    void addToRecentSearches(const QString &searchText);

    void addFolder();
    void deleteFolder();
    void moveNoteToTrash();
    void emptyTrash();
    void restoreFromTrash();
    void exportNote();
    void exportAllNotes();
    void importNotes();
    void showSettings();
    void showAbout();

    void addTodo();
    void deleteTodo();
    void toggleTodoDone(QListWidgetItem* item);

    void applySystemTheme();
    void onFolderSelectionChanged();

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupNotesView();
    void setupTodosView();
    void setupSidebar();
    void loadNotes();
    void saveNotes();
    void loadTodos();
    void saveTodos();
    void loadFolders();
    void saveFolders();
    void updateNotesList();
    void applyTheme();
    void detectSystemTheme();
    void loadSettings();
    void saveSettings();

    // Main UI components
    QSplitter *mainSplitter;
    QStackedWidget *stack;
    QTreeWidget *folderList;
    QLineEdit *searchBox;
    QToolBar *toolBar;

    // Notes UI
    QListWidget *noteList;
    RichTextEditor *noteEditor;
    QLabel *noteCountLabel;
    QLabel *notePreviewLabel;
    QWidget *notePreviewWidget;
    QStringList recentSearches;

    // Actions and Menus
    QMenu *fileMenu;
    QMenu *editMenu;
    QMenu *viewMenu;
    QMenu *helpMenu;

    QAction *newNoteAction;
    QAction *deleteNoteAction;
    QAction *newFolderAction;
    QAction *searchAction;

    QAction *exportAction;
    QAction *importAction;
    QAction *settingsAction;
    QAction *aboutAction;

    // Todos UI
    QListWidget *todoList;
    QLineEdit *todoInput;
    QPushButton *addTodoButton;
    QPushButton *deleteTodoButton;

    // Data
    QString currentFolder;
    QTimer *saveTimer;
    bool isDarkTheme;
};
