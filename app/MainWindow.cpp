// Enhanced Apple Notes clone for Linux KDE/GNOME
#include "MainWindow.h"
#include "RichTextEditor.h"
#include "SettingsDialog.h"
#include "AppleNotesTheme.h"
#include <QApplication>
#include <QSplitter>
#include <QListWidget>
#include <QStackedWidget>
#include <QTextEdit>
#include <QLineEdit>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QStyleFactory>
#include <QSettings>
#include <QJsonDocument>
#include <QJsonArray>
#include <QJsonObject>
#include <QFile>
#include <QFrame>
#include <QToolBar>
#include <QAction>
#include <QIcon>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QHeaderView>
#include <QTimer>
#include <QPalette>
#include <QStyle>
#include <QStyleHints>
#include <QFont>
#include <QFontMetrics>
#include <QTextCharFormat>
#include <QTextCursor>
#include <QKeySequence>
#include <QShortcut>
#include <QMessageBox>
#include <QInputDialog>
#include <QDateTime>
#include <QDir>
#include <QStandardPaths>
#include <QMenuBar>
#include <QMenu>
#include <QFileDialog>
#include <QTextStream>
#include <QDesktopServices>
#include <QUrl>

MainWindow::MainWindow() : currentFolder("All Notes") {
    setWindowTitle("KNoteDo - Apple Notes for Linux");
    resize(1200, 800);
    setMinimumSize(800, 600);

    // Initialize timer for auto-save
    saveTimer = new QTimer(this);
    saveTimer->setSingleShot(true);
    saveTimer->setInterval(1000); // Auto-save after 1 second of inactivity
    connect(saveTimer, &QTimer::timeout, this, &MainWindow::saveCurrentNote);

    // Detect and apply system theme
    detectSystemTheme();
    applySystemTheme();

    // Setup UI components
    setupMenuBar();
    setupUI();
    setupToolBar();

    // Load settings and data
    loadSettings();
    loadFolders();
    loadNotes();
    loadTodos();

    // Set initial state
    updateNotesList();
}

void MainWindow::setupMenuBar() {
    // File menu
    fileMenu = menuBar()->addMenu("&File");

    newNoteAction = new QAction("&New Note", this);
    newNoteAction->setShortcut(QKeySequence::New);
    newNoteAction->setStatusTip("Create a new note");
    connect(newNoteAction, &QAction::triggered, this, &MainWindow::addNote);
    fileMenu->addAction(newNoteAction);

    newFolderAction = new QAction("New &Folder", this);
    newFolderAction->setShortcut(QKeySequence("Ctrl+Shift+N"));
    connect(newFolderAction, &QAction::triggered, this, &MainWindow::addFolder);
    fileMenu->addAction(newFolderAction);

    fileMenu->addSeparator();

    exportAction = new QAction("&Export Note...", this);
    exportAction->setShortcut(QKeySequence("Ctrl+E"));
    connect(exportAction, &QAction::triggered, this, &MainWindow::exportNote);
    fileMenu->addAction(exportAction);

    QAction *exportAllAction = new QAction("Export &All Notes...", this);
    connect(exportAllAction, &QAction::triggered, this, &MainWindow::exportAllNotes);
    fileMenu->addAction(exportAllAction);

    importAction = new QAction("&Import Notes...", this);
    connect(importAction, &QAction::triggered, this, &MainWindow::importNotes);
    fileMenu->addAction(importAction);

    fileMenu->addSeparator();

    QAction *quitAction = new QAction("&Quit", this);
    quitAction->setShortcut(QKeySequence::Quit);
    connect(quitAction, &QAction::triggered, this, &QWidget::close);
    fileMenu->addAction(quitAction);

    // Edit menu
    editMenu = menuBar()->addMenu("&Edit");

    deleteNoteAction = new QAction("&Delete Note", this);
    deleteNoteAction->setShortcut(QKeySequence::Delete);
    connect(deleteNoteAction, &QAction::triggered, this, &MainWindow::deleteNote);
    editMenu->addAction(deleteNoteAction);

    editMenu->addSeparator();

    settingsAction = new QAction("&Preferences...", this);
    settingsAction->setShortcut(QKeySequence::Preferences);
    connect(settingsAction, &QAction::triggered, this, &MainWindow::showSettings);
    editMenu->addAction(settingsAction);

    // View menu
    viewMenu = menuBar()->addMenu("&View");

    // Help menu
    helpMenu = menuBar()->addMenu("&Help");

    aboutAction = new QAction("&About KNoteDo", this);
    connect(aboutAction, &QAction::triggered, this, &MainWindow::showAbout);
    helpMenu->addAction(aboutAction);
}

void MainWindow::setupUI() {
    // Main splitter
    mainSplitter = new QSplitter(Qt::Horizontal, this);
    mainSplitter->setHandleWidth(1);

    // Setup sidebar and main content
    setupSidebar();

    // Right side - stacked widget for different views
    stack = new QStackedWidget;
    setupNotesView();
    setupTodosView();

    mainSplitter->addWidget(folderList);
    mainSplitter->addWidget(stack);
    mainSplitter->setSizes({250, 950});

    setCentralWidget(mainSplitter);

    // Connect signals
    connect(folderList, &QTreeWidget::currentItemChanged, this, &MainWindow::onFolderSelectionChanged);
}

void MainWindow::setupSidebar() {
    folderList = new QTreeWidget;
    folderList->setHeaderHidden(true);
    folderList->setMaximumWidth(300);
    folderList->setMinimumWidth(200);
    folderList->setRootIsDecorated(false);

    // Add default folders
    auto *allNotesItem = new QTreeWidgetItem(folderList, QStringList("📒 All Notes"));
    auto *tasksItem = new QTreeWidgetItem(folderList, QStringList("📋 Tasks"));
    auto *trashItem = new QTreeWidgetItem(folderList, QStringList("🗑️ Recently Deleted"));

    folderList->setCurrentItem(allNotesItem);
}

void MainWindow::setupNotesView() {
    auto *notesWidget = new QWidget;
    auto *mainLayout = new QHBoxLayout(notesWidget);
    mainLayout->setContentsMargins(0, 0, 0, 0);

    // Left panel - notes list
    auto *leftPanel = new QWidget;
    leftPanel->setMaximumWidth(350);
    leftPanel->setMinimumWidth(250);
    auto *leftLayout = new QVBoxLayout(leftPanel);

    // Search box
    searchBox = new QLineEdit;
    searchBox->setPlaceholderText("🔍 Search notes...");
    connect(searchBox, &QLineEdit::textChanged, this, &MainWindow::onSearchTextChanged);

    // Notes count label
    noteCountLabel = new QLabel("0 notes");
    noteCountLabel->setStyleSheet("color: gray; font-size: 12px;");

    // Notes list
    noteList = new QListWidget;
    noteList->setAlternatingRowColors(true);
    connect(noteList, &QListWidget::currentRowChanged, this, &MainWindow::onNoteSelectionChanged);

    leftLayout->addWidget(searchBox);
    leftLayout->addWidget(noteCountLabel);
    leftLayout->addWidget(noteList);

    // Right panel - note editor
    noteEditor = new RichTextEditor;
    noteEditor->setPlaceholderText("Start writing your note...");
    noteEditor->setFont(QFont("System", 14));
    connect(noteEditor, &QTextEdit::textChanged, [this]() {
        saveTimer->start(); // Restart timer on each change
    });

    mainLayout->addWidget(leftPanel);
    mainLayout->addWidget(noteEditor, 1);

    stack->addWidget(notesWidget);
}

void MainWindow::setupTodosView() {
    auto *todosWidget = new QWidget;
    auto *layout = new QVBoxLayout(todosWidget);

    // Header
    auto *headerLayout = new QHBoxLayout;
    auto *titleLabel = new QLabel("✅ Tasks");
    titleLabel->setFont(QFont("System", 18, QFont::Bold));
    headerLayout->addWidget(titleLabel);
    headerLayout->addStretch();

    // Input for new tasks
    todoInput = new QLineEdit;
    todoInput->setPlaceholderText("Enter new task...");
    todoInput->setFont(QFont("System", 14));
    connect(todoInput, &QLineEdit::returnPressed, this, &MainWindow::addTodo);

    // Buttons
    auto *btnLayout = new QHBoxLayout;
    addTodoButton = new QPushButton("+ Add Task");
    deleteTodoButton = new QPushButton("Delete Selected");
    btnLayout->addWidget(addTodoButton);
    btnLayout->addWidget(deleteTodoButton);
    btnLayout->addStretch();

    // Tasks list
    todoList = new QListWidget;
    todoList->setAlternatingRowColors(true);

    layout->addLayout(headerLayout);
    layout->addWidget(todoInput);
    layout->addLayout(btnLayout);
    layout->addWidget(todoList);

    connect(addTodoButton, &QPushButton::clicked, this, &MainWindow::addTodo);
    connect(deleteTodoButton, &QPushButton::clicked, this, &MainWindow::deleteTodo);
    connect(todoList, &QListWidget::itemChanged, this, &MainWindow::toggleTodoDone);

    stack->addWidget(todosWidget);
}

void MainWindow::setupToolBar() {
    toolBar = addToolBar("Main");
    toolBar->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);

    // New note action
    newNoteAction = new QAction("📝 New Note", this);
    newNoteAction->setShortcut(QKeySequence::New);
    connect(newNoteAction, &QAction::triggered, this, &MainWindow::addNote);
    toolBar->addAction(newNoteAction);

    toolBar->addSeparator();

    // Formatting actions
    boldAction = new QAction("B", this);
    boldAction->setCheckable(true);
    boldAction->setShortcut(QKeySequence::Bold);
    boldAction->setToolTip("Bold");
    connect(boldAction, &QAction::triggered, this, &MainWindow::toggleNoteFormat);
    toolBar->addAction(boldAction);

    italicAction = new QAction("I", this);
    italicAction->setCheckable(true);
    italicAction->setShortcut(QKeySequence::Italic);
    italicAction->setToolTip("Italic");
    connect(italicAction, &QAction::triggered, this, &MainWindow::toggleNoteFormat);
    toolBar->addAction(italicAction);

    underlineAction = new QAction("U", this);
    underlineAction->setCheckable(true);
    underlineAction->setShortcut(QKeySequence::Underline);
    underlineAction->setToolTip("Underline");
    connect(underlineAction, &QAction::triggered, this, &MainWindow::toggleNoteFormat);
    toolBar->addAction(underlineAction);

    toolBar->addSeparator();

    // Folder actions
    newFolderAction = new QAction("📁 New Folder", this);
    connect(newFolderAction, &QAction::triggered, this, &MainWindow::addFolder);
    toolBar->addAction(newFolderAction);
}

void MainWindow::addNote() {
    QString noteTitle = QString("New Note %1").arg(QDateTime::currentDateTime().toString("MMM dd, hh:mm"));
    auto *item = new QListWidgetItem(noteTitle);
    item->setData(Qt::UserRole, QDateTime::currentDateTime());
    item->setData(Qt::UserRole + 1, currentFolder);
    noteList->addItem(item);
    noteList->setCurrentItem(item);
    noteEditor->clear();
    noteEditor->setFocus();
    updateNotesList();
    saveNotes();
}

void MainWindow::deleteNote() {
    int currentRow = noteList->currentRow();
    if (currentRow >= 0) {
        auto *item = noteList->takeItem(currentRow);
        if (item) {
            // Move to trash instead of permanent deletion
            item->setData(Qt::UserRole + 1, "Recently Deleted");
            delete item;
            noteEditor->clear();
            updateNotesList();
            saveNotes();
        }
    }
}

void MainWindow::saveCurrentNote() {
    if (noteList->currentItem() && !noteEditor->toPlainText().isEmpty()) {
        QString content = noteEditor->toPlainText();
        QString title = content.split('\n').first();
        if (title.length() > 50) {
            title = title.left(50) + "...";
        }
        if (title.isEmpty()) {
            title = "Untitled Note";
        }
        noteList->currentItem()->setText(title);
        noteList->currentItem()->setData(Qt::UserRole + 2, content);
        noteList->currentItem()->setData(Qt::UserRole, QDateTime::currentDateTime());
    }
    saveNotes();
}

void MainWindow::onNoteSelectionChanged() {
    auto *item = noteList->currentItem();
    if (item) {
        QString content = item->data(Qt::UserRole + 2).toString();
        if (content.isEmpty()) {
            content = item->text(); // Fallback to title for old notes
        }
        noteEditor->setPlainText(content);
    } else {
        noteEditor->clear();
    }
}

void MainWindow::onSearchTextChanged() {
    QString searchText = searchBox->text().toLower();
    for (int i = 0; i < noteList->count(); ++i) {
        auto *item = noteList->item(i);
        bool matches = item->text().toLower().contains(searchText) ||
                      item->data(Qt::UserRole + 2).toString().toLower().contains(searchText);
        item->setHidden(!matches);
    }
}

void MainWindow::toggleNoteFormat() {
    QTextCursor cursor = noteEditor->textCursor();
    QTextCharFormat format = cursor.charFormat();

    QAction *action = qobject_cast<QAction*>(sender());
    if (action == boldAction) {
        format.setFontWeight(boldAction->isChecked() ? QFont::Bold : QFont::Normal);
    } else if (action == italicAction) {
        format.setFontItalic(italicAction->isChecked());
    } else if (action == underlineAction) {
        format.setFontUnderline(underlineAction->isChecked());
    }

    cursor.setCharFormat(format);
    noteEditor->setTextCursor(cursor);
}

void MainWindow::addFolder() {
    bool ok;
    QString folderName = QInputDialog::getText(this, "New Folder", "Folder name:", QLineEdit::Normal, "", &ok);
    if (ok && !folderName.isEmpty()) {
        auto *item = new QTreeWidgetItem(folderList, QStringList("📁 " + folderName));
        folderList->setCurrentItem(item);
        saveFolders();
    }
}

void MainWindow::deleteFolder() {
    auto *item = folderList->currentItem();
    if (item && !item->text(0).contains("All Notes") && !item->text(0).contains("Tasks") && !item->text(0).contains("Recently Deleted")) {
        int ret = QMessageBox::question(this, "Delete Folder",
                                       "Are you sure you want to delete this folder and all its notes?",
                                       QMessageBox::Yes | QMessageBox::No);
        if (ret == QMessageBox::Yes) {
            delete item;
            saveFolders();
            updateNotesList();
        }
    }
}

void MainWindow::onFolderSelectionChanged() {
    auto *item = folderList->currentItem();
    if (item) {
        QString folderText = item->text(0);
        if (folderText.contains("Tasks")) {
            currentFolder = "Tasks";
            stack->setCurrentIndex(1); // Switch to todos view
        } else {
            if (folderText.contains("All Notes")) {
                currentFolder = "All Notes";
            } else if (folderText.contains("Recently Deleted")) {
                currentFolder = "Recently Deleted";
            } else {
                currentFolder = folderText.mid(2); // Remove emoji prefix
            }
            stack->setCurrentIndex(0); // Switch to notes view
            updateNotesList();
        }
    }
}

void MainWindow::updateNotesList() {
    noteList->clear();
    QSettings settings("KNoteDo", "Notes");
    int count = settings.beginReadArray("notes");
    int visibleCount = 0;

    for (int i = 0; i < count; ++i) {
        settings.setArrayIndex(i);
        QString folder = settings.value("folder", "All Notes").toString();

        if (currentFolder == "All Notes" || folder == currentFolder) {
            auto *item = new QListWidgetItem(settings.value("title").toString());
            item->setData(Qt::UserRole, settings.value("date"));
            item->setData(Qt::UserRole + 1, folder);
            item->setData(Qt::UserRole + 2, settings.value("content").toString());
            noteList->addItem(item);
            visibleCount++;
        }
    }
    settings.endArray();

    noteCountLabel->setText(QString("%1 notes").arg(visibleCount));
}

void MainWindow::loadNotes() {
    // Notes are loaded in updateNotesList()
    updateNotesList();
}

void MainWindow::saveNotes() {
    QSettings settings("KNoteDo", "Notes");
    settings.beginWriteArray("notes");

    // Collect all notes from all folders
    QList<QListWidgetItem*> allNotes;

    // Save current notes list
    for (int i = 0; i < noteList->count(); ++i) {
        allNotes.append(noteList->item(i));
    }

    // Also need to load notes from other folders that aren't currently visible
    QSettings tempSettings("KNoteDo", "Notes");
    int count = tempSettings.beginReadArray("notes");
    for (int i = 0; i < count; ++i) {
        tempSettings.setArrayIndex(i);
        QString folder = tempSettings.value("folder", "All Notes").toString();
        if (folder != currentFolder && currentFolder != "All Notes") {
            auto *item = new QListWidgetItem(tempSettings.value("title").toString());
            item->setData(Qt::UserRole, tempSettings.value("date"));
            item->setData(Qt::UserRole + 1, folder);
            item->setData(Qt::UserRole + 2, tempSettings.value("content").toString());
            allNotes.append(item);
        }
    }
    tempSettings.endArray();

    for (int i = 0; i < allNotes.count(); ++i) {
        settings.setArrayIndex(i);
        auto *item = allNotes[i];
        settings.setValue("title", item->text());
        settings.setValue("content", item->data(Qt::UserRole + 2).toString());
        settings.setValue("date", item->data(Qt::UserRole));
        settings.setValue("folder", item->data(Qt::UserRole + 1).toString());
    }
    settings.endArray();
}

void MainWindow::addTodo() {
    QString task = todoInput->text().trimmed();
    if (!task.isEmpty()) {
        auto *item = new QListWidgetItem(task);
        item->setFlags(item->flags() | Qt::ItemIsUserCheckable);
        item->setCheckState(Qt::Unchecked);
        item->setData(Qt::UserRole, QDateTime::currentDateTime());
        todoList->addItem(item);
        todoInput->clear();
        saveTodos();
    }
}

void MainWindow::deleteTodo() {
    int currentRow = todoList->currentRow();
    if (currentRow >= 0) {
        delete todoList->takeItem(currentRow);
        saveTodos();
    }
}

void MainWindow::toggleTodoDone(QListWidgetItem* item) {
    if (item) {
        // Apply strikethrough for completed tasks
        QFont font = item->font();
        font.setStrikeOut(item->checkState() == Qt::Checked);
        item->setFont(font);

        // Update timestamp
        item->setData(Qt::UserRole, QDateTime::currentDateTime());
        saveTodos();
    }
}

void MainWindow::loadTodos() {
    QSettings settings("KNoteDo", "Todos");
    int count = settings.beginReadArray("tasks");
    for (int i = 0; i < count; ++i) {
        settings.setArrayIndex(i);
        auto *item = new QListWidgetItem(settings.value("task").toString());
        item->setFlags(item->flags() | Qt::ItemIsUserCheckable);
        bool isDone = settings.value("done").toBool();
        item->setCheckState(isDone ? Qt::Checked : Qt::Unchecked);
        item->setData(Qt::UserRole, settings.value("date", QDateTime::currentDateTime()));

        // Apply strikethrough for completed tasks
        QFont font = item->font();
        font.setStrikeOut(isDone);
        item->setFont(font);

        todoList->addItem(item);
    }
    settings.endArray();
}

void MainWindow::saveTodos() {
    QSettings settings("KNoteDo", "Todos");
    settings.beginWriteArray("tasks");
    for (int i = 0; i < todoList->count(); ++i) {
        settings.setArrayIndex(i);
        auto *item = todoList->item(i);
        settings.setValue("task", item->text());
        settings.setValue("done", item->checkState() == Qt::Checked);
        settings.setValue("date", item->data(Qt::UserRole));
    }
    settings.endArray();
}

void MainWindow::loadFolders() {
    QSettings settings("KNoteDo", "Folders");
    QStringList folders = settings.value("customFolders").toStringList();
    for (const QString &folder : folders) {
        new QTreeWidgetItem(folderList, QStringList("📁 " + folder));
    }
}

void MainWindow::saveFolders() {
    QSettings settings("KNoteDo", "Folders");
    QStringList folders;
    for (int i = 0; i < folderList->topLevelItemCount(); ++i) {
        auto *item = folderList->topLevelItem(i);
        QString text = item->text(0);
        if (text.startsWith("📁 ")) {
            folders << text.mid(2); // Remove emoji prefix
        }
    }
    settings.setValue("customFolders", folders);
}

void MainWindow::detectSystemTheme() {
    // Try to detect system theme
    QPalette palette = QApplication::palette();
    isDarkTheme = palette.color(QPalette::Window).lightness() < 128;

    // Also check Qt's style hints
    QStyleHints *hints = QApplication::styleHints();
    if (hints) {
        // Some additional theme detection logic could go here
    }
}

void MainWindow::applySystemTheme() {
    // Apply appropriate style
    if (isDarkTheme) {
        // Dark theme
        setStyleSheet(R"(
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QListWidget {
                background-color: #3c3c3c;
                border: 1px solid #555555;
                selection-background-color: #0078d4;
            }
            QTextEdit {
                background-color: #2b2b2b;
                border: 1px solid #555555;
                color: #ffffff;
            }
            QLineEdit {
                background-color: #3c3c3c;
                border: 1px solid #555555;
                padding: 5px;
                color: #ffffff;
            }
            QPushButton {
                background-color: #0078d4;
                border: none;
                padding: 8px 16px;
                color: white;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QTreeWidget {
                background-color: #3c3c3c;
                border: 1px solid #555555;
                selection-background-color: #0078d4;
            }
        )");
    } else {
        // Light theme
        setStyleSheet(R"(
            QMainWindow {
                background-color: #ffffff;
                color: #000000;
            }
            QListWidget {
                background-color: #ffffff;
                border: 1px solid #d0d0d0;
                selection-background-color: #0078d4;
            }
            QTextEdit {
                background-color: #ffffff;
                border: 1px solid #d0d0d0;
                color: #000000;
            }
            QLineEdit {
                background-color: #ffffff;
                border: 1px solid #d0d0d0;
                padding: 5px;
                color: #000000;
            }
            QPushButton {
                background-color: #0078d4;
                border: none;
                padding: 8px 16px;
                color: white;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QTreeWidget {
                background-color: #ffffff;
                border: 1px solid #d0d0d0;
                selection-background-color: #0078d4;
            }
        )");
    }
}

void MainWindow::exportNote() {
    auto *item = noteList->currentItem();
    if (!item) {
        QMessageBox::information(this, "Export Note", "Please select a note to export.");
        return;
    }

    QString fileName = QFileDialog::getSaveFileName(this, "Export Note",
                                                   item->text() + ".txt",
                                                   "Text Files (*.txt);;HTML Files (*.html);;All Files (*)");
    if (!fileName.isEmpty()) {
        QFile file(fileName);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            QTextStream out(&file);
            if (fileName.endsWith(".html")) {
                out << noteEditor->toHtml();
            } else {
                out << noteEditor->toPlainText();
            }
            QMessageBox::information(this, "Export Note", "Note exported successfully!");
        } else {
            QMessageBox::warning(this, "Export Note", "Failed to export note.");
        }
    }
}

void MainWindow::exportAllNotes() {
    QString dirName = QFileDialog::getExistingDirectory(this, "Export All Notes");
    if (!dirName.isEmpty()) {
        int exported = 0;
        for (int i = 0; i < noteList->count(); ++i) {
            auto *item = noteList->item(i);
            QString fileName = QString("%1/%2.txt").arg(dirName, item->text());
            QFile file(fileName);
            if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
                QTextStream out(&file);
                out << item->data(Qt::UserRole + 2).toString();
                exported++;
            }
        }
        QMessageBox::information(this, "Export All Notes",
                                QString("Exported %1 notes successfully!").arg(exported));
    }
}

void MainWindow::importNotes() {
    QStringList fileNames = QFileDialog::getOpenFileNames(this, "Import Notes",
                                                         QString(),
                                                         "Text Files (*.txt);;All Files (*)");
    int imported = 0;
    for (const QString &fileName : fileNames) {
        QFile file(fileName);
        if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            QTextStream in(&file);
            QString content = in.readAll();

            QFileInfo fileInfo(fileName);
            QString title = fileInfo.baseName();

            auto *item = new QListWidgetItem(title);
            item->setData(Qt::UserRole, QDateTime::currentDateTime());
            item->setData(Qt::UserRole + 1, currentFolder);
            item->setData(Qt::UserRole + 2, content);
            noteList->addItem(item);
            imported++;
        }
    }
    if (imported > 0) {
        saveNotes();
        updateNotesList();
        QMessageBox::information(this, "Import Notes",
                                QString("Imported %1 notes successfully!").arg(imported));
    }
}

void MainWindow::showSettings() {
    SettingsDialog dialog(this);

    // Load current settings
    QSettings settings("KNoteDo", "Settings");
    dialog.setTheme(settings.value("theme", "System Default").toString());
    dialog.setAutoSave(settings.value("autoSave", true).toBool());
    dialog.setAutoSaveInterval(settings.value("autoSaveInterval", 5).toInt());
    dialog.setDefaultFont(settings.value("defaultFont", "System").toString());
    dialog.setDefaultFontSize(settings.value("defaultFontSize", 14).toInt());
    dialog.setSpellCheck(settings.value("spellCheck", false).toBool());
    dialog.setExportPath(settings.value("exportPath",
                        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation)).toString());

    if (dialog.exec() == QDialog::Accepted) {
        // Save new settings
        settings.setValue("theme", dialog.getTheme());
        settings.setValue("autoSave", dialog.getAutoSave());
        settings.setValue("autoSaveInterval", dialog.getAutoSaveInterval());
        settings.setValue("defaultFont", dialog.getDefaultFont());
        settings.setValue("defaultFontSize", dialog.getDefaultFontSize());
        settings.setValue("spellCheck", dialog.getSpellCheck());
        settings.setValue("exportPath", dialog.getExportPath());

        // Apply new settings
        loadSettings();
        applySystemTheme();
    }
}

void MainWindow::showAbout() {
    QMessageBox::about(this, "About KNoteDo",
                      "<h2>KNoteDo</h2>"
                      "<p>An Apple Notes clone for Linux</p>"
                      "<p>Version 1.0</p>"
                      "<p>Built with Qt6 for KDE Plasma and GNOME</p>"
                      "<p>Features:</p>"
                      "<ul>"
                      "<li>Rich text editing</li>"
                      "<li>Folder organization</li>"
                      "<li>Task management</li>"
                      "<li>System theme integration</li>"
                      "<li>Export/Import functionality</li>"
                      "</ul>"
                      "<p>© 2024 KNoteDo Project</p>");
}

void MainWindow::loadSettings() {
    QSettings settings("KNoteDo", "Settings");

    // Apply font settings
    QString fontFamily = settings.value("defaultFont", "System").toString();
    int fontSize = settings.value("defaultFontSize", 14).toInt();
    QFont font(fontFamily, fontSize);
    noteEditor->setFont(font);

    // Apply auto-save settings
    bool autoSave = settings.value("autoSave", true).toBool();
    int interval = settings.value("autoSaveInterval", 5).toInt();
    if (autoSave) {
        saveTimer->setInterval(interval * 1000);
    } else {
        saveTimer->stop();
    }
}

void MainWindow::saveSettings() {
    // Settings are saved in showSettings() method
}

// Placeholder methods for future implementation
void MainWindow::moveNoteToTrash() {
    // Implementation for moving notes to trash
}

void MainWindow::emptyTrash() {
    // Implementation for emptying trash
}

void MainWindow::restoreFromTrash() {
    // Implementation for restoring from trash
}
