// Enhanced Apple Notes clone for Linux KDE/GNOME
#include "MainWindow.h"
#include "RichTextEditor.h"
#include "SettingsDialog.h"
#include "AppleNotesTheme.h"
#include "FolderManager.h"
#include "ExportImportManager.h"
#include <QApplication>
#include <QSplitter>
#include <QListWidget>
#include <QStackedWidget>
#include <QTextEdit>
#include <QLineEdit>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QStyleFactory>
#include <QSettings>
#include <QJsonDocument>
#include <QJsonArray>
#include <QJsonObject>
#include <QFile>
#include <QFrame>
#include <QToolBar>
#include <QAction>
#include <QIcon>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QHeaderView>
#include <QTimer>
#include <QPalette>
#include <QStyle>
#include <QStyleHints>
#include <QFont>
#include <QFontMetrics>
#include <QTextCharFormat>
#include <QTextCursor>
#include <QKeySequence>
#include <QShortcut>
#include <QMessageBox>
#include <QInputDialog>
#include <QDateTime>
#include <QDir>
#include <QStandardPaths>
#include <QMenuBar>
#include <QMenu>
#include <QFileDialog>
#include <QTextStream>
#include <QDesktopServices>
#include <QUrl>

MainWindow::MainWindow() : currentFolder("All Notes") {
    setWindowTitle("KNoteDo - Apple Notes for Linux");
    resize(1200, 800);
    setMinimumSize(800, 600);

    // Initialize timer for auto-save
    saveTimer = new QTimer(this);
    saveTimer->setSingleShot(true);
    saveTimer->setInterval(1000); // Auto-save after 1 second of inactivity
    connect(saveTimer, &QTimer::timeout, this, &MainWindow::saveCurrentNote);

    // Detect and apply system theme
    detectSystemTheme();
    applySystemTheme();

    // Setup UI components
    setupMenuBar();
    setupUI();
    setupToolBar();

    // Load settings and data
    loadSettings();
    loadFolders();
    loadNotes();
    loadTodos();

    // Set initial state
    updateNotesList();
}

void MainWindow::setupMenuBar() {
    // File menu
    fileMenu = menuBar()->addMenu("&File");

    newNoteAction = new QAction("&New Note", this);
    newNoteAction->setShortcut(QKeySequence::New);
    newNoteAction->setStatusTip("Create a new note");
    connect(newNoteAction, &QAction::triggered, this, &MainWindow::addNote);
    fileMenu->addAction(newNoteAction);

    newFolderAction = new QAction("New &Folder", this);
    newFolderAction->setShortcut(QKeySequence("Ctrl+Shift+N"));
    connect(newFolderAction, &QAction::triggered, this, &MainWindow::addFolder);
    fileMenu->addAction(newFolderAction);

    fileMenu->addSeparator();

    exportAction = new QAction("&Export Note...", this);
    exportAction->setShortcut(QKeySequence("Ctrl+E"));
    connect(exportAction, &QAction::triggered, this, &MainWindow::exportNote);
    fileMenu->addAction(exportAction);

    QAction *exportAllAction = new QAction("Export &All Notes...", this);
    connect(exportAllAction, &QAction::triggered, this, &MainWindow::exportAllNotes);
    fileMenu->addAction(exportAllAction);

    importAction = new QAction("&Import Notes...", this);
    connect(importAction, &QAction::triggered, this, &MainWindow::importNotes);
    fileMenu->addAction(importAction);

    fileMenu->addSeparator();

    QAction *quitAction = new QAction("&Quit", this);
    quitAction->setShortcut(QKeySequence::Quit);
    connect(quitAction, &QAction::triggered, this, &QWidget::close);
    fileMenu->addAction(quitAction);

    // Edit menu
    editMenu = menuBar()->addMenu("&Edit");

    deleteNoteAction = new QAction("&Delete Note", this);
    deleteNoteAction->setShortcut(QKeySequence::Delete);
    connect(deleteNoteAction, &QAction::triggered, this, &MainWindow::deleteNote);
    editMenu->addAction(deleteNoteAction);

    editMenu->addSeparator();

    settingsAction = new QAction("&Preferences...", this);
    settingsAction->setShortcut(QKeySequence::Preferences);
    connect(settingsAction, &QAction::triggered, this, &MainWindow::showSettings);
    editMenu->addAction(settingsAction);

    // View menu
    viewMenu = menuBar()->addMenu("&View");

    // Help menu
    helpMenu = menuBar()->addMenu("&Help");

    aboutAction = new QAction("&About KNoteDo", this);
    connect(aboutAction, &QAction::triggered, this, &MainWindow::showAbout);
    helpMenu->addAction(aboutAction);
}

void MainWindow::setupUI() {
    // Main splitter
    mainSplitter = new QSplitter(Qt::Horizontal, this);
    mainSplitter->setHandleWidth(1);

    // Setup sidebar and main content
    setupSidebar();

    // Right side - stacked widget for different views
    stack = new QStackedWidget;
    setupNotesView();
    setupTodosView();

    mainSplitter->addWidget(folderList);
    mainSplitter->addWidget(stack);
    mainSplitter->setSizes({250, 950});

    setCentralWidget(mainSplitter);

    // Connect signals
    connect(folderList, &QTreeWidget::currentItemChanged, this, &MainWindow::onFolderSelectionChanged);
}

void MainWindow::setupSidebar() {
    folderList = new QTreeWidget;
    folderList->setHeaderHidden(true);
    folderList->setMaximumWidth(300);
    folderList->setMinimumWidth(200);
    folderList->setRootIsDecorated(false);

    // Add default folders
    auto *allNotesItem = new QTreeWidgetItem(folderList, QStringList("📒 All Notes"));
    auto *tasksItem = new QTreeWidgetItem(folderList, QStringList("📋 Tasks"));
    auto *trashItem = new QTreeWidgetItem(folderList, QStringList("🗑️ Recently Deleted"));

    folderList->setCurrentItem(allNotesItem);
}

void MainWindow::setupNotesView() {
    auto *notesWidget = new QWidget;
    auto *mainLayout = new QHBoxLayout(notesWidget);
    mainLayout->setContentsMargins(0, 0, 0, 0);

    // Left panel - notes list
    auto *leftPanel = new QWidget;
    leftPanel->setMaximumWidth(350);
    leftPanel->setMinimumWidth(250);
    auto *leftLayout = new QVBoxLayout(leftPanel);

    // Enhanced search box
    searchBox = new QLineEdit;
    searchBox->setPlaceholderText("🔍 Search notes...");
    searchBox->setClearButtonEnabled(true);
    connect(searchBox, &QLineEdit::textChanged, this, &MainWindow::onSearchTextChanged);
    connect(searchBox, &QLineEdit::returnPressed, this, [this]() {
        addToRecentSearches(searchBox->text());
    });

    // Notes count label
    noteCountLabel = new QLabel("0 notes");
    noteCountLabel->setStyleSheet("color: gray; font-size: 12px;");

    // Notes list with enhanced styling
    noteList = new QListWidget;
    noteList->setAlternatingRowColors(true);
    noteList->setSpacing(2);
    noteList->setUniformItemSizes(false);
    noteList->setWordWrap(true);
    connect(noteList, &QListWidget::currentRowChanged, this, &MainWindow::onNoteSelectionChanged);
    connect(noteList, &QListWidget::itemDoubleClicked, this, &MainWindow::onNoteDoubleClicked);
    connect(noteList, &QListWidget::currentItemChanged, this, &MainWindow::updateNotePreview);

    leftLayout->addWidget(searchBox);
    leftLayout->addWidget(noteCountLabel);
    leftLayout->addWidget(noteList);

    // Right panel - note editor with toolbar
    auto *rightPanel = new QWidget;
    auto *rightLayout = new QVBoxLayout(rightPanel);
    rightLayout->setContentsMargins(0, 0, 0, 0);
    rightLayout->setSpacing(0);

    noteEditor = new RichTextEditor;
    noteEditor->setPlaceholderText("Start writing your note...");
    noteEditor->setFont(QFont("System", 14));
    connect(noteEditor, &QTextEdit::textChanged, [this]() {
        saveTimer->start(); // Restart timer on each change
    });

    // Add the rich text editor's toolbar
    QToolBar *editorToolbar = noteEditor->createToolBar();
    editorToolbar->setMovable(false);
    editorToolbar->setFloatable(false);
    editorToolbar->setToolButtonStyle(Qt::ToolButtonIconOnly);

    rightLayout->addWidget(editorToolbar);
    rightLayout->addWidget(noteEditor, 1);

    mainLayout->addWidget(leftPanel);
    mainLayout->addWidget(rightPanel, 1);

    stack->addWidget(notesWidget);
}

void MainWindow::setupTodosView() {
    auto *todosWidget = new QWidget;
    auto *layout = new QVBoxLayout(todosWidget);

    // Header
    auto *headerLayout = new QHBoxLayout;
    auto *titleLabel = new QLabel("✅ Tasks");
    titleLabel->setFont(QFont("System", 18, QFont::Bold));
    headerLayout->addWidget(titleLabel);
    headerLayout->addStretch();

    // Input for new tasks
    todoInput = new QLineEdit;
    todoInput->setPlaceholderText("Enter new task...");
    todoInput->setFont(QFont("System", 14));
    connect(todoInput, &QLineEdit::returnPressed, this, &MainWindow::addTodo);

    // Buttons
    auto *btnLayout = new QHBoxLayout;
    addTodoButton = new QPushButton("+ Add Task");
    deleteTodoButton = new QPushButton("Delete Selected");
    btnLayout->addWidget(addTodoButton);
    btnLayout->addWidget(deleteTodoButton);
    btnLayout->addStretch();

    // Tasks list
    todoList = new QListWidget;
    todoList->setAlternatingRowColors(true);

    layout->addLayout(headerLayout);
    layout->addWidget(todoInput);
    layout->addLayout(btnLayout);
    layout->addWidget(todoList);

    connect(addTodoButton, &QPushButton::clicked, this, &MainWindow::addTodo);
    connect(deleteTodoButton, &QPushButton::clicked, this, &MainWindow::deleteTodo);
    connect(todoList, &QListWidget::itemChanged, this, &MainWindow::toggleTodoDone);

    stack->addWidget(todosWidget);
}

void MainWindow::setupToolBar() {
    // Main toolbar with file actions
    toolBar = addToolBar("Main");
    toolBar->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);

    // New note action
    newNoteAction = new QAction("📝 New Note", this);
    newNoteAction->setShortcut(QKeySequence::New);
    connect(newNoteAction, &QAction::triggered, this, &MainWindow::addNote);
    toolBar->addAction(newNoteAction);

    // New folder action
    newFolderAction = new QAction("📁 New Folder", this);
    connect(newFolderAction, &QAction::triggered, this, &MainWindow::addFolder);
    toolBar->addAction(newFolderAction);
}

void MainWindow::addNote() {
    QString noteTitle = QString("New Note %1").arg(QDateTime::currentDateTime().toString("MMM dd, hh:mm"));
    auto *item = new QListWidgetItem(noteTitle);
    item->setData(Qt::UserRole, QDateTime::currentDateTime());
    item->setData(Qt::UserRole + 1, currentFolder);
    noteList->addItem(item);
    noteList->setCurrentItem(item);
    noteEditor->clear();
    noteEditor->setFocus();
    updateNotesList();
    saveNotes();
}

void MainWindow::deleteNote() {
    int currentRow = noteList->currentRow();
    if (currentRow >= 0) {
        auto *item = noteList->takeItem(currentRow);
        if (item) {
            // Move to trash instead of permanent deletion
            item->setData(Qt::UserRole + 1, "Recently Deleted");
            delete item;
            noteEditor->clear();
            updateNotesList();
            saveNotes();
        }
    }
}

void MainWindow::saveCurrentNote() {
    if (noteList->currentItem() && !noteEditor->toPlainText().isEmpty()) {
        QString content = noteEditor->toPlainText();
        QString title = content.split('\n').first();
        if (title.length() > 50) {
            title = title.left(50) + "...";
        }
        if (title.isEmpty()) {
            title = "Untitled Note";
        }
        noteList->currentItem()->setText(title);
        noteList->currentItem()->setData(Qt::UserRole + 2, content);
        noteList->currentItem()->setData(Qt::UserRole, QDateTime::currentDateTime());
    }
    saveNotes();
}

void MainWindow::onNoteSelectionChanged() {
    auto *item = noteList->currentItem();
    if (item) {
        QString content = item->data(Qt::UserRole + 2).toString();
        if (content.isEmpty()) {
            content = item->text(); // Fallback to title for old notes
        }
        noteEditor->setPlainText(content);
    } else {
        noteEditor->clear();
    }
}

void MainWindow::onSearchTextChanged() {
    QString searchText = searchBox->text().toLower();
    int visibleCount = 0;

    for (int i = 0; i < noteList->count(); ++i) {
        auto *item = noteList->item(i);
        QString title = item->data(Qt::UserRole + 3).toString().toLower();
        QString content = item->data(Qt::UserRole + 2).toString().toLower();

        bool matches = searchText.isEmpty() ||
                      title.contains(searchText) ||
                      content.contains(searchText);

        item->setHidden(!matches);
        if (!item->isHidden()) {
            visibleCount++;
            // Highlight search results in the display
            if (!searchText.isEmpty()) {
                highlightSearchResults(searchText);
            }
        }
    }

    // Update count label
    if (searchText.isEmpty()) {
        noteCountLabel->setText(QString("%1 notes").arg(noteList->count()));
    } else {
        noteCountLabel->setText(QString("%1 of %2 notes").arg(visibleCount).arg(noteList->count()));
    }
}



void MainWindow::addFolder() {
    bool ok;
    QString folderName = QInputDialog::getText(this, "New Folder", "Folder name:", QLineEdit::Normal, "", &ok);
    if (ok && !folderName.isEmpty()) {
        auto *item = new QTreeWidgetItem(folderList, QStringList("📁 " + folderName));
        folderList->setCurrentItem(item);
        saveFolders();
    }
}

void MainWindow::deleteFolder() {
    auto *item = folderList->currentItem();
    if (item && !item->text(0).contains("All Notes") && !item->text(0).contains("Tasks") && !item->text(0).contains("Recently Deleted")) {
        int ret = QMessageBox::question(this, "Delete Folder",
                                       "Are you sure you want to delete this folder and all its notes?",
                                       QMessageBox::Yes | QMessageBox::No);
        if (ret == QMessageBox::Yes) {
            delete item;
            saveFolders();
            updateNotesList();
        }
    }
}

void MainWindow::onFolderSelectionChanged() {
    auto *item = folderList->currentItem();
    if (item) {
        QString folderText = item->text(0);
        if (folderText.contains("Tasks")) {
            currentFolder = "Tasks";
            stack->setCurrentIndex(1); // Switch to todos view
        } else {
            if (folderText.contains("All Notes")) {
                currentFolder = "All Notes";
            } else if (folderText.contains("Recently Deleted")) {
                currentFolder = "Recently Deleted";
            } else {
                currentFolder = folderText.mid(2); // Remove emoji prefix
            }
            stack->setCurrentIndex(0); // Switch to notes view
            updateNotesList();
        }
    }
}

void MainWindow::updateNotesList() {
    noteList->clear();
    QSettings settings("KNoteDo", "Notes");
    int count = settings.beginReadArray("notes");
    int visibleCount = 0;

    for (int i = 0; i < count; ++i) {
        settings.setArrayIndex(i);
        QString folder = settings.value("folder", "All Notes").toString();

        if (currentFolder == "All Notes" || folder == currentFolder) {
            QString title = settings.value("title").toString();
            QString content = settings.value("content").toString();
            QDateTime date = settings.value("date").toDateTime();

            // Create Apple Notes-style preview
            QString preview = content.left(100).replace('\n', ' ');
            if (content.length() > 100) preview += "...";

            // Format display text with title and preview
            QString displayText = QString("<b>%1</b><br><span style='color: gray; font-size: 12px;'>%2</span><br><span style='color: #666; font-size: 11px;'>%3</span>")
                                 .arg(title.isEmpty() ? "Untitled" : title)
                                 .arg(preview.isEmpty() ? "No additional text" : preview)
                                 .arg(date.toString("MMM dd, yyyy hh:mm"));

            auto *item = new QListWidgetItem();
            item->setText(displayText);
            item->setData(Qt::UserRole, date);
            item->setData(Qt::UserRole + 1, folder);
            item->setData(Qt::UserRole + 2, content);
            item->setData(Qt::UserRole + 3, title); // Store original title
            item->setSizeHint(QSize(0, 80)); // Set item height for preview

            noteList->addItem(item);
            visibleCount++;
        }
    }
    settings.endArray();

    noteCountLabel->setText(QString("%1 notes").arg(visibleCount));
}

void MainWindow::loadNotes() {
    // Notes are loaded in updateNotesList()
    updateNotesList();
}

void MainWindow::saveNotes() {
    QSettings settings("KNoteDo", "Notes");
    settings.beginWriteArray("notes");

    // Collect all notes from all folders
    QList<QListWidgetItem*> allNotes;

    // Save current notes list
    for (int i = 0; i < noteList->count(); ++i) {
        allNotes.append(noteList->item(i));
    }

    // Also need to load notes from other folders that aren't currently visible
    QSettings tempSettings("KNoteDo", "Notes");
    int count = tempSettings.beginReadArray("notes");
    for (int i = 0; i < count; ++i) {
        tempSettings.setArrayIndex(i);
        QString folder = tempSettings.value("folder", "All Notes").toString();
        if (folder != currentFolder && currentFolder != "All Notes") {
            auto *item = new QListWidgetItem(tempSettings.value("title").toString());
            item->setData(Qt::UserRole, tempSettings.value("date"));
            item->setData(Qt::UserRole + 1, folder);
            item->setData(Qt::UserRole + 2, tempSettings.value("content").toString());
            allNotes.append(item);
        }
    }
    tempSettings.endArray();

    for (int i = 0; i < allNotes.count(); ++i) {
        settings.setArrayIndex(i);
        auto *item = allNotes[i];
        settings.setValue("title", item->text());
        settings.setValue("content", item->data(Qt::UserRole + 2).toString());
        settings.setValue("date", item->data(Qt::UserRole));
        settings.setValue("folder", item->data(Qt::UserRole + 1).toString());
    }
    settings.endArray();
}

void MainWindow::addTodo() {
    QString task = todoInput->text().trimmed();
    if (!task.isEmpty()) {
        auto *item = new QListWidgetItem(task);
        item->setFlags(item->flags() | Qt::ItemIsUserCheckable);
        item->setCheckState(Qt::Unchecked);
        item->setData(Qt::UserRole, QDateTime::currentDateTime());
        todoList->addItem(item);
        todoInput->clear();
        saveTodos();
    }
}

void MainWindow::deleteTodo() {
    int currentRow = todoList->currentRow();
    if (currentRow >= 0) {
        delete todoList->takeItem(currentRow);
        saveTodos();
    }
}

void MainWindow::toggleTodoDone(QListWidgetItem* item) {
    if (item) {
        // Apply strikethrough for completed tasks
        QFont font = item->font();
        font.setStrikeOut(item->checkState() == Qt::Checked);
        item->setFont(font);

        // Update timestamp
        item->setData(Qt::UserRole, QDateTime::currentDateTime());
        saveTodos();
    }
}

void MainWindow::loadTodos() {
    QSettings settings("KNoteDo", "Todos");
    int count = settings.beginReadArray("tasks");
    for (int i = 0; i < count; ++i) {
        settings.setArrayIndex(i);
        auto *item = new QListWidgetItem(settings.value("task").toString());
        item->setFlags(item->flags() | Qt::ItemIsUserCheckable);
        bool isDone = settings.value("done").toBool();
        item->setCheckState(isDone ? Qt::Checked : Qt::Unchecked);
        item->setData(Qt::UserRole, settings.value("date", QDateTime::currentDateTime()));

        // Apply strikethrough for completed tasks
        QFont font = item->font();
        font.setStrikeOut(isDone);
        item->setFont(font);

        todoList->addItem(item);
    }
    settings.endArray();
}

void MainWindow::saveTodos() {
    QSettings settings("KNoteDo", "Todos");
    settings.beginWriteArray("tasks");
    for (int i = 0; i < todoList->count(); ++i) {
        settings.setArrayIndex(i);
        auto *item = todoList->item(i);
        settings.setValue("task", item->text());
        settings.setValue("done", item->checkState() == Qt::Checked);
        settings.setValue("date", item->data(Qt::UserRole));
    }
    settings.endArray();
}

void MainWindow::loadFolders() {
    QSettings settings("KNoteDo", "Folders");
    QStringList folders = settings.value("customFolders").toStringList();
    for (const QString &folder : folders) {
        new QTreeWidgetItem(folderList, QStringList("📁 " + folder));
    }
}

void MainWindow::saveFolders() {
    QSettings settings("KNoteDo", "Folders");
    QStringList folders;
    for (int i = 0; i < folderList->topLevelItemCount(); ++i) {
        auto *item = folderList->topLevelItem(i);
        QString text = item->text(0);
        if (text.startsWith("📁 ")) {
            folders << text.mid(2); // Remove emoji prefix
        }
    }
    settings.setValue("customFolders", folders);
}

void MainWindow::detectSystemTheme() {
    // Try to detect system theme
    QPalette palette = QApplication::palette();
    isDarkTheme = palette.color(QPalette::Window).lightness() < 128;

    // Also check Qt's style hints
    QStyleHints *hints = QApplication::styleHints();
    if (hints) {
        // Some additional theme detection logic could go here
    }
}

void MainWindow::applySystemTheme() {
    QPalette palette = QApplication::palette();
    bool isDarkTheme = palette.color(QPalette::Window).lightness() < 128;

    // Apply Apple Notes-like styling
    AppleNotesTheme::applyAppleNotesStyle(this, isDarkTheme);

    // Set proper window dimensions and layout
    resize(1200, 800);

    // Configure splitter proportions for Apple Notes-like layout
    if (mainSplitter) {
        mainSplitter->setSizes({AppleNotesTheme::Spacing::SIDEBAR_WIDTH,
                               AppleNotesTheme::Spacing::NOTE_LIST_WIDTH,
                               width() - AppleNotesTheme::Spacing::SIDEBAR_WIDTH - AppleNotesTheme::Spacing::NOTE_LIST_WIDTH});
    }
}

void MainWindow::exportNote() {
    auto *item = noteList->currentItem();
    if (!item) {
        QMessageBox::information(this, "Export Note", "Please select a note to export.");
        return;
    }

    QString fileName = QFileDialog::getSaveFileName(this, "Export Note",
                                                   item->text() + ".txt",
                                                   "Text Files (*.txt);;HTML Files (*.html);;All Files (*)");
    if (!fileName.isEmpty()) {
        QFile file(fileName);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            QTextStream out(&file);
            if (fileName.endsWith(".html")) {
                out << noteEditor->toHtml();
            } else {
                out << noteEditor->toPlainText();
            }
            QMessageBox::information(this, "Export Note", "Note exported successfully!");
        } else {
            QMessageBox::warning(this, "Export Note", "Failed to export note.");
        }
    }
}

void MainWindow::exportAllNotes() {
    QString dirName = QFileDialog::getExistingDirectory(this, "Export All Notes");
    if (!dirName.isEmpty()) {
        int exported = 0;
        for (int i = 0; i < noteList->count(); ++i) {
            auto *item = noteList->item(i);
            QString fileName = QString("%1/%2.txt").arg(dirName, item->text());
            QFile file(fileName);
            if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
                QTextStream out(&file);
                out << item->data(Qt::UserRole + 2).toString();
                exported++;
            }
        }
        QMessageBox::information(this, "Export All Notes",
                                QString("Exported %1 notes successfully!").arg(exported));
    }
}

void MainWindow::importNotes() {
    QStringList fileNames = QFileDialog::getOpenFileNames(this, "Import Notes",
                                                         QString(),
                                                         "Text Files (*.txt);;All Files (*)");
    int imported = 0;
    for (const QString &fileName : fileNames) {
        QFile file(fileName);
        if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            QTextStream in(&file);
            QString content = in.readAll();

            QFileInfo fileInfo(fileName);
            QString title = fileInfo.baseName();

            auto *item = new QListWidgetItem(title);
            item->setData(Qt::UserRole, QDateTime::currentDateTime());
            item->setData(Qt::UserRole + 1, currentFolder);
            item->setData(Qt::UserRole + 2, content);
            noteList->addItem(item);
            imported++;
        }
    }
    if (imported > 0) {
        saveNotes();
        updateNotesList();
        QMessageBox::information(this, "Import Notes",
                                QString("Imported %1 notes successfully!").arg(imported));
    }
}

void MainWindow::showSettings() {
    SettingsDialog dialog(this);

    // Load current settings
    QSettings settings("KNoteDo", "Settings");
    dialog.setTheme(settings.value("theme", "System Default").toString());
    dialog.setAutoSave(settings.value("autoSave", true).toBool());
    dialog.setAutoSaveInterval(settings.value("autoSaveInterval", 5).toInt());
    dialog.setDefaultFont(settings.value("defaultFont", "System").toString());
    dialog.setDefaultFontSize(settings.value("defaultFontSize", 14).toInt());
    dialog.setSpellCheck(settings.value("spellCheck", false).toBool());
    dialog.setExportPath(settings.value("exportPath",
                        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation)).toString());

    if (dialog.exec() == QDialog::Accepted) {
        // Save new settings
        settings.setValue("theme", dialog.getTheme());
        settings.setValue("autoSave", dialog.getAutoSave());
        settings.setValue("autoSaveInterval", dialog.getAutoSaveInterval());
        settings.setValue("defaultFont", dialog.getDefaultFont());
        settings.setValue("defaultFontSize", dialog.getDefaultFontSize());
        settings.setValue("spellCheck", dialog.getSpellCheck());
        settings.setValue("exportPath", dialog.getExportPath());

        // Apply new settings
        loadSettings();
        applySystemTheme();
    }
}

void MainWindow::showAbout() {
    QMessageBox::about(this, "About KNoteDo",
                      "<h2>KNoteDo</h2>"
                      "<p>An Apple Notes clone for Linux</p>"
                      "<p>Version 1.0</p>"
                      "<p>Built with Qt6 for KDE Plasma and GNOME</p>"
                      "<p>Features:</p>"
                      "<ul>"
                      "<li>Rich text editing</li>"
                      "<li>Folder organization</li>"
                      "<li>Task management</li>"
                      "<li>System theme integration</li>"
                      "<li>Export/Import functionality</li>"
                      "</ul>"
                      "<p>© 2024 KNoteDo Project</p>");
}

void MainWindow::loadSettings() {
    QSettings settings("KNoteDo", "Settings");

    // Apply font settings
    QString fontFamily = settings.value("defaultFont", "System").toString();
    int fontSize = settings.value("defaultFontSize", 14).toInt();
    QFont font(fontFamily, fontSize);
    noteEditor->setFont(font);

    // Apply auto-save settings
    bool autoSave = settings.value("autoSave", true).toBool();
    int interval = settings.value("autoSaveInterval", 5).toInt();
    if (autoSave) {
        saveTimer->setInterval(interval * 1000);
    } else {
        saveTimer->stop();
    }
}

void MainWindow::saveSettings() {
    // Settings are saved in showSettings() method
}

void MainWindow::onNoteDoubleClicked(QListWidgetItem* item) {
    // Focus on the editor when double-clicking a note
    if (item) {
        noteEditor->setFocus();
        noteEditor->moveCursor(QTextCursor::End);
    }
}

void MainWindow::updateNotePreview(QListWidgetItem* item) {
    // This method is called when note selection changes
    // Additional preview logic can be added here if needed
    if (item) {
        // Could show additional metadata or preview in status bar
        QString title = item->data(Qt::UserRole + 3).toString();
        QDateTime date = item->data(Qt::UserRole).toDateTime();
        statusBar()->showMessage(QString("Note: %1 - Modified: %2")
                               .arg(title.isEmpty() ? "Untitled" : title)
                               .arg(date.toString("MMM dd, yyyy hh:mm")));
    }
}

void MainWindow::highlightSearchResults(const QString &searchText) {
    // Highlight search results in the current note editor
    if (searchText.isEmpty()) return;

    QTextDocument *document = noteEditor->document();
    QTextCursor cursor(document);

    // Clear previous highlights
    cursor.select(QTextCursor::Document);
    QTextCharFormat clearFormat;
    clearFormat.setBackground(QBrush());
    cursor.setCharFormat(clearFormat);

    // Highlight new search results
    QTextCharFormat highlightFormat;
    highlightFormat.setBackground(QColor(255, 255, 0, 100)); // Yellow highlight

    cursor.movePosition(QTextCursor::Start);
    while (!cursor.isNull() && !cursor.atEnd()) {
        cursor = document->find(searchText, cursor, QTextDocument::FindCaseInsensitively);
        if (!cursor.isNull()) {
            cursor.setCharFormat(highlightFormat);
        }
    }
}

void MainWindow::addToRecentSearches(const QString &searchText) {
    if (searchText.isEmpty() || recentSearches.contains(searchText)) return;

    recentSearches.prepend(searchText);
    if (recentSearches.size() > 10) {
        recentSearches.removeLast();
    }

    // Save to settings
    QSettings settings("KNoteDo", "Search");
    settings.setValue("recentSearches", recentSearches);
}

void MainWindow::showRecentSearches() {
    // This could be implemented as a dropdown or popup
    // For now, we'll just load the recent searches
    QSettings settings("KNoteDo", "Search");
    recentSearches = settings.value("recentSearches").toStringList();
}

// Placeholder methods for future implementation
void MainWindow::moveNoteToTrash() {
    auto *item = noteList->currentItem();
    if (!item) return;

    int ret = QMessageBox::question(this, "Move to Trash",
                                   "Are you sure you want to move this note to trash?",
                                   QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes) {
        // Mark note as deleted and move to trash folder
        QString title = item->data(Qt::UserRole + 3).toString();
        QString content = item->data(Qt::UserRole + 2).toString();
        QDateTime date = QDateTime::currentDateTime();

        // Save to trash
        QSettings trashSettings("KNoteDo", "Trash");
        int trashCount = trashSettings.beginReadArray("deletedNotes");
        trashSettings.endArray();

        trashSettings.beginWriteArray("deletedNotes");
        trashSettings.setArrayIndex(trashCount);
        trashSettings.setValue("title", title);
        trashSettings.setValue("content", content);
        trashSettings.setValue("deletedDate", date);
        trashSettings.setValue("originalFolder", item->data(Qt::UserRole + 1).toString());
        trashSettings.endArray();

        // Remove from current notes
        deleteNote();
    }
}

void MainWindow::emptyTrash() {
    int ret = QMessageBox::question(this, "Empty Trash",
                                   "Are you sure you want to permanently delete all notes in trash?",
                                   QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes) {
        QSettings trashSettings("KNoteDo", "Trash");
        trashSettings.remove("deletedNotes");
        QMessageBox::information(this, "Trash Emptied", "All notes have been permanently deleted.");
    }
}

void MainWindow::restoreFromTrash() {
    // Implementation for restoring from trash
    // This would show a dialog with deleted notes and allow restoration
}
